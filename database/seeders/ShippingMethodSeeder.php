<?php

namespace Database\Seeders;

use App\Models\Setting;
use App\Models\ShippingMethod;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;

class ShippingMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create shipping methods
        $methods = [
            [
                'code' => 'standard',
                'name' => 'Standard Shipping',
                'description' => 'Delivery in 3-5 business days',
                'method_type' => 'flat_rate',
                'display_order' => 1,
                'is_active' => true,
                'is_free_shipping_eligible' => false,
                'minimum_order_amount' => null,
                'maximum_order_amount' => null,
            ],
            [
                'code' => 'express',
                'name' => 'Express Shipping',
                'description' => 'Delivery in 1-2 business days',
                'method_type' => 'flat_rate',
                'display_order' => 2,
                'is_active' => true,
                'is_free_shipping_eligible' => false,
                'minimum_order_amount' => null,
                'maximum_order_amount' => null,
            ],
            [
                'code' => 'free',
                'name' => 'Free Shipping',
                'description' => 'Delivery in 5-7 business days',
                'method_type' => 'price_based',
                'display_order' => 3,
                'is_active' => true,
                'is_free_shipping_eligible' => true,
                'minimum_order_amount' => 50,
                'maximum_order_amount' => null,
            ],
            [
                'code' => 'weight_based',
                'name' => 'Weight-Based Shipping',
                'description' => 'Shipping cost based on weight',
                'method_type' => 'weight_based',
                'display_order' => 4,
                'is_active' => true,
                'is_free_shipping_eligible' => false,
                'minimum_order_amount' => null,
                'maximum_order_amount' => null,
            ],
            [
                'code' => 'local_pickup',
                'name' => 'Local Pickup',
                'description' => 'Pick up your order at our store',
                'method_type' => 'flat_rate',
                'display_order' => 5,
                'is_active' => true,
                'is_free_shipping_eligible' => false,
                'minimum_order_amount' => null,
                'maximum_order_amount' => null,
            ],
            [
                'code' => 'international',
                'name' => 'International Shipping',
                'description' => 'Delivery in 7-14 business days',
                'method_type' => 'flat_rate',
                'display_order' => 6,
                'is_active' => true,
                'is_free_shipping_eligible' => false,
                'minimum_order_amount' => null,
                'maximum_order_amount' => null,
            ],
        ];

        foreach ($methods as $method) {
            ShippingMethod::create($method);
        }

        // Update only the free shipping settings in the settings table
        // Get or create shipping settings
        $setting = Setting::where('key', 'shipping_settings')->first();

        if (!$setting) {
            $setting = new Setting([
                'id' => (string) Str::uuid(),
                'key' => 'shipping_settings',
                'category' => 'shipping',
                'type' => 'json',
                'is_public' => true,
            ]);
        }

        // Update settings with only the free shipping threshold
        // The shipping methods are now stored in the shipping_methods table
        $setting->value = json_encode([
            'freeShipping' => true,
            'freeShippingAmount' => 50,
        ]);

        $setting->save();
    }
}
