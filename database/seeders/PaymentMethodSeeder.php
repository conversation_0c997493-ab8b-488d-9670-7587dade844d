<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define payment methods
        $paymentMethods = [
            'stripe' => [
                'name' => 'Credit Card (Stripe)',
                'description' => 'Pay securely with your credit card via Stripe',
                'is_active' => true,
                'icon' => 'fa-credit-card',
                'display_order' => 1,
            ],
            'paypal' => [
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account',
                'is_active' => true,
                'icon' => 'fa-paypal',
                'display_order' => 2,
            ],
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Pay directly to our bank account',
                'is_active' => true,
                'icon' => 'fa-university',
                'display_order' => 3,
                'instructions' => "Please transfer the total amount to the following bank account:\n\nBank: Example Bank\nAccount Name: Example Store\nAccount Number: **********\nRouting Number: *********\n\nPlease include your order number as the payment reference.",
            ],
            'cash_on_delivery' => [
                'name' => 'Cash on Delivery',
                'description' => 'Pay when you receive your order',
                'is_active' => true,
                'icon' => 'fa-money-bill',
                'display_order' => 4,
            ],
        ];
        
        // Save payment methods using the Setting model
        Setting::setValue(
            key: 'payment_methods',
            value: $paymentMethods,
            category: 'payment',
            type: 'array',
            isPublic: true
        );
    }
}
