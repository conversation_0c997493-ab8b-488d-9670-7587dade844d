<?php

namespace Database\Seeders;

use App\Models\ContentBlock;
use App\Models\Language;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class ContentBlockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the English language ID
        $englishLanguage = Language::where('code', 'en')->first();

        if (!$englishLanguage) {
            Log::error('English language not found. Please run the LanguageSeeder first.');
            return;
        }

        // Home Page Content
        $this->seedContentBlock('home.hero.title1', 'Transform Your Business', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.hero.animatedTitleSequence', json_encode(["Transform Your Business", "Innovate Your Solutions", "Accelerate Your Growth"]), 'JSONB', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.hero.subtitle', 'with Advanced IT Solutions', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.hero.description', 'Leverage our expertise in development, security, and innovation to drive your business forward. We provide comprehensive IT services tailored to your needs.', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.hero.button1.text', 'Get Started', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.hero.button2.text', 'Learn more', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.features.title', 'Why Choose Us', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.features.subtitle', 'We combine technical expertise with business insight to deliver solutions that drive real results.', 'TEXT', 'home', $englishLanguage->id);

        // Feature cards
        $this->seedContentBlock('home.features.card1.title', 'Secure Solutions', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.features.card1.description', 'We prioritize security in all our solutions, ensuring your data and systems are protected against threats.', 'TEXT', 'home', $englishLanguage->id);

        $this->seedContentBlock('home.features.card2.title', 'Fast Performance', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.features.card2.description', 'Our solutions are optimized for speed and efficiency, ensuring your business operates at peak performance.', 'TEXT', 'home', $englishLanguage->id);

        $this->seedContentBlock('home.features.card3.title', 'Expert Support', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.features.card3.description', 'Our team of experts is always available to provide support and guidance whenever you need it.', 'TEXT', 'home', $englishLanguage->id);

        $this->seedContentBlock('home.cta.title', 'Need IT Support?', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.cta.description', 'Get in touch with our experts for immediate assistance with your IT needs. We\'re here to help you optimize and secure your technology infrastructure.', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.cta.button1.text', 'Contact Us Now', 'TEXT', 'home', $englishLanguage->id);
        $this->seedContentBlock('home.cta.button2.text', 'Call Us', 'TEXT', 'home', $englishLanguage->id);

        // About Page Content
        $this->seedContentBlock('about.hero.title1', 'About WisdomTechno', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.hero.title2', 'Transforming Technology Services Since 2010', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.hero.description', 'Empowering businesses through innovative IT solutions and exceptional service', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.mission.title', 'Our Mission', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.mission.description', 'At WisdomTechno, we\'re dedicated to providing cutting-edge IT solutions that empower businesses to thrive in the digital age. Our commitment to excellence and innovation drives us to deliver exceptional results for our clients.', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.mission.stat1.label', 'Years Experience', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.mission.stat2.label', 'Satisfied Clients', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.mission.stat3.label', 'Success Rate', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.mission.stat4.label', 'Support', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.values.title', 'Our Core Values', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.quality', 'Quality', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.integrity', 'Integrity', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.innovation', 'Innovation', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.sustainability', 'Sustainability', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.team.feature2.description', 'Working together to solve complex challenges', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.team.feature3.title', 'Continuous Learning', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.team.feature3.description', 'Regular training and skill development', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.team.stat1.label', 'Team Members', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.team.stat2.label', 'Certifications', 'TEXT', 'about', $englishLanguage->id);
        $this->seedContentBlock('about.team.stat3.label', 'Specializations', 'TEXT', 'about', $englishLanguage->id);

        // New About Page Content for our static pages
        $this->seedContentBlock('about_page_title', 'About WisdomTechno', 'TEXT', 'about_page', $englishLanguage->id);
        $this->seedContentBlock('about_page_content', '<p class="mb-4">WisdomTechno is a leading provider of innovative technology solutions for businesses of all sizes. Founded in 2010, we have been at the forefront of technological advancement, helping our clients navigate the ever-changing digital landscape.</p>

            <h2 class="text-2xl font-bold mb-4">Our Mission</h2>
            <p class="mb-6">Our mission is to empower businesses through technology. We believe that the right technology solutions can transform organizations, making them more efficient, competitive, and successful in today\'s digital world.</p>

            <h2 class="text-2xl font-bold mb-4">Our Vision</h2>
            <p class="mb-6">We envision a world where businesses of all sizes can harness the power of technology to achieve their goals. We strive to be the trusted partner that helps them get there.</p>

            <h2 class="text-2xl font-bold mb-4">Our Values</h2>
            <ul class="list-disc pl-6 mb-6">
                <li class="mb-2"><strong>Innovation:</strong> We constantly seek new and better ways to solve problems.</li>
                <li class="mb-2"><strong>Excellence:</strong> We are committed to delivering the highest quality in everything we do.</li>
                <li class="mb-2"><strong>Integrity:</strong> We operate with honesty, transparency, and ethical standards.</li>
                <li class="mb-2"><strong>Customer Focus:</strong> We put our clients\' needs at the center of our decisions.</li>
                <li class="mb-2"><strong>Collaboration:</strong> We believe in the power of teamwork and partnership.</li>
            </ul>

            <h2 class="text-2xl font-bold mb-4">Our Approach</h2>
            <p class="mb-6">At WisdomTechno, we take a consultative approach to understanding your business challenges and objectives. We then leverage our expertise to design and implement solutions that address your specific needs. Our process includes:</p>

            <ol class="list-decimal pl-6 mb-6">
                <li class="mb-2"><strong>Discovery:</strong> We take the time to understand your business, goals, and challenges.</li>
                <li class="mb-2"><strong>Strategy:</strong> We develop a tailored strategy that aligns technology with your business objectives.</li>
                <li class="mb-2"><strong>Implementation:</strong> We execute the strategy with precision and attention to detail.</li>
                <li class="mb-2"><strong>Support:</strong> We provide ongoing support to ensure your technology continues to serve your needs.</li>
            </ol>', 'HTML', 'about_page', $englishLanguage->id);

        // Contact Page Content
        $this->seedContentBlock('contact.hero.title1', 'Get in Touch', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.hero.title2', 'We\'re here to help you succeed', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.hero.description', 'Connect with our expert team for personalized IT solutions and support', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.info.title', 'Contact Information', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.info.email.href', 'mailto:<EMAIL>', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.info.address.label', 'Address', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.info.address.value', '123 Repair Street, 75000 Paris', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.info.address.href', 'https://maps.google.com/?q=123+Repair+Street+75000+Paris', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.social.title', 'Follow Us', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.social.linkedin.href', 'https://www.linkedin.com/company/wisdomtechno', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.social.twitter.href', 'https://twitter.com/wisdomtechno', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.social.whatsapp.href', 'https://wa.me/1234567890', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.social.youtube.href', 'https://www.youtube.com/@wisdomtechno', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.title', 'Send us a message', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.description', 'Fill out the form below and we\'ll get back to you as soon as possible.', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.name.label', 'Name', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.name.placeholder', 'Your name', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.email.label', 'Email', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.email.placeholder', '<EMAIL>', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.message.label', 'Message', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.message.placeholder', 'Your message', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.submit.label', 'Send', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.submit.loading', 'Sending...', 'TEXT', 'contact', $englishLanguage->id);
        $this->seedContentBlock('contact.form.submit.success', 'Sent!', 'TEXT', 'contact', $englishLanguage->id);

        // New Contact Page Content for our static pages
        $this->seedContentBlock('contact_page_title', 'Contact Us', 'TEXT', 'contact_page', $englishLanguage->id);
        $this->seedContentBlock('contact_page_content', '<p class="mb-6">We\'d love to hear from you! Whether you have a question about our services, need technical support, or want to discuss a potential project, our team is ready to assist you.</p>

            <p class="mb-6">Fill out the form below, and we\'ll get back to you as soon as possible. Alternatively, you can reach us directly using the contact information provided.</p>', 'HTML', 'contact_page', $englishLanguage->id);

        // Contact information blocks
        $this->seedContentBlock('headquarters', '<p><strong>Address:</strong><br>
            123 Tech Street<br>
            San Francisco, CA 94103<br>
            United States</p>

            <p class="mt-2"><strong>Phone:</strong> +****************</p>
            <p class="mt-2"><strong>Email:</strong> <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></p>

            <p class="mt-2"><strong>Hours:</strong><br>
            Monday - Friday: 9am - 5pm PST<br>
            Saturday - Sunday: Closed</p>', 'HTML', 'contact_info', $englishLanguage->id);

        $this->seedContentBlock('support', '<p><strong>Technical Support:</strong><br>
            <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></p>

            <p class="mt-2"><strong>Support Phone:</strong> +****************</p>

            <p class="mt-2"><strong>Support Hours:</strong><br>
            Monday - Friday: 8am - 8pm PST<br>
            Saturday: 10am - 4pm PST<br>
            Sunday: Closed</p>', 'HTML', 'contact_info', $englishLanguage->id);

        // Privacy and Terms pages
        $this->seedContentBlock('privacy_page_title', 'Privacy Policy', 'TEXT', 'privacy_page', $englishLanguage->id);
        $this->seedContentBlock('terms_page_title', 'Terms and Conditions', 'TEXT', 'terms_page', $englishLanguage->id);

        // Privacy Policy content
        $this->seedContentBlock('privacy_page_content', '<div class="text-gray-700 dark:text-gray-300">
            <p class="mb-4 text-sm text-gray-500 dark:text-gray-400">Last updated: April 24, 2025</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">1. Introduction</h2>
            <p class="mb-4">At WisdomTechno, we respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">2. The Data We Collect About You</h2>
            <p class="mb-4">Personal data, or personal information, means any information about an individual from which that person can be identified. We may collect, use, store and transfer different kinds of personal data about you which we have grouped together as follows:</p>
            <ul class="list-disc pl-6 mb-6 space-y-2">
                <li><strong>Identity Data</strong> includes first name, last name, username or similar identifier.</li>
                <li><strong>Contact Data</strong> includes billing address, delivery address, email address and telephone numbers.</li>
                <li><strong>Technical Data</strong> includes internet protocol (IP) address, your login data, browser type and version, time zone setting and location, browser plug-in types and versions, operating system and platform, and other technology on the devices you use to access this website.</li>
                <li><strong>Usage Data</strong> includes information about how you use our website, products and services.</li>
            </ul>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">3. How We Use Your Personal Data</h2>
            <p class="mb-4">We will only use your personal data when the law allows us to. Most commonly, we will use your personal data in the following circumstances:</p>
            <ul class="list-disc pl-6 mb-6 space-y-2">
                <li>Where we need to perform the contract we are about to enter into or have entered into with you.</li>
                <li>Where it is necessary for our legitimate interests (or those of a third party) and your interests and fundamental rights do not override those interests.</li>
                <li>Where we need to comply with a legal obligation.</li>
            </ul>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">4. Data Security</h2>
            <p class="mb-4">We have put in place appropriate security measures to prevent your personal data from being accidentally lost, used or accessed in an unauthorized way, altered or disclosed. In addition, we limit access to your personal data to those employees, agents, contractors and other third parties who have a business need to know.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">5. Data Retention</h2>
            <p class="mb-4">We will only retain your personal data for as long as reasonably necessary to fulfill the purposes we collected it for, including for the purposes of satisfying any legal, regulatory, tax, accounting or reporting requirements.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">6. Your Legal Rights</h2>
            <p class="mb-4">Under certain circumstances, you have rights under data protection laws in relation to your personal data, including the right to:</p>
            <ul class="list-disc pl-6 mb-6 space-y-2">
                <li>Request access to your personal data.</li>
                <li>Request correction of your personal data.</li>
                <li>Request erasure of your personal data.</li>
                <li>Object to processing of your personal data.</li>
                <li>Request restriction of processing your personal data.</li>
                <li>Request transfer of your personal data.</li>
                <li>Right to withdraw consent.</li>
            </ul>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">7. Cookies</h2>
            <p class="mb-4">You can set your browser to refuse all or some browser cookies, or to alert you when websites set or access cookies. If you disable or refuse cookies, please note that some parts of this website may become inaccessible or not function properly.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">8. Changes to the Privacy Policy</h2>
            <p class="mb-4">We may update our privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the "Last updated" date at the top of this privacy policy.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">9. Contact Us</h2>
            <p class="mb-4">If you have any questions about this privacy policy or our privacy practices, please contact us at:</p>
            <div class="mb-6">
                <p>Email: <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"><EMAIL></a><br>
                Phone: +****************<br>
                Address: 123 Tech Street, San Francisco, CA 94103, United States</p>
            </div>
        </div>', 'HTML', 'privacy_page', $englishLanguage->id);

        // Terms and Conditions content
        $this->seedContentBlock('terms_page_content', '<div class="text-gray-700 dark:text-gray-300">
            <p class="mb-4 text-sm text-gray-500 dark:text-gray-400">Last updated: April 24, 2025</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">1. Introduction</h2>
            <p class="mb-4">Welcome to WisdomTechno. These Terms and Conditions govern your use of our website and services. By accessing or using our website, you agree to be bound by these Terms and Conditions.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">2. Definitions</h2>
            <p class="mb-4">In these Terms and Conditions:</p>
            <ul class="list-disc pl-6 mb-6 space-y-2">
                <li>"We," "our," or "us" refers to WisdomTechno.</li>
                <li>"You" or "your" refers to the user or visitor of our website.</li>
                <li>"Website" refers to wisdomtechno.com and all its subdomains.</li>
                <li>"Services" refers to all products, services, and content offered by WisdomTechno.</li>
            </ul>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">3. Account Registration</h2>
            <p class="mb-4">To access certain features of our website, you may need to register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">4. User Responsibilities</h2>
            <p class="mb-4">You are responsible for:</p>
            <ul class="list-disc pl-6 mb-6 space-y-2">
                <li>Maintaining the confidentiality of your account credentials</li>
                <li>All activities that occur under your account</li>
                <li>Ensuring that your use of our services complies with all applicable laws and regulations</li>
                <li>Notifying us immediately of any unauthorized use of your account</li>
            </ul>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">5. Intellectual Property</h2>
            <p class="mb-4">All content on our website, including text, graphics, logos, images, and software, is the property of WisdomTechno or its content suppliers and is protected by copyright, trademark, and other intellectual property laws.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">6. Product Information and Pricing</h2>
            <p class="mb-4">We strive to provide accurate product information and pricing. However, we do not warrant that product descriptions or prices are accurate, complete, reliable, current, or error-free. If a product is not as described, your sole remedy is to return it in unused condition.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">7. Orders and Payments</h2>
            <p class="mb-4">When you place an order, you offer to purchase the product at the price and terms indicated. We reserve the right to accept or decline your order for any reason. Payment must be made at the time of order.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">8. Shipping and Delivery</h2>
            <p class="mb-4">Shipping and delivery times are estimates only and are not guaranteed. We are not responsible for delays caused by factors beyond our control.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">9. Returns and Refunds</h2>
            <p class="mb-4">Our return and refund policy is outlined separately. By making a purchase, you agree to be bound by our return and refund policy.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">10. Limitation of Liability</h2>
            <p class="mb-4">To the maximum extent permitted by law, WisdomTechno shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">11. Indemnification</h2>
            <p class="mb-4">You agree to indemnify, defend, and hold harmless WisdomTechno and its officers, directors, employees, agents, and suppliers from and against all claims, losses, expenses, damages, and costs arising from your use of our website or any violation of these Terms and Conditions.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">12. Governing Law</h2>
            <p class="mb-4">These Terms and Conditions shall be governed by and construed in accordance with the laws of the State of California, without regard to its conflict of law provisions.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">13. Changes to Terms and Conditions</h2>
            <p class="mb-4">We reserve the right to modify these Terms and Conditions at any time. Changes will be effective immediately upon posting on our website. Your continued use of our website following the posting of changes constitutes your acceptance of such changes.</p>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">14. Contact Us</h2>
            <p class="mb-4">If you have any questions about these Terms and Conditions, please contact us at:</p>
            <div class="mb-6">
                <p>Email: <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"><EMAIL></a><br>
                Phone: +****************<br>
                Address: 123 Tech Street, San Francisco, CA 94103, United States</p>
            </div>
        </div>', 'HTML', 'terms_page', $englishLanguage->id);

        // Services Page Content
        $this->seedContentBlock('services.hero.title1', 'Our Services', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.hero.title2', 'Comprehensive IT Solutions for Your Business', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.hero.description', 'Discover our complete range of technology services, designed to help your business thrive in the digital age with cutting-edge solutions and expert support.', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.grid.learnMore', 'Learn more', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.cta.title', 'Ready to Transform Your Business?', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.cta.description', 'Contact us today to discuss how our services can help you achieve your goals.', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.cta.button1.text', 'Contact Us', 'TEXT', 'services', $englishLanguage->id);
        $this->seedContentBlock('services.cta.button2.text', 'Request Quote', 'TEXT', 'services', $englishLanguage->id);

        // Inquiry Page Content
        $this->seedContentBlock('inquiry.hero.title1', 'Submit an Inquiry', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.hero.title2', 'Get personalized solutions tailored to your needs', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.hero.description', 'Let us help you transform your IT infrastructure with our expert solutions', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.title', 'Inquiry Form', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.name.label', 'Name', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.name.placeholder', 'Your name', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.email.label', 'Email', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.email.placeholder', '<EMAIL>', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.service.label', 'Service Type', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.service.placeholder', 'Select a service', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.message.label', 'Message', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.message.placeholder', 'Describe your IT needs or technical requirements...', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.attachments.label', 'Attachments', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.attachments.dropzone', 'Drag and drop files here, or click to select files', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.attachments.formats', 'Supported formats: JPG, PNG, GIF, PDF, TXT (max 10MB)', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.submit.label', 'Submit Inquiry', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.form.submit.loading', 'Submitting...', 'TEXT', 'inquiry', $englishLanguage->id);
        $this->seedContentBlock('inquiry.info.timezone.prefix', 'Your current timezone:', 'TEXT', 'inquiry', $englishLanguage->id);
    }

    /**
     * Seed a content block with translations.
     */
    private function seedContentBlock(string $key, string $content, string $contentType, string $location, string $languageId): void
    {
        // Create or update the content block
        $contentBlock = ContentBlock::updateOrCreate(
            ['key' => $key],
            [
                'location' => $location,
                'is_active' => true,
            ]
        );

        // Set the translation
        if ($contentType === 'JSONB') {
            $contentBlock->setTranslation('content', 'en', json_decode($content));
        } else {
            $contentBlock->setTranslation('content', 'en', $content);
        }

        // Set a default title based on the key
        $title = ucwords(str_replace(['.', '_'], ' ', explode('.', $key)[count(explode('.', $key)) - 1]));
        $contentBlock->setTranslation('title', 'en', $title);

        $contentBlock->save();
    }
}
