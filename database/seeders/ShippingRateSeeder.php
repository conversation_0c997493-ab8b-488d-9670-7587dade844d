<?php

namespace Database\Seeders;

use App\Models\ShippingMethod;
use App\Models\ShippingRate;
use App\Models\ShippingZone;
use Illuminate\Database\Seeder;

class ShippingRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get shipping zones
        $domesticZone = ShippingZone::where('name', 'Domestic')->first();
        $canadaZone = ShippingZone::where('name', 'Canada')->first();
        $europeZone = ShippingZone::where('name', 'Europe')->first();
        $restOfWorldZone = ShippingZone::where('name', 'Rest of World')->first();
        
        // Get shipping methods
        $standardMethod = ShippingMethod::where('code', 'standard')->first();
        $expressMethod = ShippingMethod::where('code', 'express')->first();
        $freeMethod = ShippingMethod::where('code', 'free')->first();
        $weightBasedMethod = ShippingMethod::where('code', 'weight_based')->first();
        $localPickupMethod = ShippingMethod::where('code', 'local_pickup')->first();
        $internationalMethod = ShippingMethod::where('code', 'international')->first();
        
        // Create shipping rates
        $rates = [
            // Domestic rates
            [
                'shipping_zone_id' => $domesticZone->id,
                'shipping_method_id' => $standardMethod->id,
                'base_rate' => 5.99,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            [
                'shipping_zone_id' => $domesticZone->id,
                'shipping_method_id' => $expressMethod->id,
                'base_rate' => 12.99,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            [
                'shipping_zone_id' => $domesticZone->id,
                'shipping_method_id' => $freeMethod->id,
                'base_rate' => 0,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => 50,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            [
                'shipping_zone_id' => $domesticZone->id,
                'shipping_method_id' => $weightBasedMethod->id,
                'base_rate' => 3.99,
                'per_item_rate' => null,
                'per_weight_rate' => 1.50,
                'weight_unit' => 'kg',
                'min_weight' => 0,
                'max_weight' => 20,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            [
                'shipping_zone_id' => $domesticZone->id,
                'shipping_method_id' => $localPickupMethod->id,
                'base_rate' => 0,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            
            // Canada rates
            [
                'shipping_zone_id' => $canadaZone->id,
                'shipping_method_id' => $standardMethod->id,
                'base_rate' => 14.99,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            [
                'shipping_zone_id' => $canadaZone->id,
                'shipping_method_id' => $expressMethod->id,
                'base_rate' => 24.99,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            [
                'shipping_zone_id' => $canadaZone->id,
                'shipping_method_id' => $weightBasedMethod->id,
                'base_rate' => 9.99,
                'per_item_rate' => null,
                'per_weight_rate' => 2.50,
                'weight_unit' => 'kg',
                'min_weight' => 0,
                'max_weight' => 20,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            
            // Europe rates
            [
                'shipping_zone_id' => $europeZone->id,
                'shipping_method_id' => $internationalMethod->id,
                'base_rate' => 24.99,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
            
            // Rest of World rates
            [
                'shipping_zone_id' => $restOfWorldZone->id,
                'shipping_method_id' => $internationalMethod->id,
                'base_rate' => 34.99,
                'per_item_rate' => null,
                'per_weight_rate' => null,
                'weight_unit' => 'kg',
                'min_weight' => null,
                'max_weight' => null,
                'min_price' => null,
                'max_price' => null,
                'conditions' => null,
                'is_active' => true,
            ],
        ];
        
        foreach ($rates as $rate) {
            ShippingRate::create($rate);
        }
    }
}
