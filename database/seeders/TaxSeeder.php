<?php

namespace Database\Seeders;

use App\Models\TaxRate;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing tax rates
        DB::table('tax_rates')->truncate();
        
        // Create default tax rates
        $taxRates = [
            // Global Default
            [
                'name' => 'Global Default Rate',
                'country' => 'ZZ',
                'region' => null,
                'rate' => 10.0,
                'is_default' => true,
                'is_active' => true,
                'priority' => 0,
                'is_compound' => false,
            ],
            
            // United States
            [
                'name' => 'California Tax',
                'country' => 'US',
                'region' => 'CA',
                'rate' => 7.25,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'New York Tax',
                'country' => 'US',
                'region' => 'NY',
                'rate' => 8.875,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            
            // European Union (VAT) - All Member States
            [
                'name' => 'Austria VAT (Standard)',
                'country' => 'AT',
                'rate' => 20.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Belgium VAT (Standard)',
                'country' => 'BE',
                'rate' => 21.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Bulgaria VAT (Standard)',
                'country' => 'BG',
                'rate' => 20.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Croatia VAT (Standard)',
                'country' => 'HR',
                'rate' => 25.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Cyprus VAT (Standard)',
                'country' => 'CY',
                'rate' => 19.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Czech Republic VAT (Standard)',
                'country' => 'CZ',
                'rate' => 21.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Denmark VAT (Standard)',
                'country' => 'DK',
                'rate' => 25.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Estonia VAT (Standard)',
                'country' => 'EE',
                'rate' => 20.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Finland VAT (Standard)',
                'country' => 'FI',
                'rate' => 24.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'France VAT (Standard)',
                'country' => 'FR',
                'rate' => 20.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Germany VAT (Standard)',
                'country' => 'DE',
                'rate' => 19.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Greece VAT (Standard)',
                'country' => 'GR',
                'rate' => 24.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Hungary VAT (Standard)',
                'country' => 'HU',
                'rate' => 27.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Ireland VAT (Standard)',
                'country' => 'IE',
                'rate' => 23.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Italy VAT (Standard)',
                'country' => 'IT',
                'rate' => 22.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Latvia VAT (Standard)',
                'country' => 'LV',
                'rate' => 21.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Lithuania VAT (Standard)',
                'country' => 'LT',
                'rate' => 21.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Luxembourg VAT (Standard)',
                'country' => 'LU',
                'rate' => 17.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Malta VAT (Standard)',
                'country' => 'MT',
                'rate' => 18.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Netherlands VAT (Standard)',
                'country' => 'NL',
                'rate' => 21.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Poland VAT (Standard)',
                'country' => 'PL',
                'rate' => 23.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Portugal VAT (Standard)',
                'country' => 'PT',
                'rate' => 23.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Romania VAT (Standard)',
                'country' => 'RO',
                'rate' => 19.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Slovakia VAT (Standard)',
                'country' => 'SK',
                'rate' => 20.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Slovenia VAT (Standard)',
                'country' => 'SI',
                'rate' => 22.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Spain VAT (Standard)',
                'country' => 'ES',
                'rate' => 21.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Sweden VAT (Standard)',
                'country' => 'SE',
                'rate' => 25.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            
            // United Kingdom
            [
                'name' => 'UK VAT (Standard)',
                'country' => 'GB',
                'region' => null,
                'rate' => 20.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            
            // Canada
            [
                'name' => 'Canada GST/HST',
                'country' => 'CA',
                'region' => null,
                'rate' => 5.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Ontario HST',
                'country' => 'CA',
                'region' => 'ON',
                'rate' => 13.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 2,
                'is_compound' => false,
            ],
            
            // Asia
            [
                'name' => 'Japan Consumption Tax',
                'country' => 'JP',
                'region' => null,
                'rate' => 10.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => false,
            ],
            [
                'name' => 'Australia GST',
                'country' => 'AU',
                'region' => null,
                'rate' => 10.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => true,
            ],
            
            // Other Major Economies
            [
                'name' => 'India GST (Standard)',
                'country' => 'IN',
                'region' => null,
                'rate' => 18.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => true,
            ],
            [
                'name' => 'Brazil ICMS (SP)',
                'country' => 'BR',
                'region' => 'SP',
                'rate' => 17.0,
                'is_default' => false,
                'is_active' => true,
                'priority' => 1,
                'is_compound' => true,
            ],
        ];
        
        foreach ($taxRates as $rate) {
            TaxRate::create($rate);
        }
    }
}