<?php

namespace Database\Seeders;

use App\Models\ShippingZone;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ShippingZoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create shipping zones
        $zones = [
            [
                'name' => 'Domestic',
                'description' => 'Shipping within the United States',
                'countries' => ['US'],
                'regions' => null,
                'postal_codes' => null,
                'is_catch_all' => false,
                'display_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Canada',
                'description' => 'Shipping to Canada',
                'countries' => ['CA'],
                'regions' => null,
                'postal_codes' => null,
                'is_catch_all' => false,
                'display_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Europe',
                'description' => 'Shipping to European countries',
                'countries' => ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'PT', 'IE', 'GR'],
                'regions' => null,
                'postal_codes' => null,
                'is_catch_all' => false,
                'display_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Rest of World',
                'description' => 'Shipping to all countries not covered by other zones',
                'countries' => null, // Null for catch-all zones
                'regions' => null,
                'postal_codes' => null,
                'is_catch_all' => true,
                'display_order' => 999, // High display order to ensure it's checked last
                'is_active' => true,
            ],
        ];

        foreach ($zones as $zone) {
            ShippingZone::create($zone);
        }
    }
}
