<?php

namespace Database\Seeders;

use App\Models\ShippingZone;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ShippingZoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create shipping zones
        $zones = [
            [
                'name' => 'Domestic',
                'description' => 'Shipping within the United States',
                'countries' => ['US'],
                'regions' => null,
                'postal_codes' => null,
                'display_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Canada',
                'description' => 'Shipping to Canada',
                'countries' => ['CA'],
                'regions' => null,
                'postal_codes' => null,
                'display_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Europe',
                'description' => 'Shipping to European countries',
                'countries' => ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'PT', 'IE', 'GR'],
                'regions' => null,
                'postal_codes' => null,
                'display_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Rest of World',
                'description' => 'Shipping to all other countries',
                'countries' => ['AU', 'NZ', 'JP', 'CN', 'IN', 'BR', 'MX', 'ZA', 'SG', 'AE', 'SA', 'RU'],
                'regions' => null,
                'postal_codes' => null,
                'display_order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($zones as $zone) {
            ShippingZone::create($zone);
        }
    }
}
