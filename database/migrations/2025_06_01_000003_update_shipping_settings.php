<?php

use App\Models\Setting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the shipping settings
        $setting = Setting::where('key', 'shipping_settings')->first();
        
        if ($setting) {
            $shippingSettings = json_decode($setting->value, true);
            
            // Remove the shipping methods from the settings
            // as they are now stored in the shipping_methods table
            if (isset($shippingSettings['shippingMethods'])) {
                unset($shippingSettings['shippingMethods']);
            }
            
            // Keep only the free shipping threshold settings
            $updatedSettings = [
                'freeShipping' => $shippingSettings['freeShipping'] ?? true,
                'freeShippingAmount' => $shippingSettings['freeShippingAmount'] ?? 50,
            ];
            
            // Update the settings
            $setting->value = json_encode($updatedSettings);
            $setting->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration removes data, so there's no way to restore it
        // We could potentially recreate the shipping methods in the settings
        // based on the database, but that's not necessary for this migration
    }
};
