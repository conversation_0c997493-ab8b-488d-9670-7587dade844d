<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddDefaultTaxRate extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        try {
            // Check if we already have a default tax rate
            $exists = DB::table('tax_rates')
                ->where('is_default', true)
                ->exists();

            if (!$exists) {
                DB::table('tax_rates')->insert([
                    'name' => 'Global Default',
                    'country' => 'ZZ', // Special country code for global default
                    'rate' => 0, // 0% tax by default - should be configured in admin
                    'is_default' => true,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                Log::info('Default global tax rate created');
            }
        } catch (\Exception $e) {
            Log::error('Failed to create default tax rate: ' . $e->getMessage());
            // Don't fail the migration if we can't create the default rate
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Don't remove the default tax rate as it might be in use
    }
}
