<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create a new table with UUID primary key
        Schema::create('settings_new', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('key')->nullable();
            $table->text('value')->nullable();
            $table->string('category')->default('general');
            $table->string('type')->default('text');
            $table->json('options')->nullable();
            $table->boolean('is_public')->default(true);
            $table->timestamps();
        });

        // Copy data from old table to new table
        $settings = \DB::table('settings')->get();
        foreach ($settings as $setting) {
            \DB::table('settings_new')->insert([
                'id' => (string) \Illuminate\Support\Str::uuid(),
                'key' => $setting->key,
                'value' => $setting->value,
                'category' => $setting->category,
                'type' => $setting->type,
                'options' => $setting->options,
                'is_public' => $setting->is_public,
                'created_at' => $setting->created_at,
                'updated_at' => $setting->updated_at,
            ]);
        }

        // Drop old table and rename new one
        Schema::drop('settings');
        Schema::rename('settings_new', 'settings');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to auto-incrementing ID
        Schema::create('settings_old', function (Blueprint $table) {
            $table->id();
            $table->string('key')->nullable();
            $table->text('value')->nullable();
            $table->string('category')->default('general');
            $table->string('type')->default('text');
            $table->json('options')->nullable();
            $table->boolean('is_public')->default(true);
            $table->timestamps();
        });

        // Copy data back to old table format
        $settings = \DB::table('settings')->get();
        $id = 1;
        foreach ($settings as $setting) {
            \DB::table('settings_old')->insert([
                'id' => $id++,
                'key' => $setting->key,
                'value' => $setting->value,
                'category' => $setting->category,
                'type' => $setting->type,
                'options' => $setting->options,
                'is_public' => $setting->is_public,
                'created_at' => $setting->created_at,
                'updated_at' => $setting->updated_at,
            ]);
        }

        // Drop new table and rename old one
        Schema::drop('settings');
        Schema::rename('settings_old', 'settings');
    }
};
