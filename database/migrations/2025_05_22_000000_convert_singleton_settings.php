<?php

use App\Models\Setting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all settings that are using the singleton pattern (has options but no key)
        $settings = DB::table('settings')
            ->whereNull('key')
            ->whereNotNull('options')
            ->get();

        foreach ($settings as $setting) {
            $options = json_decode($setting->options, true);
            
            if (!is_array($options)) {
                continue;
            }
            
            // Convert each option to a key-value setting
            foreach ($options as $key => $value) {
                // Skip if the setting already exists
                if (Setting::where('key', $key)->exists()) {
                    continue;
                }
                
                // Create a new setting for each key-value pair
                Setting::setValue(
                    key: $key,
                    value: $value,
                    category: 'legacy',
                    type: $this->determineType($value)
                );
            }
            
            // Delete the old singleton settings row
            DB::table('settings')
                ->where('id', $setting->id)
                ->delete();
        }
    }

    /**
     * Determine the type of a value
     */
    private function determineType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        }
        if (is_int($value)) {
            return 'integer';
        }
        if (is_float($value)) {
            return 'float';
        }
        if (is_array($value)) {
            return 'array';
        }
        return 'string';
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a one-way migration
        // We can't reliably revert this without additional tracking
    }
};
