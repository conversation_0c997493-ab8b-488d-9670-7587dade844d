<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_zones', function (Blueprint $table) {
            if (!Schema::hasColumn('shipping_zones', 'is_catch_all')) {
                $table->boolean('is_catch_all')->default(false)->comment('Whether this zone applies to all countries not covered by other zones')->after('postal_codes');
                $table->index('is_catch_all');
            }
        });

        // Update the existing "Rest of World" zone to be a catch-all zone
        DB::table('shipping_zones')
            ->where('name', 'Rest of World')
            ->update([
                'is_catch_all' => true,
                'countries' => null, // Remove specific countries to make it truly catch-all
                'description' => 'Shipping to all countries not covered by other zones'
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_zones', function (Blueprint $table) {
            if (Schema::hasColumn('shipping_zones', 'is_catch_all')) {
                $table->dropIndex(['is_catch_all']);
                $table->dropColumn('is_catch_all');
            }
        });

        // Restore the original "Rest of World" zone configuration
        DB::table('shipping_zones')
            ->where('name', 'Rest of World')
            ->update([
                'countries' => json_encode(['AU', 'NZ', 'JP', 'CN', 'IN', 'BR', 'MX', 'ZA', 'SG', 'AE', 'SA', 'RU']),
                'description' => 'Shipping to all other countries'
            ]);
    }
};
