<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add is_default column if it doesn't exist
        if (!Schema::hasColumn('tax_rates', 'is_default')) {
            Schema::table('tax_rates', function (Blueprint $table) {
                $table->boolean('is_default')->default(false)->after('is_active');
            });
        }

        // Add indexes if they don't exist
        Schema::table('tax_rates', function (Blueprint $table) {
            if (!Schema::hasIndex('tax_rates', 'tax_rates_country_region_index')) {
                $table->index(['country', 'region'], 'tax_rates_country_region_index');
            }
            if (!Schema::hasIndex('tax_rates', 'tax_rates_is_default_index')) {
                $table->index('is_default', 'tax_rates_is_default_index');
            }
            if (!Schema::hasIndex('tax_rates', 'tax_rates_priority_index')) {
                $table->index('priority', 'tax_rates_priority_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't remove columns in production to prevent data loss
        if (!app()->environment('production')) {
            // Drop indexes
            Schema::table('tax_rates', function (Blueprint $table) {
                $table->dropIndexIfExists('tax_rates_country_region_index');
                $table->dropIndexIfExists('tax_rates_is_default_index');
                $table->dropIndexIfExists('tax_rates_priority_index');
            });

            // Drop column if it exists
            if (Schema::hasColumn('tax_rates', 'is_default')) {
                Schema::table('tax_rates', function (Blueprint $table) {
                    $table->dropColumn('is_default');
                });
            }
        }
    }
};
