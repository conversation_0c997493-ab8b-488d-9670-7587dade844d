<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_methods', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('code')->unique()->comment('Unique identifier for the shipping method');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('method_type')->default('flat_rate')->comment('flat_rate, weight_based, price_based, etc.');
            $table->integer('display_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_free_shipping_eligible')->default(false)->comment('Whether this method can be used for free shipping promotions');
            $table->decimal('minimum_order_amount', 10, 2)->nullable()->comment('Minimum order amount for this method to be available');
            $table->decimal('maximum_order_amount', 10, 2)->nullable()->comment('Maximum order amount for this method to be available');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index('code');
            $table->index('is_active');
            $table->index('method_type');
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_methods');
    }
};
