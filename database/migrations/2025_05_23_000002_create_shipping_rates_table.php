<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_rates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('shipping_zone_id');
            $table->uuid('shipping_method_id');
            $table->decimal('base_rate', 10, 2)->default(0)->comment('Base shipping rate');
            $table->decimal('per_item_rate', 10, 2)->nullable()->comment('Additional rate per item');
            $table->decimal('per_weight_rate', 10, 2)->nullable()->comment('Additional rate per weight unit');
            $table->string('weight_unit')->default('kg');
            $table->decimal('min_weight', 10, 2)->nullable()->comment('Minimum weight for this rate to apply');
            $table->decimal('max_weight', 10, 2)->nullable()->comment('Maximum weight for this rate to apply');
            $table->decimal('min_price', 10, 2)->nullable()->comment('Minimum order price for this rate to apply');
            $table->decimal('max_price', 10, 2)->nullable()->comment('Maximum order price for this rate to apply');
            $table->json('conditions')->nullable()->comment('Additional conditions for this rate');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('shipping_zone_id')
                ->references('id')
                ->on('shipping_zones')
                ->onDelete('cascade');
                
            $table->foreign('shipping_method_id')
                ->references('id')
                ->on('shipping_methods')
                ->onDelete('cascade');
                
            // Indexes
            $table->index('is_active');
            $table->unique(['shipping_zone_id', 'shipping_method_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_rates');
    }
};
