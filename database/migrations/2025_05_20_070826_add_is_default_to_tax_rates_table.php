<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tax_rates', function (Blueprint $table) {
            if (!Schema::hasColumn('tax_rates', 'is_default')) {
                $table->boolean('is_default')->default(false)->after('is_active');
                $table->index('is_default');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't remove in production to prevent data loss
        if (!app()->environment('production')) {
            Schema::table('tax_rates', function (Blueprint $table) {
                if (Schema::hasColumn('tax_rates', 'is_default')) {
                    $table->dropColumn('is_default');
                }
                $table->dropIndexIfExists('tax_rates_is_default_index');
            });
        }
    }
};
