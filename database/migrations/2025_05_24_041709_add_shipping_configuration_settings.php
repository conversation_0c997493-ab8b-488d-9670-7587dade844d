<?php

use App\Models\Setting;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add shipping configuration settings
        $shippingConfig = [
            'fallback_behavior' => 'catch_all', // Options: 'catch_all', 'block', 'contact_quote'
            'enable_dynamic_calculations' => true,
            'log_shipping_calculations' => true,
            'require_shipping_address' => true,
            'prioritize_specific_zones' => true,
            'default_fallback_methods' => [
                'standard' => [
                    'name' => 'Standard Shipping',
                    'description' => 'Delivery in 3-5 business days',
                    'price' => 5.99,
                    'method_type' => 'flat_rate'
                ],
                'express' => [
                    'name' => 'Express Shipping',
                    'description' => 'Delivery in 1-2 business days',
                    'price' => 12.99,
                    'method_type' => 'flat_rate'
                ]
            ]
        ];

        Setting::setValue(
            'shipping_configuration',
            $shippingConfig,
            'shipping',
            'json',
            [
                'fallback_behavior' => [
                    'catch_all' => 'Apply catch-all zone rates with dynamic calculations',
                    'block' => 'Block shipping to unconfigured destinations',
                    'contact_quote' => 'Show "Contact for quote" message'
                ]
            ],
            false // Not public - admin only
        );

        // Update existing shipping settings to include new configuration
        $existingSettings = Setting::getValue('shipping_settings', []);
        if (is_string($existingSettings)) {
            $existingSettings = json_decode($existingSettings, true) ?: [];
        }

        $updatedSettings = array_merge($existingSettings, [
            'freeShipping' => $existingSettings['freeShipping'] ?? true,
            'freeShippingAmount' => $existingSettings['freeShippingAmount'] ?? 50,
            'enableZoneHierarchy' => true,
            'prioritizeSpecificZones' => true
        ]);

        Setting::setValue(
            'shipping_settings',
            $updatedSettings,
            'shipping',
            'json',
            [],
            true // Public
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove shipping configuration settings
        Setting::where('key', 'shipping_configuration')->delete();

        // Restore original shipping settings
        $existingSettings = Setting::getValue('shipping_settings', []);
        if (is_string($existingSettings)) {
            $existingSettings = json_decode($existingSettings, true) ?: [];
        }

        $restoredSettings = [
            'freeShipping' => $existingSettings['freeShipping'] ?? true,
            'freeShippingAmount' => $existingSettings['freeShippingAmount'] ?? 50
        ];

        Setting::setValue(
            'shipping_settings',
            $restoredSettings,
            'shipping',
            'json',
            [],
            true
        );
    }
};
