<?php

namespace Database\Factories;

use App\Models\Address;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Address::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'address_line1' => $this->faker->streetAddress(),
            'address_line2' => $this->faker->boolean(30) ? $this->faker->secondaryAddress() : null,
            'city' => $this->faker->city(),
            'region' => $this->faker->state(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->countryCode(),
        ];
    }

    /**
     * Indicate that the address is in the United States.
     */
    public function unitedStates(): self
    {
        return $this->state(function () {
            return [
                'country' => 'US',
                'region' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
            ];
        });
    }

    /**
     * Indicate that the address is in the United Kingdom.
     */
    public function unitedKingdom(): self
    {
        return $this->state(function () {
            return [
                'country' => 'GB',
                'region' => $this->faker->county(),
                'postal_code' => $this->faker->postcode(),
            ];
        });
    }

    /**
     * Indicate that the address is in Canada.
     */
    public function canada(): self
    {
        return $this->state(function () {
            return [
                'country' => 'CA',
                'region' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
            ];
        });
    }
}
