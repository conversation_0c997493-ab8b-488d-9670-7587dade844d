<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserAddress>
 */
class UserAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'address_id' => Address::factory(),
            'is_default_shipping' => $this->faker->boolean(20),
            'is_default_billing' => $this->faker->boolean(20),
        ];
    }

    /**
     * Indicate that the address is the default shipping address.
     */
    public function defaultShipping(): self
    {
        return $this->state(function () {
            return [
                'is_default_shipping' => true,
            ];
        });
    }

    /**
     * Indicate that the address is the default billing address.
     */
    public function defaultBilling(): self
    {
        return $this->state(function () {
            return [
                'is_default_billing' => true,
            ];
        });
    }
}
