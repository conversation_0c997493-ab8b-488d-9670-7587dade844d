<?php

namespace Database\Factories;

use App\Models\ShippingMethod;
use App\Models\ShippingRate;
use App\Models\ShippingZone;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShippingRate>
 */
class ShippingRateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ShippingRate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'shipping_zone_id' => ShippingZone::factory(),
            'shipping_method_id' => ShippingMethod::factory(),
            'base_rate' => $this->faker->randomFloat(2, 0, 50),
            'per_item_rate' => $this->faker->optional(0.3)->randomFloat(2, 0, 10),
            'per_weight_rate' => $this->faker->optional(0.3)->randomFloat(2, 0, 5),
            'weight_unit' => $this->faker->randomElement(['kg', 'g', 'lb', 'oz']),
            'min_weight' => $this->faker->optional(0.3)->randomFloat(2, 0, 10),
            'max_weight' => $this->faker->optional(0.3)->randomFloat(2, 10, 50),
            'min_price' => $this->faker->optional(0.3)->randomFloat(2, 0, 50),
            'max_price' => $this->faker->optional(0.3)->randomFloat(2, 50, 500),
            'conditions' => null,
            'is_active' => true,
        ];
    }
}
