<?php

namespace Database\Factories;

use App\Models\ShippingMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShippingMethod>
 */
class ShippingMethodFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ShippingMethod::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->word(),
            'name' => $this->faker->words(2, true),
            'description' => $this->faker->sentence(),
            'method_type' => $this->faker->randomElement(['flat_rate', 'weight_based', 'price_based', 'item_based']),
            'display_order' => $this->faker->numberBetween(1, 10),
            'is_active' => true,
            'is_free_shipping_eligible' => $this->faker->boolean(20),
            'minimum_order_amount' => $this->faker->optional(0.3)->randomFloat(2, 10, 100),
            'maximum_order_amount' => $this->faker->optional(0.1)->randomFloat(2, 500, 1000),
        ];
    }
}
