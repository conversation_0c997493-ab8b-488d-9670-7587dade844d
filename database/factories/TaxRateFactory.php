<?php

namespace Database\Factories;

use App\Models\TaxRate;
use Illuminate\Database\Eloquent\Factories\Factory;

class TaxRateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = TaxRate::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->word,
            'country' => $this->faker->countryCode,
            'region' => $this->faker->stateAbbr,
            'rate' => $this->faker->randomFloat(2, 0, 25),
            'description' => $this->faker->sentence,
            'tax_category' => 'standard',
            'is_compound' => false,
            'priority' => 1,
            'is_active' => true,
            'is_default' => false,
            'valid_from' => null,
            'valid_until' => null,
        ];
    }

    /**
     * Indicate that the tax rate is the default.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function default()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => true,
            ];
        });
    }

    /**
     * Indicate that the tax rate is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}
