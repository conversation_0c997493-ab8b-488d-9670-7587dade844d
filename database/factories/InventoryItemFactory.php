<?php

namespace Database\Factories;

use App\Models\InventoryItem;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InventoryItem>
 */
class InventoryItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = InventoryItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_variant_id' => ProductVariant::factory(),
            'quantity_on_hand' => $this->faker->numberBetween(10, 100),
            'quantity_reserved' => 0,
            'track_inventory' => true,
            'allow_backorder' => false,
            'low_stock_threshold' => $this->faker->numberBetween(5, 20),
            'low_stock_notified' => false,
            'reorder_point' => $this->faker->numberBetween(5, 15),
            'reorder_quantity' => $this->faker->numberBetween(10, 30),
        ];
    }
    
    /**
     * Configure the model to not track inventory.
     */
    public function noTracking(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'track_inventory' => false,
            ];
        });
    }
    
    /**
     * Configure the model to allow backorders.
     */
    public function allowBackorders(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'allow_backorder' => true,
            ];
        });
    }
}
