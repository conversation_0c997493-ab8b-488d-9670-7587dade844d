<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);

        return [
            'category_id' => Category::factory(),
            'code' => 'PROD-' . Str::upper(Str::random(6)),
            'is_active' => true,
            'is_featured' => $this->faker->boolean(20),
            'name' => ['en' => $name],
            'description' => ['en' => $this->faker->paragraph()],
            'slug' => ['en' => Str::slug($name)],
        ];
    }
}
