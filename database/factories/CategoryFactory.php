<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(2, true);
        
        return [
            'code' => 'CAT-' . Str::upper(Str::random(6)),
            'parent_id' => null,
            'name' => ['en' => $name],
            'slug' => ['en' => Str::slug($name)],
            'description' => ['en' => $this->faker->paragraph()],
        ];
    }

    /**
     * Indicate that the category is a child category.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function child()
    {
        return $this->state(function (array $attributes) {
            return [
                'parent_id' => Category::factory(),
            ];
        });
    }
}
