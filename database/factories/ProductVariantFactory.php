<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductVariant>
 */
class ProductVariantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductVariant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'sku' => 'SKU-' . Str::upper(Str::random(6)),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'compare_at_price' => $this->faker->optional(0.3)->randomFloat(2, 10, 1500),
            'cost_price' => $this->faker->optional(0.7)->randomFloat(2, 5, 500),
            'weight' => $this->faker->optional(0.8)->randomFloat(2, 0.1, 10),
            'weight_unit' => $this->faker->randomElement(['kg', 'g', 'lb', 'oz']),
            'attributes' => null,
            'is_active' => true,
            'name' => ['en' => $this->faker->words(2, true)],
        ];
    }
}
