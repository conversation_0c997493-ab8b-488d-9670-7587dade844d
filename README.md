# WisdomTechno - E-commerce Platform

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-FF2D20?style=for-the-badge&logo=laravel&logoColor=white)](https://laravel.com)
[![PHP](https://img.shields.io/badge/PHP-777BB4?style=for-the-badge&logo=php&logoColor=white)](https://www.php.net/)
[![MySQL](https://img.shields.io/badge/MySQL-005C84?style=for-the-badge&logo=mysql&logoColor=white)](https://www.mysql.com/)
[![Stripe](https://img.shields.io/badge/Stripe-008CDD?style=for-the-badge&logo=stripe&logoColor=white)](https://stripe.com/)
[![PayPal](https://img.shields.io/badge/PayPal-00457C?style=for-the-badge&logo=paypal&logoColor=white)](https://www.paypal.com/)

WisdomTechno is a modern, feature-rich e-commerce platform built with Laravel 12. It provides a robust foundation for building online stores with support for multiple payment gateways, inventory management, and customer management.

## ✨ Features

- 🛍️ **Product Management**
  - Product variants and attributes
  - Categories and tags
  - Inventory tracking
  - Digital and physical products

- 💳 **Payment Processing**
  - Stripe integration
  - PayPal integration
  - Multiple payment methods
  - Secure checkout process

- 🚚 **Shipping & Tax**
  - Multiple shipping zones
  - Tax rate management
  - Real-time shipping calculations
  - Free shipping thresholds

- 👥 **Customer Management**
  - User profiles
  - Order history
  - Address book
  - Wishlists

- 📊 **Admin Dashboard**
  - Sales analytics
  - Order management
  - Customer insights
  - Product management

## 🚀 Installation

### Prerequisites

- PHP 8.2+
- Composer
- MySQL 8.0+ or MariaDB 10.4+
- Node.js 18+ & NPM

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/wisdomtechno.git
   cd wisdomtechno
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install JavaScript dependencies**
   ```bash
   npm install
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure database**
   Update `.env` with your database credentials:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=wisdomtechno
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. **Run migrations and seeders**
   ```bash
   php artisan migrate --seed
   ```

7. **Compile assets**
   ```bash
   npm run dev
   # or for production
   npm run build
   ```

8. **Start the development server**
   ```bash
   php artisan serve
   ```

9. **Access the application**
   - Frontend: http://localhost:8000
   - Admin Panel: http://localhost:8000/admin
     - Default admin credentials:
       - Email: <EMAIL>
       - Password: password

## 🔧 Configuration

### Payment Gateways

#### Stripe
1. Get your API keys from [Stripe Dashboard](https://dashboard.stripe.com/)
2. Update `.env`:
   ```env
   STRIPE_KEY=your_stripe_key
   STRIPE_SECRET=your_stripe_secret
   STRIPE_WEBHOOK_SECRET=your_webhook_secret
   ```

#### PayPal
1. Get your API credentials from [PayPal Developer](https://developer.paypal.com/)
2. Update `.env`:
   ```env
   PAYPAL_MODE=sandbox
   PAYPAL_SANDBOX_API_USERNAME=your_sandbox_username
   PAYPAL_SANDBOX_API_PASSWORD=your_sandbox_password
   PAYPAL_SANDBOX_API_SECRET=your_sandbox_secret
   ```

### Email Configuration
Update your mail settings in `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

## 🧪 Testing

Run the test suite:
```bash
composer test
```

For test coverage:
```bash
composer test -- --coverage-html coverage
```

## 🛠️ Development

### Code Style
This project follows PSR-12 coding standards. To fix code style issues:

```bash
composer fix-style
```

### IDE Helpers
Generate IDE helper files:

```bash
composer ide-helper
docker-compose exec app php artisan ide-helper:models -W
```

## 🚀 Deployment

### Production Requirements
- PHP 8.2+
- Composer 2.0+
- Node.js 18+ & NPM
- MySQL 8.0+ or MariaDB 10.4+
- Redis (recommended for caching)
- Supervisor (for queue workers)

### Deployment Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/wisdomtechno.git /var/www/wisdomtechno
   cd /var/www/wisdomtechno
   ```

2. **Install dependencies**
   ```bash
   composer install --optimize-autoloader --no-dev
   npm install && npm run build
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   php artisan key:generate
   ```

4. **Run migrations**
   ```bash
   php artisan migrate --force
   php artisan db:seed --class=ProductionSeeder
   ```

5. **Optimize**
   ```bash
   php artisan optimize
   php artisan storage:link
   ```

6. **Set up queue workers**
   Configure Supervisor to run `php artisan queue:work --tries=3`

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open-source and available under the [MIT License](LICENSE).

## ❤️ Support

If you find this project helpful, please consider giving it a ⭐️ on GitHub!
