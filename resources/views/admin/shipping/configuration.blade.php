<x-admin-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Shipping Configuration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    
                    @if (session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('info'))
                        <div class="mb-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                            {{ session('info') }}
                        </div>
                    @endif

                    <form action="{{ route('admin.shipping.configuration.update') }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Fallback Behavior -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-lg font-medium mb-4">{{ __('Fallback Behavior') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                {{ __('Choose what happens when a customer\'s address is not covered by any specific shipping zone.') }}
                            </p>
                            
                            <div class="space-y-3">
                                @foreach($fallbackOptions as $value => $label)
                                    <label class="flex items-start">
                                        <input type="radio" name="fallback_behavior" value="{{ $value }}" 
                                               {{ $config['fallback_behavior'] === $value ? 'checked' : '' }}
                                               class="mt-1 h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                        <div class="ml-3">
                                            <span class="text-sm font-medium">{{ $label }}</span>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                        </div>

                        <!-- Dynamic Calculations -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-lg font-medium mb-4">{{ __('Calculation Settings') }}</h3>
                            
                            <div class="space-y-4">
                                <label class="flex items-center">
                                    <input type="checkbox" name="enable_dynamic_calculations" value="1"
                                           {{ $config['enable_dynamic_calculations'] ? 'checked' : '' }}
                                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <span class="ml-2 text-sm">{{ __('Enable dynamic calculations for catch-all zones') }}</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" name="prioritize_specific_zones" value="1"
                                           {{ $config['prioritize_specific_zones'] ? 'checked' : '' }}
                                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <span class="ml-2 text-sm">{{ __('Prioritize specific zones over catch-all zones') }}</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" name="require_shipping_address" value="1"
                                           {{ $config['require_shipping_address'] ? 'checked' : '' }}
                                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <span class="ml-2 text-sm">{{ __('Require shipping address for calculations') }}</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" name="log_shipping_calculations" value="1"
                                           {{ $config['log_shipping_calculations'] ? 'checked' : '' }}
                                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <span class="ml-2 text-sm">{{ __('Log shipping calculations for debugging') }}</span>
                                </label>
                            </div>
                        </div>

                        <!-- Default Fallback Methods -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-lg font-medium mb-4">{{ __('Default Fallback Methods') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                {{ __('These methods are used when no dynamic calculations are available.') }}
                            </p>

                            <div id="fallback-methods" class="space-y-4">
                                @foreach($config['default_fallback_methods'] as $code => $method)
                                    <div class="border border-gray-300 dark:border-gray-600 p-4 rounded-lg">
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium mb-1">{{ __('Method Code') }}</label>
                                                <input type="text" name="default_fallback_methods[{{ $code }}][code]" 
                                                       value="{{ $code }}" readonly
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100">
                                            </div>
                                            
                                            <div>
                                                <label class="block text-sm font-medium mb-1">{{ __('Name') }}</label>
                                                <input type="text" name="default_fallback_methods[{{ $code }}][name]" 
                                                       value="{{ $method['name'] }}" required
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                            </div>
                                            
                                            <div>
                                                <label class="block text-sm font-medium mb-1">{{ __('Price') }}</label>
                                                <input type="number" step="0.01" min="0" 
                                                       name="default_fallback_methods[{{ $code }}][price]" 
                                                       value="{{ $method['price'] }}" required
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                            </div>
                                            
                                            <div>
                                                <label class="block text-sm font-medium mb-1">{{ __('Type') }}</label>
                                                <select name="default_fallback_methods[{{ $code }}][method_type]" required
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                                    <option value="flat_rate" {{ $method['method_type'] === 'flat_rate' ? 'selected' : '' }}>Flat Rate</option>
                                                    <option value="weight_based" {{ $method['method_type'] === 'weight_based' ? 'selected' : '' }}>Weight Based</option>
                                                    <option value="price_based" {{ $method['method_type'] === 'price_based' ? 'selected' : '' }}>Price Based</option>
                                                    <option value="item_based" {{ $method['method_type'] === 'item_based' ? 'selected' : '' }}>Item Based</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <label class="block text-sm font-medium mb-1">{{ __('Description') }}</label>
                                            <input type="text" name="default_fallback_methods[{{ $code }}][description]" 
                                                   value="{{ $method['description'] ?? '' }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-between">
                            <div class="space-x-3">
                                <button type="submit" 
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    {{ __('Save Configuration') }}
                                </button>
                                
                                <a href="{{ route('admin.shipping.configuration.reset') }}" 
                                   onclick="return confirm('Are you sure you want to reset to defaults?')"
                                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    {{ __('Reset to Defaults') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Test Configuration -->
                    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                        <h3 class="text-lg font-medium mb-4">{{ __('Test Configuration') }}</h3>
                        
                        <form action="{{ route('admin.shipping.configuration.test') }}" method="POST" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            @csrf
                            
                            <div>
                                <label class="block text-sm font-medium mb-1">{{ __('Country Code') }}</label>
                                <input type="text" name="test_country" placeholder="US" maxlength="2" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium mb-1">{{ __('Region/State') }}</label>
                                <input type="text" name="test_region" placeholder="CA"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium mb-1">{{ __('Postal Code') }}</label>
                                <input type="text" name="test_postal_code" placeholder="90210"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                            
                            <div class="flex items-end">
                                <button type="submit" 
                                        class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    {{ __('Test') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
