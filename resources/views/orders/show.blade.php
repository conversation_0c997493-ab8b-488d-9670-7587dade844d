<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Order #:number', ['number' => $order->order_number]) }}
            </h2>
            <a href="{{ route('orders.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                {{ __('Back to Orders') }}
            </a>
        </div>
    </x-slot>

    @section('content')
    <div class="py-8 md:py-12">
        <div class="max-w-5xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 lg:p-8 text-gray-900 dark:text-gray-100">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Order Summary -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">{{ __('Order Summary') }}</h3>

                            <div class="space-y-4">
                                @foreach($order->items as $item)
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $item->name }}
                                            </div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $item->formatted_total }}
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            <p>{{ __('Quantity') }}: {{ $item->quantity }}</p>
                                            <p>{{ __('Price') }}: {{ $item->formatted_price }}</p>
                                            @if($item->options)
                                                <div class="mt-1">
                                                    @foreach($item->options as $key => $value)
                                                        <span class="block">{{ ucfirst($key) }}: {{ is_array($value) ? implode(', ', $value) : $value }}</span>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-500 dark:text-gray-300">{{ __('Subtotal') }}:</span>
                                    <span class="text-gray-900 dark:text-white">{{ $order->formatted_subtotal }}</span>
                                </div>
                                @if($order->tax_amount > 0)
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-500 dark:text-gray-300">{{ __('Tax') }}:</span>
                                        <span class="text-gray-900 dark:text-white">{{ $order->formatted_tax_amount }}</span>
                                    </div>
                                @endif
                                @if($order->shipping_amount > 0)
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-500 dark:text-gray-300">{{ __('Shipping') }}:</span>
                                        <span class="text-gray-900 dark:text-white">{{ $order->formatted_shipping_amount }}</span>
                                    </div>
                                @endif
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-500 dark:text-gray-300">{{ __('Discount') }}:</span>
                                        <span class="text-green-600 dark:text-green-400">-{{ $order->formatted_discount_amount }}</span>
                                    </div>
                                @endif
                                <div class="flex justify-between font-bold text-gray-900 dark:text-white">
                                    <span>{{ __('Total') }}:</span>
                                    <span>{{ $order->formatted_total }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Order Details Sidebar -->
                        <div>
                            <!-- Order Status -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Order Information') }}</h3>

                                <div class="space-y-3 text-sm">
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Order Number') }}:</p>
                                        <p class="text-gray-900 dark:text-white">{{ $order->order_number }}</p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Order Date') }}:</p>
                                        <p class="text-gray-900 dark:text-white">{{ $order->created_at->format('M d, Y h:i A') }}</p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Status') }}:</p>
                                        <p class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($order->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @elseif($order->status === 'processing') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300
                                            @endif">
                                            {{ ucfirst($order->status) }}
                                        </p>
                                    </div>

                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Status') }}:</p>
                                        <p class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($order->isPaid()) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @else bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                            @endif">
                                            {{ $order->isPaid() ? __('Paid') : __('Awaiting Payment') }}
                                        </p>
                                    </div>

                                    @php
                                        $latestPayment = $order->latestPayment();
                                    @endphp

                                    @if($latestPayment)
                                        <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                                            <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Details') }}:</p>
                                            <div class="space-y-1 mt-1">
                                                <p class="text-gray-900 dark:text-white text-sm">{{ __('Method') }}: {{ ucfirst($latestPayment->payment_method) }}</p>
                                                <p class="text-gray-900 dark:text-white text-sm">{{ __('Status') }}: {{ ucfirst($latestPayment->status) }}</p>
                                                @if($latestPayment->gateway_transaction_id)
                                                    <p class="text-gray-900 dark:text-white text-sm">{{ __('Transaction ID') }}: {{ $latestPayment->gateway_transaction_id }}</p>
                                                @endif
                                                @if($latestPayment->processed_at)
                                                     <p class="text-gray-900 dark:text-white text-sm">{{ __('Processed At') }}: {{ $latestPayment->processed_at->format('M d, Y h:i A') }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Shipping Information -->
                            @if($order->shipping_address)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Shipping Information') }}</h3>

                                    <div class="space-y-2 text-sm">
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $order->shipping_address->full_name }}</p>
                                        <p class="text-gray-600 dark:text-gray-400">{{ $order->shipping_address->address_line_1 }}</p>
                                        @if($order->shipping_address->address_line_2)
                                            <p class="text-gray-600 dark:text-gray-400">{{ $order->shipping_address->address_line_2 }}</p>
                                        @endif
                                        <p class="text-gray-600 dark:text-gray-400">
                                            {{ $order->shipping_address->city }},
                                            {{ $order->shipping_address->state }}
                                            {{ $order->shipping_address->postal_code }}
                                        </p>
                                        <p class="text-gray-600 dark:text-gray-400">{{ $order->shipping_address->country }}</p>
                                        @if($order->shipping_address->phone)
                                            <p class="text-gray-600 dark:text-gray-400">{{ $order->shipping_address->phone }}</p>
                                        @endif
                                    </div>

                                    @if($order->shipping_method)
                                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                            <div class="flex justify-between">
                                                <span class="text-gray-600 dark:text-gray-400">{{ __('Shipping Method') }}:</span>
                                                <span class="text-gray-900 dark:text-white">{{ $order->shipping_method }}</span>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endif

                            <!-- Billing Information -->
                            @if($order->billing_address)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Billing Information') }}</h3>

                                    <div class="space-y-2 text-sm">
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $order->billing_address->full_name }}</p>
                                        <p class="text-gray-600 dark:text-gray-400">{{ $order->billing_address->address_line_1 }}</p>
                                        @if($order->billing_address->address_line_2)
                                            <p class="text-gray-600 dark:text-gray-400">{{ $order->billing_address->address_line_2 }}</p>
                                        @endif
                                        <p class="text-gray-600 dark:text-gray-400">
                                            {{ $order->billing_address->city }},
                                            {{ $order->billing_address->state }}
                                            {{ $order->billing_address->postal_code }}
                                        </p>
                                        <p class="text-gray-600 dark:text-gray-400">{{ $order->billing_address->country }}</p>
                                        @if($order->billing_address->phone)
                                            <p class="text-gray-600 dark:text-gray-400">{{ $order->billing_address->phone }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <!-- Payment Actions -->
                            @if($order->status === 'pending' && !$order->isPaid())
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Payment Actions') }}</h3>

                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        {{ __('This order is awaiting payment. Click the button below to complete your payment.') }}
                                    </p>

                                    <div class="mb-4">
                                        <div class="text-xs text-amber-600 dark:text-amber-400">
                                            <p class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                </svg>
                                                {{ __('Note: Product availability may have changed since you placed this order.') }}
                                            </p>
                                        </div>
                                    </div>

                                    <a href="{{ route('payments.show', $order->order_number) }}"
                                       class="w-full inline-flex justify-center items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                        {{ __('Complete Payment') }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-app-layout>
