<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Payment') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Order Summary -->
                        <div class="md:col-span-1">
                            <h3 class="text-lg font-semibold mb-4">{{ __('Order Summary') }}</h3>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="mb-2"><span class="font-medium">{{ __('Order Number') }}:</span> {{ $order->order_number }}</p>
                                <p class="mb-2"><span class="font-medium">{{ __('Date') }}:</span> {{ $order->created_at->format('M d, Y') }}</p>
                                <p class="mb-4"><span class="font-medium">{{ __('Status') }}:</span> 
                                    <span class="px-2 py-1 text-xs rounded-full 
                                        @if($order->status === 'pending') bg-yellow-200 text-yellow-800
                                        @elseif($order->status === 'processing') bg-blue-200 text-blue-800
                                        @elseif($order->status === 'shipped') bg-indigo-200 text-indigo-800
                                        @elseif($order->status === 'delivered') bg-green-200 text-green-800
                                        @elseif($order->status === 'cancelled') bg-red-200 text-red-800
                                        @elseif($order->status === 'refunded') bg-gray-200 text-gray-800
                                        @endif">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </p>
                                
                                <div class="border-t border-gray-300 dark:border-gray-600 pt-4 mb-4">
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Subtotal') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->subtotal, 2) }}</span>
                                    </p>
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Shipping') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->shipping_cost, 2) }}</span>
                                    </p>
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Tax') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->tax_amount, 2) }}</span>
                                    </p>
                                    @if($order->discount_amount > 0)
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Discount') }}:</span>
                                        <span>-{{ $order->currency }} {{ number_format($order->discount_amount, 2) }}</span>
                                    </p>
                                    @endif
                                    <p class="flex justify-between font-bold mt-2 pt-2 border-t border-gray-300 dark:border-gray-600">
                                        <span>{{ __('Total') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->total, 2) }}</span>
                                    </p>
                                </div>
                                
                                <div class="text-sm">
                                    <p class="mb-1"><span class="font-medium">{{ __('Shipping Address') }}:</span></p>
                                    <p class="mb-4">
                                        {{ $order->shippingAddress->address_line1 }}<br>
                                        @if($order->shippingAddress->address_line2)
                                            {{ $order->shippingAddress->address_line2 }}<br>
                                        @endif
                                        {{ $order->shippingAddress->city }}, 
                                        @if($order->shippingAddress->state)
                                            {{ $order->shippingAddress->state }}, 
                                        @endif
                                        {{ $order->shippingAddress->postal_code }}<br>
                                        {{ $order->shippingAddress->country }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Form -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-semibold mb-4">{{ __('Credit Card Payment') }}</h3>
                            
                            <div class="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg">
                                <form id="payment-form" class="space-y-6">
                                    <div>
                                        <label for="card-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ __('Credit or debit card') }}
                                        </label>
                                        <div id="card-element" class="border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-800">
                                            <!-- Stripe Elements will be inserted here -->
                                        </div>
                                        <div id="card-errors" class="text-red-500 text-sm mt-2" role="alert"></div>
                                    </div>
                                    
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                        </svg>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ __('Your payment information is secured with SSL encryption') }}
                                        </span>
                                    </div>
                                    
                                    <button id="submit-button" type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center">
                                        <span id="button-text">{{ __('Pay') }} {{ $order->currency }} {{ number_format($order->total, 2) }}</span>
                                        <div id="spinner" class="hidden">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            <span>{{ __('Processing...') }}</span>
                                        </div>
                                    </button>
                                    
                                    <div class="flex justify-center space-x-4 mt-4">
                                        <img src="{{ asset('img/payment/visa.svg') }}" alt="Visa" class="h-8">
                                        <img src="{{ asset('img/payment/mastercard.svg') }}" alt="Mastercard" class="h-8">
                                        <img src="{{ asset('img/payment/amex.svg') }}" alt="American Express" class="h-8">
                                        <img src="{{ asset('img/payment/discover.svg') }}" alt="Discover" class="h-8">
                                    </div>
                                </form>
                            </div>
                            
                            <div class="mt-6 text-center">
                                <a href="{{ route('orders.show', $order) }}" class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium">
                                    {{ __('Cancel and return to order details') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        // Pass the order number and URLs to JavaScript
        const orderNumber = '{{ $order->order_number }}';
        const cancelUrl = '{{ route('checkout.stripe.cancel', ['order_id' => $order->order_number]) }}';
        const confirmationUrl = '{{ route('checkout.confirmation', $order->order_number) }}';
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Stripe
            const stripe = Stripe('{{ $publicKey }}');
            const elements = stripe.elements();
            
            // Create card element
            const style = {
                base: {
                    color: document.documentElement.classList.contains('dark') ? '#fff' : '#32325d',
                    fontFamily: '"Figtree", sans-serif',
                    fontSmoothing: 'antialiased',
                    fontSize: '16px',
                    '::placeholder': {
                        color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#aab7c4'
                    },
                    backgroundColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff',
                },
                invalid: {
                    color: '#fa755a',
                    iconColor: '#fa755a'
                }
            };
            
            const cardElement = elements.create('card', { style: style });
            cardElement.mount('#card-element');
            
            // Handle form submission
            const form = document.getElementById('payment-form');
            const submitButton = document.getElementById('submit-button');
            const buttonText = document.getElementById('button-text');
            const spinner = document.getElementById('spinner');
            const cardErrors = document.getElementById('card-errors');
            
            form.addEventListener('submit', async (event) => {
                event.preventDefault();
                
                // Disable the submit button to prevent repeated clicks
                submitButton.disabled = true;
                buttonText.classList.add('hidden');
                spinner.classList.remove('hidden');
                cardErrors.textContent = '';
                
                try {
                    const { paymentIntent, error } = await stripe.confirmCardPayment('{{ $clientSecret }}', {
                        payment_method: {
                            card: cardElement,
                            billing_details: {
                                name: '{{ $order->billingAddress->full_name ?? $order->shippingAddress->full_name }}',
                                email: '{{ $order->user->email ?? $order->email }}',
                                address: {
                                    line1: '{{ $order->billingAddress->address_line1 ?? $order->shippingAddress->address_line1 }}',
                                    city: '{{ $order->billingAddress->city ?? $order->shippingAddress->city }}',
                                    state: '{{ $order->billingAddress->state ?? $order->shippingAddress->state }}',
                                    postal_code: '{{ $order->billingAddress->postal_code ?? $order->shippingAddress->postal_code }}',
                                    country: '{{ $order->billingAddress->country ?? $order->shippingAddress->country }}',
                                }
                            }
                        },
                        return_url: window.location.origin + '/payments/' + orderNumber + '?payment_method=stripe',
                    });
                    
                    if (error) {
                        // Handle card errors
                        if (error.payment_intent && 
                           (error.payment_intent.status === 'requires_payment_method' || 
                            error.payment_intent.status === 'canceled')) {
                            // Redirect to payment page with error message
                            window.location.href = cancelUrl + '?error=' + encodeURIComponent(error.message);
                        } else {
                            // Show error to customer
                            cardErrors.textContent = error.message;
                            // Re-enable the submit button
                            submitButton.disabled = false;
                            buttonText.classList.remove('hidden');
                            spinner.classList.add('hidden');
                        }
                    } else if (paymentIntent.status === 'succeeded') {
                        // Payment successful
                        window.location.href = confirmationUrl;
                    } else if (paymentIntent.status === 'requires_action') {
                        // Handle 3D Secure authentication
                        const { error: confirmError } = await stripe.confirmCardPayment('{{ $clientSecret }}');
                        if (confirmError) {
                            window.location.href = cancelUrl + '?error=' + encodeURIComponent(confirmError.message);
                        }
                    }
                } catch (error) {
                    console.error('Error:', error);
                    window.location.href = cancelUrl + '?error=' + encodeURIComponent('An unexpected error occurred. Please try again.');
                }
            });
            
            // Handle real-time validation errors from the card Element
            cardElement.addEventListener('change', (event) => {
                if (event.error) {
                    cardErrors.textContent = event.error.message;
                } else {
                    cardErrors.textContent = '';
                }
            });
        });
    </script>
    @endpush
</x-app-layout>
