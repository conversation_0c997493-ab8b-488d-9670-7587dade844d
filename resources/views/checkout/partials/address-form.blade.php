{{-- resources/views/checkout/partials/address-form.blade.php --}}
@props([
    'type' => 'billing', // 'billing' or 'shipping'
    'countries' => [],
    'address' => null // Pass an existing address model for editing, or null for new
])

<div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
    <div>
        <label for="{{ $type }}_address[first_name]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('First Name') }} <span class="text-red-500">*</span></label>
        <input type="text" name="{{ $type }}_address[first_name]" id="{{ $type }}_address_first_name"
               value="{{ old($type.'_address.first_name', $address->first_name ?? (Auth::check() && $type === 'billing' ? Auth::user()->first_name : '')) }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
        @error($type.'_address.first_name') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div>
        <label for="{{ $type }}_address[last_name]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Last Name') }} <span class="text-red-500">*</span></label>
        <input type="text" name="{{ $type }}_address[last_name]" id="{{ $type }}_address_last_name"
               value="{{ old($type.'_address.last_name', $address->last_name ?? (Auth::check() && $type === 'billing' ? Auth::user()->first_name : '')) }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
        @error($type.'_address.last_name') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div class="md:col-span-2">
        <label for="{{ $type }}_address[address_line1]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Address Line 1') }} <span class="text-red-500">*</span></label>
        <input type="text" name="{{ $type }}_address[address_line1]" id="{{ $type }}_address_address_line1"
               value="{{ old($type.'_address.address_line1', $address->address_line1 ?? '') }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
        @error($type.'_address.address_line1') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div class="md:col-span-2">
        <label for="{{ $type }}_address[address_line2]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Address Line 2') }} ({{ __('Optional') }})</label>
        <input type="text" name="{{ $type }}_address[address_line2]" id="{{ $type }}_address_address_line2"
               value="{{ old($type.'_address.address_line2', $address->address_line2 ?? '') }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
        @error($type.'_address.address_line2') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div>
        <label for="{{ $type }}_address[city]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('City') }} <span class="text-red-500">*</span></label>
        <input type="text" name="{{ $type }}_address[city]" id="{{ $type }}_address_city"
               value="{{ old($type.'_address.city', $address->city ?? '') }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
        @error($type.'_address.city') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div>
        <label for="{{ $type }}_address[region]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('State / Province') }} <span class="text-red-500">*</span></label>
        <input type="text" name="{{ $type }}_address[region]" id="{{ $type }}_address_region"
               value="{{ old($type.'_address.region', $address->region ?? '') }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}"> {{-- Made region required as per ProcessCheckoutRequest --}}
        @error($type.'_address.region') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div>
        <label for="{{ $type }}_address[postal_code]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Postal Code') }} <span class="text-red-500">*</span></label>
        <input type="text" name="{{ $type }}_address[postal_code]" id="{{ $type }}_address_postal_code"
               value="{{ old($type.'_address.postal_code', $address->postal_code ?? '') }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
        @error($type.'_address.postal_code') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div>
        <label for="{{ $type }}_address[country]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Country') }} <span class="text-red-500">*</span></label>
        <select name="{{ $type }}_address[country]" id="{{ $type }}_address_country"
                class="mt-1 block w-full py-2 px-3 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                required x-bind:disabled="{{ $type === 'shipping' ? '!(!useBillingForShipping && showNewShippingAddressForm)' : '!showNewBillingAddressForm' }}">
            <option value="">{{ __('Select a country') }}</option>
            @foreach($countries as $code => $name)
                <option value="{{ $code }}" {{ old($type.'_address.country', $address->country ?? '') === $code ? 'selected' : '' }}>
                    {{ $name }}
                </option>
            @endforeach
        </select>
        @error($type.'_address.country') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    @if($type === 'billing') {{-- Email and Phone only for billing as per ProcessCheckoutRequest --}}
    <div>
        <label for="billing_address[email]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Email Address') }} <span class="text-red-500">*</span></label>
        <input type="email" name="billing_address[email]" id="billing_address_email"
               value="{{ old('billing_address.email', $address->email ?? (Auth::check() ? Auth::user()->email : '')) }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               required x-bind:disabled="!showNewBillingAddressForm">
        @error('billing_address.email') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>

    <div>
        <label for="billing_address[phone]" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Phone Number') }}</label>
        <input type="tel" name="billing_address[phone]" id="billing_address_phone"
               value="{{ old('billing_address.phone', $address->phone ?? (Auth::check() ? Auth::user()->phone_number : '')) }}"
               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md"
               x-bind:disabled="!showNewBillingAddressForm">
        @error('billing_address.phone') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
    </div>
    @endif
</div>
