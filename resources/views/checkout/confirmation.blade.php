@extends('layouts.app')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        {{ __('Order Confirmation') }}
    </h2>
@endsection

@section('content')
    <div class="py-8 md:py-12">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 lg:p-8 text-gray-900 dark:text-gray-100">
                    <!-- Order Confirmation Header -->
                    <div class="text-center mb-8">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-2">{{ __('Thank You for Your Order!') }}</h3>
                        <p class="text-md text-gray-600 dark:text-gray-400">
                            {{ __('Your order has been received and is now being processed.') }}
                        </p>
                        <p class="text-gray-600 dark:text-gray-400 mt-2 text-sm">
                            {{ __('Order Number') }}: <span class="font-semibold">{{ $order->order_number }}</span>
                        </p>
                    </div>

                    <!-- Order Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Order Summary -->
                        <div class="md:col-span-2">
                            <h4 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                                {{ __('Order Details') }}
                            </h4>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-800">
                                        <tr>
                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                {{ __('Product') }}
                                            </th>
                                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                {{ __('Quantity') }}
                                            </th>
                                            <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                {{ __('Price') }}
                                            </th>
                                            <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                {{ __('Total') }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        @foreach($order->items as $item)
                                            <tr>
                                                <td class="px-4 py-3 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden">
                                                            @if($item->product_image)
                                                                <img src="{{ $item->product_image }}" alt="{{ $item->product_name }}" class="h-10 w-10 object-cover">
                                                            @else
                                                                <div class="h-10 w-10 flex items-center justify-center text-gray-500 dark:text-gray-400">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                                    </svg>
                                                                </div>
                                                            @endif
                                                        </div>
                                                        <div class="ml-3">
                                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                                {{ $item->product_name }}
                                                            </div>
                                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                                {{ $item->variant_name }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $item->quantity }}
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                                                    {{ $order->currency }} {{ number_format($item->unit_price, 2) }}
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right font-medium">
                                                    {{ $order->currency }} {{ number_format($item->unit_price * $item->quantity, 2) }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot class="bg-gray-50 dark:bg-gray-800">
                                        <tr>
                                            <td colspan="3" class="px-4 py-2 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                                                {{ __('Subtotal') }}:
                                            </td>
                                            <td class="px-4 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ $order->currency }} {{ number_format($order->subtotal, 2) }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" class="px-4 py-2 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                                                {{ __('Shipping') }}:
                                            </td>
                                            <td class="px-4 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ $order->currency }} {{ number_format($order->shipping_cost, 2) }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" class="px-4 py-2 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                                                {{ __('Tax') }}:
                                            </td>
                                            <td class="px-4 py-2 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ $order->currency }} {{ number_format($order->tax_amount, 2) }}
                                            </td>
                                        </tr>
                                        @if($order->discount_amount > 0)
                                            <tr>
                                                <td colspan="3" class="px-4 py-2 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                                                    {{ __('Discount') }}:
                                                </td>
                                                <td class="px-4 py-2 text-right text-sm font-medium text-green-600 dark:text-green-400">
                                                    -{{ $order->currency }} {{ number_format($order->discount_amount, 2) }}
                                                </td>
                                            </tr>
                                        @endif
                                        <tr class="bg-gray-100 dark:bg-gray-700">
                                            <td colspan="3" class="px-4 py-2 text-right text-base font-bold text-gray-900 dark:text-gray-100">
                                                {{ __('Total') }}:
                                            </td>
                                            <td class="px-4 py-2 text-right text-base font-bold text-gray-900 dark:text-gray-100">
                                                {{ $order->currency }} {{ number_format($order->total, 2) }}
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <!-- Order Information and Addresses -->
                        <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 class="text-lg font-semibold mb-4">{{ __('Order Information') }}</h4>
                                <div class="space-y-3 text-sm">
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Order Number') }}</p>
                                        <p class="text-gray-900 dark:text-gray-100">{{ $order->order_number }}</p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Date') }}</p>
                                        <p class="text-gray-900 dark:text-gray-100">{{ $order->created_at->format('M d, Y, h:i A') }}</p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Status') }}</p>
                                        <p class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                                            @elseif($order->status === 'shipped') bg-indigo-100 text-indigo-800
                                            @elseif($order->status === 'delivered') bg-green-100 text-green-800
                                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                            @elseif($order->status === 'refunded') bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($order->status) }}
                                        </p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Method') }}</p>
                                        <p class="text-gray-900 dark:text-gray-100">
                                            @if($order->payment_method === 'stripe')
                                                {{ __('Credit Card (Stripe)') }}
                                            @elseif($order->payment_method === 'paypal')
                                                {{ __('PayPal') }}
                                            @elseif($order->payment_method === 'bank_transfer')
                                                {{ __('Bank Transfer') }}
                                            @else
                                                {{ ucfirst($order->payment_method) }}
                                            @endif
                                        </p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-500 dark:text-gray-400">{{ __('Shipping Method') }}</p>
                                        <p class="text-gray-900 dark:text-gray-100">{{ $order->shipping_method }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-6">
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <h4 class="text-lg font-semibold mb-4">{{ __('Shipping Address') }}</h4>
                                    <address class="not-italic text-gray-700 dark:text-gray-300 text-sm">
                                        {{ $order->shippingAddress->first_name }} {{ $order->shippingAddress->last_name }}<br>
                                        {{ $order->shippingAddress->address_line1 }}<br>
                                        @if($order->shippingAddress->address_line2)
                                            {{ $order->shippingAddress->address_line2 }}<br>
                                        @endif
                                        {{ $order->shippingAddress->city }}, 
                                        @if($order->shippingAddress->state)
                                            {{ $order->shippingAddress->state }}, 
                                        @endif
                                        {{ $order->shippingAddress->postal_code }}<br>
                                        {{ $order->shippingAddress->country }}
                                    </address>
                                </div>

                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <h4 class="text-lg font-semibold mb-4">{{ __('Billing Address') }}</h4>
                                    <address class="not-italic text-gray-700 dark:text-gray-300 text-sm">
                                        {{ $order->billingAddress->first_name }} {{ $order->billingAddress->last_name }}<br>
                                        {{ $order->billingAddress->address_line1 }}<br>
                                        @if($order->billingAddress->address_line2)
                                            {{ $order->billingAddress->address_line2 }}<br>
                                        @endif
                                        {{ $order->billingAddress->city }}, 
                                        @if($order->billingAddress->state)
                                            {{ $order->billingAddress->state }}, 
                                        @endif
                                        {{ $order->billingAddress->postal_code }}<br>
                                        {{ $order->billingAddress->country }}
                                    </address>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Next Steps -->
                    <div class="mt-8 text-center">
                        <p class="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                            {{ __('A confirmation email has been sent to your email address.') }}
                        </p>
                        <div class="flex flex-col sm:flex-row justify-center gap-4">
                            <a href="{{ route('orders.show', $order) }}" class="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                {{ __('View Order Details') }}
                            </a>
                            <a href="{{ route('home') }}" class="inline-flex items-center justify-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-semibold text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                {{ __('Continue Shopping') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
