@extends('layouts.app')

@section('content')
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-extrabold text-gray-900 dark:text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
                <span class="block">WisdomTechno</span>
                <span class="block text-indigo-600 dark:text-indigo-400">YouTube Channel</span>
            </h1>
            <p class="mt-5 max-w-xl mx-auto text-xl text-gray-500 dark:text-gray-300">
                Explore our latest videos featuring tech insights, tutorials, and success stories.
            </p>
        </div>

        <!-- Featured Video Section -->
        <div class="mb-16">
            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl overflow-hidden shadow-xl">
                <div class="p-6 text-white">
                    <h2 class="text-2xl font-bold mb-2">Featured Video</h2>
                    <p class="opacity-80">Watch our most popular content</p>
                </div>
                <div class="aspect-w-16 aspect-h-9 bg-gray-800">
                    <div id="featured-player" class="w-full h-full"></div>
                </div>
            </div>
        </div>

        <!-- Video Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @if(count($videos) > 0)
                @foreach($videos as $video)
                    <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="relative">
                            <img src="{{ $video['snippet']['thumbnails']['high']['url'] ?? 'https://via.placeholder.com/480x360.png?text=No+Thumbnail' }}"
                                 alt="{{ $video['snippet']['title'] ?? 'Video thumbnail' }}"
                                 class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                                <button
                                    class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg flex items-center play-video"
                                    data-video-id="{{ $video['id']['videoId'] ?? '' }}"
                                >
                                    <x-svg.play class="h-5 w-5 mr-2" />
                                    Play Video
                                </button>
                            </div>
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                {{ $video['snippet']['title'] ?? 'Untitled Video' }}
                            </h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                                {{ $video['snippet']['description'] ?? 'No description available' }}
                            </p>
                            <div class="flex items-center text-gray-500 dark:text-gray-400 text-xs">
                                <x-svg.calendar class="h-4 w-4 mr-1" />
                                @if(isset($video['snippet']['publishedAt']))
                                    {{ \Carbon\Carbon::parse($video['snippet']['publishedAt'])->format('F j, Y') }}
                                @else
                                    Unknown date
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="col-span-3 text-center py-12">
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                        <x-svg.calendar class="mx-auto h-12 w-12 text-gray-400" />
                        <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No videos found</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">We couldn't find any videos for this channel. Please check back later.</p>
                    </div>
                </div>
            @endif
        </div>

        <!-- Subscribe Section -->
        <div class="mt-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl p-8 text-white text-center">
            <h2 class="text-2xl font-bold mb-4">Subscribe to Our Channel</h2>
            <p class="mb-6 max-w-2xl mx-auto">Stay updated with our latest videos, tutorials, and tech insights by subscribing to our YouTube channel.</p>
            <a href="{{ $channelUrl }}" target="_blank" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-300">
                <x-svg.youtube class="h-6 w-6 mr-2 text-red-600" />
                Subscribe on YouTube
            </a>
        </div>
    </div>

    <!-- Video Modal -->
    <div id="video-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="absolute inset-0 bg-black bg-opacity-75" id="modal-backdrop"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-xl max-w-4xl w-full mx-4">
            <div class="absolute top-0 right-0 pt-4 pr-4 z-10">
                <button id="close-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                    <x-svg.close class="h-6 w-6" />
                </button>
            </div>
            <div class="aspect-w-16 aspect-h-9">
                <div id="modal-player" class="w-full h-full"></div>
            </div>
        </div>
    </div>

    <!-- YouTube API -->
    <script>
        // Load the YouTube IFrame Player API code asynchronously
        var tag = document.createElement('script');
        tag.src = "https://www.youtube.com/iframe_api";
        var firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

        var featuredPlayer;
        var modalPlayer;
        var currentVideoId = '';

        // Create YouTube players when API is ready
        function onYouTubeIframeAPIReady() {
            // Initialize featured player with the first video
            featuredPlayer = new YT.Player('featured-player', {
                videoId: '{{ isset($videos[0]['id']['videoId']) ? $videos[0]['id']['videoId'] : 'dQw4w9WgXcQ' }}',
                playerVars: {
                    'autoplay': 0,
                    'controls': 1,
                    'rel': 0,
                    'modestbranding': 1
                }
            });

            // Initialize modal player (empty at first)
            modalPlayer = new YT.Player('modal-player', {
                playerVars: {
                    'autoplay': 1,
                    'controls': 1,
                    'rel': 0,
                    'modestbranding': 1
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('video-modal');
            const modalBackdrop = document.getElementById('modal-backdrop');
            const closeModalBtn = document.getElementById('close-modal');
            const playButtons = document.querySelectorAll('.play-video');

            // Open modal and play video
            playButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const videoId = this.getAttribute('data-video-id');
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');

                    if (modalPlayer && modalPlayer.loadVideoById) {
                        modalPlayer.loadVideoById(videoId);
                    } else {
                        currentVideoId = videoId;
                        // If player not ready yet, we'll load it when ready
                    }
                });
            });

            // Close modal
            function closeModal() {
                modal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
                if (modalPlayer && modalPlayer.stopVideo) {
                    modalPlayer.stopVideo();
                }
            }

            closeModalBtn.addEventListener('click', closeModal);
            modalBackdrop.addEventListener('click', closeModal);

            // Close on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal();
                }
            });
        });
    </script>

    <style>
        /* Aspect ratio utility classes */
        .aspect-w-16 {
            position: relative;
            padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
            --tw-aspect-w: 16;
        }
        .aspect-h-9 {
            --tw-aspect-h: 9;
        }
        .aspect-w-16 > * {
            position: absolute;
            height: 100%;
            width: 100%;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        /* Line clamp utility classes */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
@endsection
