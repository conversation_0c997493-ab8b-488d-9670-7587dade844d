@extends('layouts.app')

@section('content')
    <!-- Enhanced Hero Section -->
    <div class="relative bg-gradient-to-r from-indigo-700 via-purple-700 to-pink-700 text-white overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-10" aria-hidden="true"></div>
            <div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 to-transparent opacity-30"></div>
        </div>

        <div class="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 text-center">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 leading-tight">
                <span class="block">Our Services</span>
                <span class="block text-indigo-600 dark:text-indigo-400">Innovative Solutions for Your Business</span>
            </h1>
            <p class="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
                Discover how our cutting-edge technology services can transform your business and drive growth.
            </p>
        </div>
        <!-- Wave divider -->
        <div class="absolute bottom-0 left-0 right-0 text-gray-50 dark:text-gray-900">
            <svg class="w-full h-auto" viewBox="0 0 1440 100" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 60C240 100 480 100 720 60S1200 20 1440 60V100H0V60Z"></path>
            </svg>
        </div>
    </div>

    <div class="bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <!-- Services Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                @forelse($services as $service)
                    <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-2xl hover:-translate-y-1.5">
                        <div class="h-3 bg-gradient-to-r from-indigo-500 to-purple-600"></div>

                        @php
                            $thumbnailUrl = '';
                            try {
                                $thumbnailUrl = $service->getFirstMediaUrl('thumbnail');
                            } catch (\Exception $e) {
                                // Silently handle the error
                            }
                        @endphp

                        @if(!empty($thumbnailUrl))
                            <img src="{{ $thumbnailUrl }}" alt="{{ $service->getTranslation('name', app()->getLocale()) }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 flex items-center justify-center">
                                <div class="text-indigo-500 dark:text-indigo-400">
                                @if($service->icon && str_starts_with(trim($service->icon), '<svg'))
                                    <div class="h-16 w-16">{!! $service->icon !!}</div>
                                @elseif($service->icon) {{-- Assume it's a Font Awesome class --}}
                                    <i class="{{ $service->icon }} text-5xl"></i>
                                @else
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                @endif
                            </div>
                        </div>
                        @endif

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                {{ $service->getTranslation('name', app()->getLocale()) }}
                            </h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-5 line-clamp-3">
                                {{ $service->getTranslation('description', app()->getLocale()) }}
                            </p>
                            <div class="flex justify-between items-center">
                                <a href="{{ route('services.show', $service->getTranslation('slug', app()->getLocale())) }}" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium inline-flex items-center group">
                                    Learn more
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transition-transform duration-200 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                                <a href="{{ route('inquiries.create', ['service_id' => $service->id]) }}" class="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white text-xs font-semibold rounded-full transition-colors duration-200 shadow-sm hover:shadow-md">
                                    Inquire
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-1 md:col-span-2 lg:col-span-3 text-center py-12">
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No services available</h3>
                            <p class="mt-2 text-gray-500 dark:text-gray-400">We're currently updating our service offerings. Please check back soon.</p>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- CTA Section -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-700 rounded-xl overflow-hidden shadow-2xl">
                <div class="px-8 py-12 text-center text-white relative">
                    <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-10" aria-hidden="true"></div>
                    <div class="relative z-10">
                        <h2 class="text-3xl font-bold mb-4">Ready to Transform Your Business?</h2>
                        <p class="text-lg mb-8 max-w-2xl mx-auto opacity-90">
                            Contact us today to discuss how our services can help you achieve your goals.
                        </p>
                        <div class="flex flex-col sm:flex-row justify-center gap-4">
                            <a href="{{ route('contact') }}" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-300 transform hover:scale-105">
                                Contact Us
                            </a>
                            <a href="{{ route('inquiries.create') }}" class="inline-flex items-center justify-center px-6 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition-colors duration-300 transform hover:scale-105">
                                Request Quote
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
