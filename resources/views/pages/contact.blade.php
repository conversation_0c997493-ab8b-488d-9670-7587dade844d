@extends('pages.layout')

@section('title', 'Contact Us')

@section('page-title', 'Contact Us')

@section('page-subtitle', 'Get in touch with our team')

@section('page-content')
<!-- Hero section with animated gradient background -->
<div class="relative overflow-hidden rounded-2xl mb-16">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 opacity-90"></div>
    <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20"></div>
    <div class="relative px-8 py-16 text-white">
        <div class="max-w-3xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">We'd Love to Hear From You</h2>
            <p class="text-lg md:text-xl opacity-90 mb-8">Whether you have a question about our services, need technical support, or want to discuss a potential project, our team is ready to assist you.</p>

            <div class="flex flex-wrap justify-center gap-6 mt-10">
                <div class="flex flex-col items-center bg-white/20 backdrop-blur-sm px-6 py-4 rounded-xl">
                    <x-svg.envelope class="h-8 w-8 mb-2" />
                    <span class="text-lg font-medium">Email Support</span>
                    <span class="text-sm opacity-80">24-hour response time</span>
                </div>
                <div class="flex flex-col items-center bg-white/20 backdrop-blur-sm px-6 py-4 rounded-xl">
                    <x-svg.phone-support class="h-8 w-8 mb-2" />
                    <span class="text-lg font-medium">Phone Support</span>
                    <span class="text-sm opacity-80">Mon-Fri, 9am-5pm</span>
                </div>
                <div class="flex flex-col items-center bg-white/20 backdrop-blur-sm px-6 py-4 rounded-xl">
                    <x-svg.live-chat class="h-8 w-8 mb-2" />
                    <span class="text-lg font-medium">Live Chat</span>
                    <span class="text-sm opacity-80">Available 24/7</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-10">
    <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition duration-500 hover:shadow-2xl">
            <h3 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Contact Information</h3>

            @if(isset($contactInfo) && count($contactInfo) > 0)
                <div class="space-y-6">
                    @foreach($contactInfo as $info)
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mt-1">
                                @php
                                    $iconTitle = strtolower(trim($info->title ?? ''));
                                @endphp
                                @if (str_contains($iconTitle, 'email') || str_contains($iconTitle, 'e-mail'))
                                    <x-svg.envelope class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                @elseif (str_contains($iconTitle, 'phone') || str_contains($iconTitle, 'tel') || str_contains($iconTitle, 'call'))
                                    <x-svg.phone-contact class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                @elseif (str_contains($iconTitle, 'address') || str_contains($iconTitle, 'location') || str_contains($iconTitle, 'office'))
                                    <x-svg.location-pin class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                @elseif (str_contains($iconTitle, 'hours') || str_contains($iconTitle, 'business') || str_contains($iconTitle, 'opening'))
                                    <x-svg.clock class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                @else 
                                    <x-svg.info-circle class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                @endif
                            </div>
                            <div class="ml-4 min-w-0"> 
                                <p class="text-gray-700 dark:text-gray-300 font-medium">{{ $info->title }}</p>
                                <div class="text-gray-900 dark:text-white [&_a]:text-blue-600 dark:[&_a]:text-blue-400 [&_a:hover]:underline break-words">
                                    {!! $info->content !!}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="space-y-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <x-svg.envelope class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div class="ml-4 min-w-0">
                            <p class="text-gray-700 dark:text-gray-300 font-medium">Email</p>
                            <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline break-words"><EMAIL></a>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <x-svg.phone-contact class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div class="ml-4 min-w-0">
                            <p class="text-gray-700 dark:text-gray-300 font-medium">Phone</p>
                            <div class="text-gray-900 dark:text-white break-words">
                                <a href="tel:+85244537120" class="text-blue-600 dark:text-blue-400 hover:underline">+852 4453 7120</a> /
                                <a href="tel:+447361884937" class="text-blue-600 dark:text-blue-400 hover:underline">+44 7361 884937</a>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <x-svg.location-pin class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div class="ml-4 min-w-0">
                            <p class="text-gray-700 dark:text-gray-300 font-medium">Address</p>
                            <p class="text-gray-900 dark:text-white break-words">
                                123 Tech Street<br>
                                75000 Paris<br>
                                France
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <x-svg.clock class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div class="ml-4 min-w-0">
                            <p class="text-gray-700 dark:text-gray-300 font-medium">Business Hours</p>
                            <p class="text-gray-900 dark:text-white break-words">
                                Monday - Friday: 9am to 5pm<br>
                                Saturday - Sunday: Closed
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="mt-10">
                <h4 class="font-semibold text-lg mb-4 text-gray-900 dark:text-white">Connect With Us</h4>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors">
                        <x-svg.social-linkedin class="h-6 w-6" />
                    </a>
                    <a href="#" class="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors">
                        <x-svg.social-twitter class="h-6 w-6" />
                    </a>
                    <a href="#" class="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors">
                        <x-svg.social-instagram class="h-6 w-6" />
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="lg:col-span-2">
        <div class="relative overflow-hidden bg-white dark:bg-gray-800 rounded-xl shadow-xl">
            <!-- Decorative top gradient bar -->
            <div class="h-2 bg-gradient-to-r from-indigo-500 to-purple-600"></div>

            <!-- Form content -->
            <div class="p-8">
                <div class="flex items-center mb-6">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-4">
                        <x-svg.message-chat class="h-5 w-5" />
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Send Us a Message</h3>
                </div>

                <div class="mb-6 p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-100 dark:border-indigo-800/50">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <x-svg.info-circle-filled class="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-indigo-700 dark:text-indigo-300">We aim to respond to all inquiries within 24 hours during business days. Please fill out the form below with your details.</p>
                        </div>
                    </div>
                </div>

                <form action="{{ route('contact.submit') }}" method="POST" class="space-y-6">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="name" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                <x-svg.form-user class="w-4 h-4 mr-2 text-indigo-500" />
                                Name
                            </label>
                            <div class="relative">
                                <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                    class="w-full pl-4 pr-10 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-transparent transition duration-200 @error('name') border-red-500 @enderror">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <x-svg.form-user-icon class="h-5 w-5 text-gray-400" />
                                </div>
                            </div>
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="space-y-2">
                            <label for="email" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                <x-svg.form-email class="w-4 h-4 mr-2 text-indigo-500" />
                                Email
                            </label>
                            <div class="relative">
                                <input type="email" id="email" name="email" value="{{ old('email') }}" required
                                    class="w-full pl-4 pr-10 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-transparent transition duration-200 @error('email') border-red-500 @enderror">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <x-svg.form-email-icon class="h-5 w-5 text-gray-400" />
                                </div>
                            </div>
                            @error('email')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="subject" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                            <x-svg.form-subject class="w-4 h-4 mr-2 text-indigo-500" />
                            Subject
                        </label>
                        <div class="relative">
                            <input type="text" id="subject" name="subject" value="{{ old('subject') }}" required
                                class="w-full pl-4 pr-10 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-transparent transition duration-200 @error('subject') border-red-500 @enderror">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <x-svg.form-subject-icon class="h-5 w-5 text-gray-400" />
                            </div>
                        </div>
                        @error('subject')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="space-y-2">
                        <label for="message" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                            <x-svg.form-message class="w-4 h-4 mr-2 text-indigo-500" />
                            Message
                        </label>
                        <textarea id="message" name="message" rows="6" required
                            class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-transparent transition duration-200 @error('message') border-red-500 @enderror">{{ old('message') }}</textarea>
                        @error('message')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="pt-4">
                        <button type="submit" class="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                            <div class="flex items-center justify-center">
                                <x-svg.send-paper-plane class="w-5 h-5 mr-2" />
                                Send Message
                            </div>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Map Section -->
<div class="mt-16">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
            <h3 class="text-xl font-bold">Find Us</h3>
            <p class="opacity-80">Visit our office at 123 Tech Street, 75000 Paris, France</p>
        </div>
        <div class="h-96 w-full">
            <!-- OpenStreetMap using Leaflet -->
            <div id="map" class="h-full w-full"></div>
        </div>
    </div>
</div>

<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the map
        var map = L.map('map').setView([48.8566, 2.3522], 13); // Paris coordinates

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add a marker for the office location
        var marker = L.marker([48.8566, 2.3522]).addTo(map);
        marker.bindPopup("<b>WisdomTechno</b><br>123 Tech Street<br>75000 Paris, France").openPopup();
    });
</script>

@endsection