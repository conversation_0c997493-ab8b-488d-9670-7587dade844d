<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        
        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Custom CSS for modern, eye-friendly design -->
        <link rel="stylesheet" href="{{ asset('css/custom.css') }}">

        <!-- Preload key assets -->
        <link rel="preload" href="{{ asset('img/grid-pattern.svg') }}" as="image" type="image/svg+xml">
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900 flex flex-col">
            @include('layouts.navigation')

            <!-- Notification Area -->
            <div class="fixed top-5 right-5 z-50 space-y-4 w-full max-w-sm">
                @if (session('success'))
                    <x-notification type="success" :message="session('success')" />
                @endif
                @if (session('error'))
                    <x-notification type="error" :message="session('error')" />
                @endif
                @if (session('warning'))
                    <x-notification type="warning" :message="session('warning')" />
                @endif
                @if (session('info'))
                    <x-notification type="info" :message="session('info')" />
                @endif
            </div>

            <!-- Page Heading -->
            @isset($header)
                <header class="bg-white dark:bg-gray-800 shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main class="flex-grow">
                @yield('content')
            </main>

            <!-- Footer -->
            <x-footer />
        </div>
    </body>
</html>
