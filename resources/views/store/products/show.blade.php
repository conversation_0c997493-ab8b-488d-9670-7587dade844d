<x-app-layout>
    <x-slot name="header">
        <!-- Add CSS and JS for enhanced product page -->
        <link rel="stylesheet" href="{{ asset('css/product-page.css') }}">
        <script src="{{ asset('js/product-page.js') }}" defer></script>
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ $product->getTranslation('name', app()->getLocale()) }}
            </h2>
            <div class="mt-2 sm:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('store.index') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-white">
                                <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                                </svg>
                                Store
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                                <a href="{{ route('categories.show', $product->category->getTranslation('slug', app()->getLocale())) }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                                    {{ $product->category->getTranslation('name', app()->getLocale()) }}
                                </a>
                            </div>
                        </li>
                        <li aria-current="page">
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                                    {{ $product->getTranslation('name', app()->getLocale()) }}
                                </span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </x-slot>

    @section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Product Images -->
                    <div class="product-gallery">
                        @php
                            $thumbnailUrl = '';
                            $images = [];

                            try {
                                $thumbnailUrl = $product->getFirstMediaUrl('thumbnail');
                                $images = $product->getMedia('images');
                            } catch (\Exception $e) {
                                // Silently handle the error
                            }
                        @endphp

                        <div class="mb-4 relative overflow-hidden rounded-lg product-image-container">
                            @if(!empty($thumbnailUrl))
                                <img src="{{ $thumbnailUrl }}" alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                    class="w-full h-96 object-cover rounded-lg main-product-image transition-transform duration-300 cursor-pointer"
                                    data-zoom-enabled="true">
                                <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-300 cursor-zoom-in zoom-overlay"></div>
                            @else
                                <div class="w-full h-96 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center rounded-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            @endif
                        </div>

                        @if(count($images) > 0)
                            <div class="grid grid-cols-4 gap-2 product-thumbnails">
                                @if(!empty($thumbnailUrl))
                                    <img src="{{ $thumbnailUrl }}" alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                        class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-75 hover:shadow-lg transition-all duration-200 product-thumbnail ring-2 ring-indigo-500"
                                        data-full-image="{{ $thumbnailUrl }}">
                                @endif

                                @foreach($images as $image)
                                    <img src="{{ $image->getUrl('thumb') }}" alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                        class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-75 hover:shadow-lg transition-all duration-200 product-thumbnail"
                                        data-full-image="{{ $image->getUrl() }}">
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Product Details -->
                    <div class="product-details">
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                            {{ $product->getTranslation('name', app()->getLocale()) }}
                        </h1>

                        <div class="mb-4 flex items-center justify-between">
                            <a href="{{ route('categories.show', $product->category->getTranslation('slug', app()->getLocale())) }}" class="text-sm text-indigo-600 dark:text-indigo-400 hover:underline">
                                {{ $product->category->getTranslation('name', app()->getLocale()) }}
                            </a>

                            <!-- Social Sharing -->
                            <div class="flex space-x-2">
                                <button type="button" class="social-share-btn" data-platform="facebook" aria-label="Share on Facebook">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <button type="button" class="social-share-btn" data-platform="twitter" aria-label="Share on Twitter">
                                    <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                    </svg>
                                </button>
                                <button type="button" class="social-share-btn" data-platform="pinterest" aria-label="Share on Pinterest">
                                    <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M12 0a12 12 0 00-4.373 23.182c-.067-.593-.132-1.505.028-2.153.145-.587.933-3.739.933-3.739s-.238-.477-.238-1.182c0-1.107.642-1.933 1.438-1.933.678 0 1.006.509 1.006 1.119 0 .681-.434 1.7-.659 2.644-.188.79.398 1.435 1.18 1.435 1.416 0 2.502-1.493 2.502-3.646 0-1.907-1.37-3.24-3.329-3.24-2.266 0-3.591 1.693-3.591 3.44 0 .68.26 1.409.587 1.805.065.08.075.149.055.228-.06.252-.196.796-.222.907-.035.146-.116.177-.268.107-1-.465-1.624-1.926-1.624-3.1 0-2.523 1.834-4.84 5.287-4.84 2.775 0 4.932 1.977 4.932 4.62 0 2.757-1.739 4.976-4.151 4.976-.811 0-1.573-.421-1.834-.919l-.498 1.902c-.181.695-.669 1.566-.997 2.097A12 12 0 1012 0z"></path>
                                    </svg>
                                </button>
                                <button type="button" class="social-share-btn" data-platform="email" aria-label="Share via Email">
                                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="mb-6">
                            <p class="text-gray-700 dark:text-gray-300">
                                {{ $product->getTranslation('description', app()->getLocale()) }}
                            </p>
                        </div>

                        @if($product->variants->count() > 0)
                            @php
                                $firstVariant = $product->variants->first();
                                $minPrice = $product->variants->min('price');
                                $maxPrice = $product->variants->max('price');
                            @endphp

                            <div class="mb-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-2xl font-bold text-gray-900 dark:text-white product-price">
                                            @if($minPrice === $maxPrice)
                                                ${{ number_format($minPrice, 2) }}
                                            @else
                                                ${{ number_format($minPrice, 2) }} - ${{ number_format($maxPrice, 2) }}
                                            @endif
                                        </span>

                                        @if($firstVariant->compare_at_price && $firstVariant->compare_at_price > $firstVariant->price)
                                            <span class="ml-2 text-lg text-gray-500 line-through product-compare-price">
                                                ${{ number_format($firstVariant->compare_at_price, 2) }}
                                            </span>
                                            <span class="ml-2 text-sm font-medium text-green-600 product-discount">
                                                {{ round((1 - $firstVariant->price / $firstVariant->compare_at_price) * 100) }}% off
                                            </span>
                                        @endif
                                    </div>

                                    <button type="button" class="wishlist-button p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" aria-label="Add to wishlist">
                                        <svg class="w-6 h-6 text-gray-400 hover:text-red-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                    </button>
                                </div>

                                <div class="mt-2">
                                    <span class="stock-status text-green-600">In stock</span>
                                </div>
                            </div>

                            <form action="{{ route('cart.add') }}" method="POST" class="space-y-6 add-to-cart-form" data-checkout-url="{{ route('checkout.index') }}">
                                @csrf

                                @php
                                    // Extract unique attributes from variants
                                    $attributeTypes = [];
                                    $variantAttributes = [];

                                    foreach ($product->variants as $variant) {
                                        if (!empty($variant->attributes)) {
                                            foreach ($variant->attributes as $key => $value) {
                                                if (!isset($attributeTypes[$key])) {
                                                    $attributeTypes[$key] = [];
                                                }
                                                if (!in_array($value, $attributeTypes[$key])) {
                                                    $attributeTypes[$key][] = $value;
                                                }
                                            }
                                            $variantAttributes[$variant->id] = $variant->attributes;
                                        }
                                    }

                                    // Sort attribute types
                                    ksort($attributeTypes);
                                @endphp

                                @php
                                    // Check if this is a default variant product (no attributes)
                                    $hasAttributes = count($attributeTypes) > 0;
                                    $isDefaultVariantProduct = !$hasAttributes && $product->variants->count() > 0;
                                @endphp

                                <!-- Hidden select for form submission -->
                                <div class="hidden">
                                    <select name="product_variant_id" id="product_variant_id" {{ $isDefaultVariantProduct ? 'data-default-variant="true"' : '' }}>
                                        <option value="">Select a variant</option>
                                        @foreach($product->variants as $variant)
                                            @php
                                                $isAvailable = true;
                                                $stockMessage = '';
                                                $availableStock = 0;

                                                if ($variant->inventoryItem && $variant->inventoryItem->track_inventory) {
                                                    $availableStock = $variant->inventoryItem->quantity_on_hand - $variant->inventoryItem->quantity_reserved;

                                                    if ($availableStock <= 0 && !$variant->inventoryItem->allow_backorder) {
                                                        $isAvailable = false;
                                                        $stockMessage = 'Out of stock';
                                                    } elseif ($availableStock <= 5) {
                                                        $stockMessage = "Only {$availableStock} left";
                                                    }
                                                }
                                            @endphp

                                            <option value="{{ $variant->id }}"
                                                {{ !$isAvailable ? 'disabled' : '' }}
                                                data-price="{{ number_format($variant->price, 2) }}"
                                                data-compare-price="{{ $variant->compare_at_price ? number_format($variant->compare_at_price, 2) : '' }}"
                                                data-available="{{ $isAvailable ? 'true' : 'false' }}"
                                                data-stock-message="{{ $stockMessage }}"
                                                data-stock-quantity="{{ $availableStock }}"
                                                data-attributes="{{ json_encode($variant->attributes ?? []) }}">
                                                {{ $variant->getTranslation('name', app()->getLocale()) ?? 'Default' }} -
                                                ${{ number_format($variant->price, 2) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Visual variant selection -->
                                @if(count($attributeTypes) > 0)
                                    <div class="space-y-4 variant-selection">
                                        @foreach($attributeTypes as $attributeName => $attributeValues)
                                            <div class="variant-attribute" data-attribute="{{ $attributeName }}">
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                    {{ ucfirst($attributeName) }}
                                                </label>

                                                @if($attributeName == 'color')
                                                    <!-- Color swatches -->
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach($attributeValues as $colorValue)
                                                            @php
                                                                // Convert color names to hex codes for common colors
                                                                $colorMap = [
                                                                    'red' => '#EF4444',
                                                                    'blue' => '#3B82F6',
                                                                    'green' => '#10B981',
                                                                    'yellow' => '#FBBF24',
                                                                    'purple' => '#8B5CF6',
                                                                    'pink' => '#EC4899',
                                                                    'indigo' => '#6366F1',
                                                                    'black' => '#000000',
                                                                    'white' => '#FFFFFF',
                                                                    'gray' => '#6B7280',
                                                                    'orange' => '#F97316',
                                                                    'brown' => '#92400E',
                                                                    'teal' => '#14B8A6',
                                                                    'cyan' => '#06B6D4',
                                                                    'lime' => '#84CC16',
                                                                    'amber' => '#F59E0B',
                                                                ];

                                                                $colorHex = strtolower($colorValue);
                                                                if (isset($colorMap[$colorHex])) {
                                                                    $colorHex = $colorMap[$colorHex];
                                                                } elseif (!preg_match('/^#[a-f0-9]{6}$/i', $colorHex)) {
                                                                    $colorHex = '#CCCCCC'; // Default gray for unknown colors
                                                                }

                                                                $textColor = (strtolower($colorValue) == 'white' || strtolower($colorValue) == '#ffffff') ? 'text-gray-800' : 'text-white';
                                                            @endphp

                                                            <button type="button"
                                                                class="color-swatch w-10 h-10 rounded-full border-2 border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
                                                                style="background-color: {{ $colorHex }};"
                                                                data-attribute-name="{{ $attributeName }}"
                                                                data-attribute-value="{{ $colorValue }}"
                                                                title="{{ ucfirst($colorValue) }}">
                                                                <span class="sr-only">{{ ucfirst($colorValue) }}</span>
                                                                <span class="color-swatch-check hidden absolute inset-0 flex items-center justify-center {{ $textColor }}">
                                                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                    </svg>
                                                                </span>
                                                            </button>
                                                        @endforeach
                                                    </div>
                                                @elseif($attributeName == 'size')
                                                    <!-- Size buttons -->
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach($attributeValues as $sizeValue)
                                                            <button type="button"
                                                                class="size-button min-w-[3rem] h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-all duration-200"
                                                                data-attribute-name="{{ $attributeName }}"
                                                                data-attribute-value="{{ $sizeValue }}">
                                                                {{ strtoupper($sizeValue) }}
                                                            </button>
                                                        @endforeach
                                                    </div>
                                                @else
                                                    <!-- Other attributes as buttons -->
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach($attributeValues as $attrValue)
                                                            <button type="button"
                                                                class="attribute-button px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-all duration-200"
                                                                data-attribute-name="{{ $attributeName }}"
                                                                data-attribute-value="{{ $attrValue }}">
                                                                {{ ucfirst($attrValue) }}
                                                            </button>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- Variant not available message -->
                                    <div class="mt-2 hidden text-red-600 dark:text-red-400 text-sm variant-not-available">
                                        The selected combination is not available.
                                    </div>
                                @else
                                    <!-- Fallback to dropdown if no attributes -->
                                    <div>
                                        <label for="variant_selector" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Select Variant
                                        </label>
                                        <select id="variant_selector"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <option value="">Select an option</option>
                                            @foreach($product->variants as $variant)
                                                @php
                                                    $isAvailable = true;
                                                    $stockMessage = '';

                                                    if ($variant->inventoryItem && $variant->inventoryItem->track_inventory) {
                                                        $availableStock = $variant->inventoryItem->quantity_on_hand - $variant->inventoryItem->quantity_reserved;

                                                        if ($availableStock <= 0 && !$variant->inventoryItem->allow_backorder) {
                                                            $isAvailable = false;
                                                            $stockMessage = 'Out of stock';
                                                        } elseif ($availableStock <= 5) {
                                                            $stockMessage = "Only {$availableStock} left";
                                                        }
                                                    }
                                                @endphp

                                                <option value="{{ $variant->id }}" {{ !$isAvailable ? 'disabled' : '' }}>
                                                    {{ $variant->getTranslation('name', app()->getLocale()) ?? 'Default' }} -
                                                    ${{ number_format($variant->price, 2) }}
                                                    {{ !$isAvailable ? '(Out of stock)' : '' }}
                                                    {{ $stockMessage && $isAvailable ? "({$stockMessage})" : '' }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                @endif

                                <div>
                                    <label for="quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Quantity
                                    </label>
                                    <div class="flex rounded-md shadow-sm">
                                        <button type="button" class="quantity-btn minus-btn px-3 py-2 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                            </svg>
                                        </button>
                                        <input type="number" name="quantity" id="quantity" min="1" max="100" value="1" required
                                            class="flex-1 min-w-0 block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-center">
                                        <button type="button" class="quantity-btn plus-btn px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-700 rounded-r-md bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <button type="submit" class="flex-1 bg-indigo-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-all duration-200 add-to-cart-button relative overflow-hidden group">
                                        <span class="absolute inset-0 w-full h-full transition duration-200 ease-out transform translate-x-full bg-indigo-700 group-hover:translate-x-0"></span>
                                        <span class="relative flex items-center justify-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                            </svg>
                                            Add to Cart
                                        </span>
                                    </button>
                                    <button type="button" class="buy-now-button bg-green-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 transition-all duration-200 relative overflow-hidden group">
                                        <span class="absolute inset-0 w-full h-full transition duration-200 ease-out transform translate-x-full bg-green-700 group-hover:translate-x-0"></span>
                                        <span class="relative flex items-center justify-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Buy Now
                                        </span>
                                    </button>
                                </div>
                            </form>
                        @else
                            <div class="p-4 bg-yellow-50 dark:bg-yellow-900/30 rounded-md">
                                <p class="text-yellow-800 dark:text-yellow-200">
                                    This product is currently unavailable.
                                </p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Related Products -->
                @if($relatedProducts->count() > 0)
                    <div class="mt-16">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">
                            Related Products
                        </h2>

                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            @foreach($relatedProducts as $relatedProduct)
                                <div class="bg-white dark:bg-gray-700 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <a href="{{ route('products.show', $relatedProduct->getTranslation('slug', app()->getLocale())) }}">
                                        @php
                                            $thumbnailUrl = '';
                                            try {
                                                $thumbnailUrl = $relatedProduct->getFirstMediaUrl('thumbnail');
                                            } catch (\Exception $e) {
                                                // Silently handle the error
                                            }
                                        @endphp

                                        @if(!empty($thumbnailUrl))
                                            <img src="{{ $thumbnailUrl }}" alt="{{ $relatedProduct->getTranslation('name', app()->getLocale()) }}" class="w-full h-48 object-cover">
                                        @else
                                            <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        @endif
                                    </a>

                                    <div class="p-4">
                                        <a href="{{ route('products.show', $relatedProduct->getTranslation('slug', app()->getLocale())) }}" class="block">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200">
                                                {{ $relatedProduct->getTranslation('name', app()->getLocale()) }}
                                            </h3>
                                        </a>

                                        @if($relatedProduct->variants->count() > 0)
                                            @php
                                                $minPrice = $relatedProduct->variants->min('price');
                                                $maxPrice = $relatedProduct->variants->max('price');
                                            @endphp

                                            <div class="flex items-center justify-between">
                                                <div>
                                                    @if($minPrice === $maxPrice)
                                                        <span class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($minPrice, 2) }}</span>
                                                    @else
                                                        <span class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($minPrice, 2) }} - ${{ number_format($maxPrice, 2) }}</span>
                                                    @endif
                                                </div>

                                                <a href="{{ route('products.show', $relatedProduct->getTranslation('slug', app()->getLocale())) }}" class="inline-flex items-center px-3 py-1 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                                    View
                                                </a>
                                            </div>
                                        @else
                                            <div class="text-sm text-gray-500 dark:text-gray-400 italic">
                                                Currently unavailable
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Product Reviews Section -->
                <div class="mt-16">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">
                        Customer Reviews
                    </h2>

                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <div class="flex items-center">
                                @for ($i = 1; $i <= 5; $i++)
                                    <svg class="w-5 h-5 {{ $i <= 4 ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                @endfor
                            </div>
                            <p class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-200">4.0 out of 5</p>
                            <span class="w-1 h-1 mx-2 bg-gray-500 rounded-full"></span>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Based on 24 reviews</p>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">5 star</span>
                                <div class="w-full h-4 mx-2 bg-gray-200 dark:bg-gray-700 rounded">
                                    <div class="h-4 bg-yellow-400 rounded" style="width: 70%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12 text-right">70%</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">4 star</span>
                                <div class="w-full h-4 mx-2 bg-gray-200 dark:bg-gray-700 rounded">
                                    <div class="h-4 bg-yellow-400 rounded" style="width: 17%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12 text-right">17%</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">3 star</span>
                                <div class="w-full h-4 mx-2 bg-gray-200 dark:bg-gray-700 rounded">
                                    <div class="h-4 bg-yellow-400 rounded" style="width: 8%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12 text-right">8%</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">2 star</span>
                                <div class="w-full h-4 mx-2 bg-gray-200 dark:bg-gray-700 rounded">
                                    <div class="h-4 bg-yellow-400 rounded" style="width: 3%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12 text-right">3%</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">1 star</span>
                                <div class="w-full h-4 mx-2 bg-gray-200 dark:bg-gray-700 rounded">
                                    <div class="h-4 bg-yellow-400 rounded" style="width: 2%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12 text-right">2%</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-8">
                        <button type="button" class="write-review-button inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
                            Write a Review
                        </button>
                    </div>

                    <div class="space-y-8">
                        <!-- Sample Reviews -->
                        <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <svg class="w-5 h-5 {{ $i <= 5 ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                </div>
                                <h3 class="ml-2 text-sm font-semibold text-gray-900 dark:text-white">Excellent product!</h3>
                            </div>
                            <div class="flex items-center mb-2">
                                <p class="text-sm text-gray-500 dark:text-gray-400">John D. | <span>Verified Purchase</span></p>
                                <span class="w-1 h-1 mx-2 bg-gray-500 rounded-full"></span>
                                <p class="text-sm text-gray-500 dark:text-gray-400">2 months ago</p>
                            </div>
                            <p class="text-gray-700 dark:text-gray-300">This product exceeded my expectations. The quality is outstanding and it works exactly as described. I would definitely recommend it to anyone looking for a reliable solution.</p>
                        </div>

                        <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <svg class="w-5 h-5 {{ $i <= 4 ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                </div>
                                <h3 class="ml-2 text-sm font-semibold text-gray-900 dark:text-white">Great value for money</h3>
                            </div>
                            <div class="flex items-center mb-2">
                                <p class="text-sm text-gray-500 dark:text-gray-400">Sarah M. | <span>Verified Purchase</span></p>
                                <span class="w-1 h-1 mx-2 bg-gray-500 rounded-full"></span>
                                <p class="text-sm text-gray-500 dark:text-gray-400">1 month ago</p>
                            </div>
                            <p class="text-gray-700 dark:text-gray-300">I'm very happy with my purchase. The product is well-made and durable. Shipping was fast and the packaging was secure. Would buy from this store again.</p>
                        </div>

                        <div class="pb-8">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <svg class="w-5 h-5 {{ $i <= 3 ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                </div>
                                <h3 class="ml-2 text-sm font-semibold text-gray-900 dark:text-white">Good but could be better</h3>
                            </div>
                            <div class="flex items-center mb-2">
                                <p class="text-sm text-gray-500 dark:text-gray-400">Michael T. | <span>Verified Purchase</span></p>
                                <span class="w-1 h-1 mx-2 bg-gray-500 rounded-full"></span>
                                <p class="text-sm text-gray-500 dark:text-gray-400">3 weeks ago</p>
                            </div>
                            <p class="text-gray-700 dark:text-gray-300">The product works as advertised, but I found the instructions a bit confusing. Once I figured it out, it worked well. Three stars because the quality is good but the user experience could be improved.</p>
                        </div>
                    </div>

                    <div class="mt-8 flex justify-center">
                        <button type="button" class="load-more-reviews-button inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
                            Load More Reviews
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-app-layout>
