<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Payment') }}
        </h2>
    </x-slot>

    @section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Order Summary -->
                        <div class="md:col-span-1">
                            <h3 class="text-lg font-semibold mb-4">{{ __('Order Summary') }}</h3>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="mb-2"><span class="font-medium">{{ __('Order Number') }}:</span> {{ $order->order_number }}</p>
                                <p class="mb-2"><span class="font-medium">{{ __('Date') }}:</span> {{ $order->created_at->format('M d, Y') }}</p>
                                <p class="mb-4"><span class="font-medium">{{ __('Status') }}:</span>
                                    <span class="px-2 py-1 text-xs rounded-full
                                        @if($order->status === 'pending') bg-yellow-200 text-yellow-800
                                        @elseif($order->status === 'processing') bg-blue-200 text-blue-800
                                        @elseif($order->status === 'shipped') bg-indigo-200 text-indigo-800
                                        @elseif($order->status === 'delivered') bg-green-200 text-green-800
                                        @elseif($order->status === 'cancelled') bg-red-200 text-red-800
                                        @elseif($order->status === 'refunded') bg-gray-200 text-gray-800
                                        @endif">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </p>

                                <div class="border-t border-gray-300 dark:border-gray-600 pt-4 mb-4">
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Subtotal') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->subtotal, 2) }}</span>
                                    </p>
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Shipping') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->shipping_cost, 2) }}</span>
                                    </p>
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Tax') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->tax_amount, 2) }}</span>
                                    </p>
                                    @if($order->discount_amount > 0)
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Discount') }}:</span>
                                        <span>-{{ $order->currency }} {{ number_format($order->discount_amount, 2) }}</span>
                                    </p>
                                    @endif
                                    <p class="flex justify-between font-bold mt-2 pt-2 border-t border-gray-300 dark:border-gray-600">
                                        <span>{{ __('Total') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->total, 2) }}</span>
                                    </p>
                                </div>

                                <div class="text-sm">
                                    <p class="mb-1"><span class="font-medium">{{ __('Shipping Address') }}:</span></p>
                                    <p class="mb-4">
                                        {{ $order->shippingAddress->address_line1 }}<br>
                                        @if($order->shippingAddress->address_line2)
                                            {{ $order->shippingAddress->address_line2 }}<br>
                                        @endif
                                        {{ $order->shippingAddress->city }},
                                        @if($order->shippingAddress->region)
                                            {{ $order->shippingAddress->region }},
                                        @endif
                                        {{ $order->shippingAddress->postal_code }}<br>
                                        {{ $order->shippingAddress->country }}
                                    </p>

                                    <p class="mb-1"><span class="font-medium">{{ __('Billing Address') }}:</span></p>
                                    <p>
                                        {{ $order->billingAddress->address_line1 }}<br>
                                        @if($order->billingAddress->address_line2)
                                            {{ $order->billingAddress->address_line2 }}<br>
                                        @endif
                                        {{ $order->billingAddress->city }},
                                        @if($order->billingAddress->region)
                                            {{ $order->billingAddress->region }},
                                        @endif
                                        {{ $order->billingAddress->postal_code }}<br>
                                        {{ $order->billingAddress->country }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-semibold mb-4">{{ __('Select Payment Method') }}</h3>

                            <form action="{{ route('payments.select-method', $order->order_number) }}" method="POST">
                                @csrf
                                <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-6">
                                    <div class="space-y-4">
                                        @foreach($paymentMethods as $key => $method)
                                        <div class="payment-method-option border border-gray-300 dark:border-gray-600 rounded-lg p-4 cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-400 transition-colors"
                                             data-method="{{ $key }}">
                                            <div class="flex items-center">
                                                <input type="radio" name="payment_method" id="payment_{{ $key }}" value="{{ $key }}" class="mr-3 h-4 w-4 text-indigo-600">
                                                <label for="payment_{{ $key }}" class="flex-1 cursor-pointer">
                                                    <div class="flex items-center">
                                                        <i class="fas {{ $method['icon'] }} mr-2 text-gray-600 dark:text-gray-300"></i>
                                                        <span class="font-medium">{{ $method['name'] }}</span>
                                                    </div>
                                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $method['description'] }}</p>
                                                </label>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                        {{ __('Continue to Payment') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Variables
            const paymentMethodOptions = document.querySelectorAll('.payment-method-option');

            // Event Listeners
            paymentMethodOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const radio = this.querySelector('input[type="radio"]');

                    // Update radio button
                    document.querySelectorAll('input[name="payment_method"]').forEach(input => {
                        input.checked = false;
                    });
                    radio.checked = true;

                    // Update UI
                    paymentMethodOptions.forEach(opt => {
                        opt.classList.remove('border-indigo-500', 'dark:border-indigo-400');
                        opt.classList.add('border-gray-300', 'dark:border-gray-600');
                    });
                    this.classList.remove('border-gray-300', 'dark:border-gray-600');
                    this.classList.add('border-indigo-500', 'dark:border-indigo-400');
                });
            });

            // Select first payment method by default
            if (paymentMethodOptions.length > 0) {
                paymentMethodOptions[0].click();
            }
        });
    </script>
    @endpush
</x-app-layout>
