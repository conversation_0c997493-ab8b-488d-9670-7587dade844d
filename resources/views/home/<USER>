@extends('layouts.app')

@section('content')
    <!-- Hero Section with Modern Design -->
    <div class="relative bg-gradient-to-r from-blue-900 via-indigo-800 to-purple-900 text-white overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20" aria-hidden="true"></div>
            <div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 lg:py-40">
            <div class="md:w-2/3">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 leading-tight">
                    <span class="block">{{ ContentBlock::get('home.hero.title_line1', 'Transform Your Business') }}</span>
                    <span class="block text-blue-300">{{ ContentBlock::get('home.hero.title_line2', 'with Advanced IT Solutions') }}</span>
                </h1>

                <p class="text-xl text-blue-100 mb-8 max-w-2xl">
                    {{ ContentBlock::get('home.hero.subtitle', 'Leverage our expertise in development, security, and innovation to drive your business forward. We provide comprehensive IT services tailored to your needs.') }}
                </p>

                <div class="flex flex-wrap gap-4">
                    <a href="#our-services" class="px-8 py-3 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        Get Started
                    </a>
                    <a href="{{ route('about') }}" class="px-8 py-3 bg-transparent hover:bg-white/10 border-2 border-white text-white font-medium rounded-lg transition duration-300">
                        Learn more
                    </a>
                </div>
            </div>
        </div>

        <!-- Decorative elements -->
        <div class="absolute top-1/4 right-0 w-64 h-64 bg-blue-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20" aria-hidden="true"></div>
        <div class="absolute bottom-1/3 left-0 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20" aria-hidden="true"></div>
    </div>

    <!-- Why Choose Us Section -->
    <div id="why-choose-us" class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    {{ ContentBlock::get('home.why_choose_us.title', 'Why Choose Us') }}
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    {{ ContentBlock::get('home.why_choose_us.subtitle', 'We combine technical expertise with business insight to deliver solutions that drive real results.') }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition duration-500 hover:scale-105">
                    <div class="text-blue-600 mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Secure Solutions</h3>
                    <p class="text-gray-600 dark:text-gray-300">We prioritize security in all our solutions, ensuring your data and systems are protected against threats.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition duration-500 hover:scale-105">
                    <div class="text-blue-600 mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Fast Performance</h3>
                    <p class="text-gray-600 dark:text-gray-300">Our solutions are optimized for speed and efficiency, ensuring your business operates at peak performance.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 transform transition duration-500 hover:scale-105">
                    <div class="text-blue-600 mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Expert Support</h3>
                    <p class="text-gray-600 dark:text-gray-300">Our team of experts is always available to provide support and guidance whenever you need it.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- About Us Teaser Section -->
    <div class="py-20 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center gap-8 md:gap-12">
                <div class="w-full md:w-1/2 lg:w-2/5">
                    <img src="{{ asset('img/main_image.webp') }}" alt="WisdomTechno - Our Commitment" class="rounded-xl shadow-xl w-full h-auto object-cover max-h-96">
                </div>
                <div class="w-full md:w-1/2 lg:w-3/5">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {{ ContentBlock::get('home.about_teaser.title', 'Your Trusted Partner in Technology') }}
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 mb-6">
                        {{ ContentBlock::get('home.about_teaser.subtitle', 'At WisdomTechno, we are passionate about leveraging technology to solve complex challenges and drive business success. Our team is dedicated to providing innovative and reliable IT solutions tailored to your unique needs.') }}
                    </p>
                    <a href="{{ route('about') }}" class="inline-block px-8 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        Learn More About Us
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Services Section -->
    <div id="our-services" class="py-20 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    {{ ContentBlock::get('home.featured_services.title', 'Our Core Services') }}
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    {{ ContentBlock::get('home.featured_services.subtitle', 'Explore our range of expert IT solutions designed to elevate your business.') }}
                </p>
            </div>

            @if(isset($featuredServices) && $featuredServices->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($featuredServices as $service)
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden transform transition duration-500 hover:scale-105 hover:shadow-2xl">
                            <div class="h-3 bg-gradient-to-r from-teal-400 to-blue-500"></div>
                             @php
                                $serviceThumbnailUrl = '';
                                try { $serviceThumbnailUrl = $service->getFirstMediaUrl('thumbnail'); } catch (\Exception $e) {}
                            @endphp
                            @if(!empty($serviceThumbnailUrl))
                                <img src="{{ $serviceThumbnailUrl }}" alt="{{ $service->getTranslation('name', app()->getLocale()) }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center">
                                    @if($service->icon)
                                        <i class="{{ $service->icon }} text-5xl text-indigo-500 dark:text-indigo-400"></i>
                                    @else
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    @endif
                                </div>
                            @endif
                            <div class="p-6">
                                <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">{{ $service->getTranslation('name', app()->getLocale()) }}</h3>
                                <p class="text-gray-600 dark:text-gray-300 mb-5 line-clamp-3">{{ Str::limit($service->getTranslation('description', app()->getLocale()), 120) }}</p>
                                <a href="{{ route('services.show', $service->getTranslation('slug', app()->getLocale())) }}" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium inline-flex items-center">
                                    Learn more
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
                 <div class="mt-12 text-center">
                    <a href="{{ route('services.index') }}" class="px-8 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        View All Services
                    </a>
                </div>
            @else
                <p class="text-center text-gray-600 dark:text-gray-400">Our services will be listed here soon. Please check back later.</p>
            @endif
        </div>
    </div>

    <!-- Featured Products Section -->
    @if(isset($featuredProducts) && $featuredProducts->count() > 0)
    <div class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    {{ ContentBlock::get('home.featured_products.title', 'Featured Products') }}
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    {{ ContentBlock::get('home.featured_products.subtitle', 'Check out some of our top products available in our store.') }}
                </p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @foreach($featuredProducts as $product)
                    <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                        <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}">
                            @php
                                $productThumbnailUrl = '';
                                try { $productThumbnailUrl = $product->getFirstMediaUrl('thumbnail'); } catch (\Exception $e) {}
                            @endphp
                            @if(!empty($productThumbnailUrl))
                                <img src="{{ $productThumbnailUrl }}" alt="{{ $product->getTranslation('name', app()->getLocale()) }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                                </div>
                            @endif
                        </a>
                        <div class="p-4">
                            <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}" class="block">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 line-clamp-2">{{ $product->getTranslation('name', app()->getLocale()) }}</h3>
                            </a>
                            @if($product->variants->count() > 0)
                                <span class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($product->variants->min('price'), 2) }}</span>
                            @endif
                            <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}" class="mt-3 block w-full text-center px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white font-medium rounded-lg transition duration-300 text-sm">View Product</a>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="mt-12 text-center">
                <a href="{{ route('products.index') }}" class="px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    Visit Our Store
                </a>
            </div>
        </div>
    </div>
    @endif

    <!-- CTA Section with Modern Design -->
    <div class="py-20 bg-gradient-to-br from-blue-600 to-indigo-700 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-10 md:p-16 shadow-2xl relative overflow-hidden">
                <!-- Decorative elements -->
                <div class="absolute top-0 right-0 -mt-10 -mr-10 w-40 h-40 bg-blue-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20" aria-hidden="true"></div>
                <div class="absolute bottom-0 left-0 -mb-10 -ml-10 w-40 h-40 bg-purple-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20" aria-hidden="true"></div>

                <div class="relative z-10 text-center md:text-left">
                    <!-- Text/CTA Column -->
                    <div class="w-full">
                        {{-- Consider md:w-2/3 lg:w-1/2 mx-auto md:mx-0 if you want to constrain width on larger screens --}}
                        <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ ContentBlock::get('home.cta.title', 'Need IT Support?') }}</h2>
                        <p class="text-xl text-blue-100 mb-8">{{ ContentBlock::get('home.cta.subtitle', 'Get in touch with our experts for immediate assistance with your IT needs. We\'re here to help you optimize and secure your technology infrastructure.') }}</p>
                        <div class="flex flex-wrap gap-4">
                            <a href="{{ route('inquiries.create') }}" class="px-8 py-3 bg-white text-blue-600 hover:bg-blue-50 font-medium rounded-lg transition duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                Contact Us Now
                            </a>
                            <a href="tel:{{ ContentBlock::get('site.contact.phone', '+15551234567') }}" class="px-8 py-3 bg-transparent hover:bg-white/10 border-2 border-white text-white font-medium rounded-lg transition duration-300">
                                Call Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
