<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    @section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Section -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Welcome back, :name!', ['name' => Auth::user()->first_name]) }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Here\'s an overview of your account') }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                <strong>Role:</strong> {{ Auth::user()->role }}
                                <strong>Is Admin:</strong> {{ Auth::user()->isAdmin() ? 'Yes' : 'No' }}
                                <strong>Email:</strong> {{ Auth::user()->email }}
                            </p>

                            @if(Auth::user()->isAdmin())
                                <div class="mt-4 p-4 bg-indigo-50 dark:bg-indigo-900/30 rounded-lg border border-indigo-200 dark:border-indigo-800">
                                    <p class="text-sm font-medium text-indigo-700 dark:text-indigo-300 mb-2">
                                        {{ __('You have admin privileges!') }}
                                    </p>
                                    <a href="{{ route('admin.dashboard') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                        {{ __('Go to Admin Dashboard') }}
                                    </a>
                                </div>
                            @endif
                        </div>
                        <div class="hidden sm:block">
                            <a href="{{ route('profile.edit') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                {{ __('Edit Profile') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Account Summary -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="h-2 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="h-10 w-10 rounded-md bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Account Summary') }}</h3>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Name') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ Auth::user()->name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Email') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ Auth::user()->email }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Phone') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ Auth::user()->phone_number ?? __('Not provided') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Account Status') }}</span>
                                <span class="text-sm font-medium {{ Auth::user()->is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                    {{ Auth::user()->is_active ? __('Active') : __('Inactive') }}
                                </span>
                            </div>
                        </div>
                        <div class="mt-6">
                            <a href="{{ route('profile.edit') }}" class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300">
                                {{ __('Update Profile Information') }} &rarr;
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="h-2 bg-gradient-to-r from-green-500 to-emerald-600"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="h-10 w-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Recent Orders') }}</h3>
                        </div>

                        @if(Auth::user()->orders->count() > 0)
                            <div class="space-y-3">
                                @foreach(Auth::user()->orders->sortByDesc('created_at')->take(3) as $order)
                                    <a href="{{ route('orders.show', $order) }}" class="block hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150">
                                        <div class="flex justify-between items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Order #:number', ['number' => $order->order_number]) }}</p>
                                                <p class="text-xs text-gray-600 dark:text-gray-400">{{ $order->created_at->format('M d, Y') }}</p>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="px-2 py-1 text-xs rounded-full
                                                    @if($order->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                                    @elseif($order->status === 'processing') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                                    @elseif($order->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300
                                                    @endif">
                                                    {{ ucfirst($order->status) }}
                                                </span>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                            <div class="mt-6">
                                <a href="{{ route('orders.index') }}" class="text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                    {{ __('View All Orders') }} &rarr;
                                </a>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('You haven\'t placed any orders yet.') }}</p>
                                <a href="{{ route('services.index') }}" class="mt-2 inline-block text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                    {{ __('Browse Services') }} &rarr;
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Recent Inquiries -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="h-2 bg-gradient-to-r from-purple-500 to-pink-600"></div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="h-10 w-10 rounded-md bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Recent Inquiries') }}</h3>
                        </div>

                        @if(Auth::user()->inquiries->count() > 0)
                            <div class="space-y-3">
                                @foreach(Auth::user()->inquiries->sortByDesc('created_at')->take(3) as $inquiry)
                                    <a href="{{ route('user.inquiries') }}" class="block hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150">
                                        <div class="flex justify-between items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ Str::limit($inquiry->message, 30) }}</p>
                                                <p class="text-xs text-gray-600 dark:text-gray-400">{{ $inquiry->created_at->format('M d, Y') }}</p>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="px-2 py-1 text-xs rounded-full
                                                    @if($inquiry->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                                    @elseif($inquiry->status === 'in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                                    @elseif($inquiry->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300
                                                    @endif">
                                                    {{ ucfirst(str_replace('_', ' ', $inquiry->status)) }}
                                                </span>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                            <div class="mt-6">
                                <a href="{{ route('user.inquiries') }}" class="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">
                                    {{ __('View All Inquiries') }} &rarr;
                                </a>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('You haven\'t submitted any inquiries yet.') }}</p>
                                <a href="{{ route('inquiries.create') }}" class="mt-2 inline-block text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">
                                    {{ __('Submit an Inquiry') }} &rarr;
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recommended Services -->
            <div class="mt-6">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="h-2 bg-gradient-to-r from-blue-500 to-cyan-600"></div>
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Recommended Services') }}</h3>
                            </div>
                            <a href="{{ route('services.index') }}" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                {{ __('View All') }} &rarr;
                            </a>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @php
                                $featuredServices = \App\Models\Service::where('is_featured', true)->take(3)->get();
                            @endphp

                            @if($featuredServices->count() > 0)
                                @foreach($featuredServices as $service)
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                                        @php
                                            $thumbnailUrl = '';
                                            try {
                                                $thumbnailUrl = $service->getFirstMediaUrl('thumbnail');
                                            } catch (\Exception $e) {
                                                // Silently handle the error
                                            }
                                        @endphp

                                        @if(!empty($thumbnailUrl))
                                            <img src="{{ $thumbnailUrl }}" alt="{{ $service->getTranslation('name', app()->getLocale()) }}" class="w-full h-32 object-cover">
                                        @else
                                            <div class="w-full h-32 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900 dark:to-cyan-900 flex items-center justify-center">
                                                @if($service->icon)
                                                    <i class="{{ $service->icon }} text-4xl text-blue-500 dark:text-blue-400"></i>
                                                @else
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                    </svg>
                                                @endif
                                            </div>
                                        @endif

                                        <div class="p-4">
                                            <h4 class="font-medium text-gray-900 dark:text-white mb-2">{{ $service->getTranslation('name', app()->getLocale()) }}</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-4">
                                                {{ Str::limit($service->getTranslation('description', app()->getLocale()), 100) }}
                                            </p>
                                            <a href="{{ route('services.show', $service->getTranslation('slug', app()->getLocale())) }}" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                                {{ __('Learn More') }} &rarr;
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="col-span-3 text-center py-8">
                                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ __('No Services Available Yet') }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 max-w-md mx-auto mb-4">
                                        {{ __('We\'re currently working on adding our services. Please check back soon or contact us for more information.') }}
                                    </p>
                                    <a href="{{ route('contact') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                        {{ __('Contact Us') }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Line clamp utility class */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
    @endsection
</x-app-layout>
