/**
 * Product Page Enhancements
 *
 * This script adds modern e-commerce features to the product detail page:
 * - Image gallery with thumbnail navigation and zoom
 * - AJAX cart functionality
 * - Dynamic variant selection
 * - Quantity controls
 * - Social sharing
 * - Wishlist functionality
 * - Notifications
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initImageGallery();
    initVariantSelection();
    initQuantityControls();
    initAjaxCart();
    initSocialSharing();
    initWishlist();
    initReviewsSection();
});

/**
 * Image Gallery Functionality
 * - Thumbnail navigation
 * - Image zoom on hover
 * - Fullscreen view on click
 */
function initImageGallery() {
    const mainImage = document.querySelector('.main-product-image');
    const thumbnails = document.querySelectorAll('.product-thumbnail');
    const zoomOverlay = document.querySelector('.zoom-overlay');

    if (!mainImage) return;

    // Create fullscreen modal if it doesn't exist
    let fullscreenModal = document.getElementById('image-fullscreen-modal');
    if (!fullscreenModal) {
        fullscreenModal = document.createElement('div');
        fullscreenModal.id = 'image-fullscreen-modal';
        fullscreenModal.className = 'fixed inset-0 z-50 hidden bg-black bg-opacity-90 flex items-center justify-center';
        fullscreenModal.innerHTML = `
            <div class="relative w-full h-full flex flex-col items-center justify-center p-4">
                <button class="absolute top-4 right-4 text-white hover:text-gray-300 focus:outline-none z-10" id="close-fullscreen">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <button class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 focus:outline-none z-10" id="prev-fullscreen">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 focus:outline-none z-10" id="next-fullscreen">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                <img src="" alt="Fullscreen product image" class="max-h-full max-w-full object-contain" id="fullscreen-image">
            </div>
        `;
        document.body.appendChild(fullscreenModal);

        // Set up fullscreen modal events
        document.getElementById('close-fullscreen').addEventListener('click', function() {
            fullscreenModal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });

        // Close on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !fullscreenModal.classList.contains('hidden')) {
                fullscreenModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        });
    }

    // Get all product images (main + thumbnails)
    const allProductImages = [];

    // Add main image if it has a src
    if (mainImage.src) {
        allProductImages.push(mainImage.src);
    }

    // Add thumbnail images
    thumbnails.forEach(thumbnail => {
        const fullImageUrl = thumbnail.getAttribute('data-full-image');
        if (fullImageUrl && !allProductImages.includes(fullImageUrl)) {
            allProductImages.push(fullImageUrl);
        }
    });

    // Set up thumbnail click handlers
    if (thumbnails.length > 0) {
        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Get full-size image URL
                const fullImageUrl = this.getAttribute('data-full-image');
                if (!fullImageUrl) return;

                // Update main image
                mainImage.src = fullImageUrl;

                // Remove active class from all thumbnails
                thumbnails.forEach(t => t.classList.remove('ring-2', 'ring-indigo-500'));

                // Add active class to clicked thumbnail
                this.classList.add('ring-2', 'ring-indigo-500');
            });
        });

        // Set first thumbnail as active by default
        thumbnails[0].classList.add('ring-2', 'ring-indigo-500');
    }

    // Set up fullscreen view on main image click
    mainImage.addEventListener('click', function() {
        const fullscreenImage = document.getElementById('fullscreen-image');
        fullscreenImage.src = this.src;

        // Show modal
        fullscreenModal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // Set up navigation
        const currentIndex = allProductImages.indexOf(this.src);

        document.getElementById('prev-fullscreen').addEventListener('click', function() {
            const newIndex = (currentIndex - 1 + allProductImages.length) % allProductImages.length;
            fullscreenImage.src = allProductImages[newIndex];
        });

        document.getElementById('next-fullscreen').addEventListener('click', function() {
            const newIndex = (currentIndex + 1) % allProductImages.length;
            fullscreenImage.src = allProductImages[newIndex];
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (fullscreenModal.classList.contains('hidden')) return;

            if (e.key === 'ArrowLeft') {
                const newIndex = (currentIndex - 1 + allProductImages.length) % allProductImages.length;
                fullscreenImage.src = allProductImages[newIndex];
            } else if (e.key === 'ArrowRight') {
                const newIndex = (currentIndex + 1) % allProductImages.length;
                fullscreenImage.src = allProductImages[newIndex];
            }
        });
    });

    // Set up image zoom functionality
    if (zoomOverlay) {
        const imageContainer = mainImage.parentElement;

        zoomOverlay.addEventListener('mousemove', function(e) {
            if (window.innerWidth < 768) return; // Disable on mobile

            const rect = imageContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const xPercent = (x / rect.width) * 100;
            const yPercent = (y / rect.height) * 100;

            mainImage.style.transformOrigin = `${xPercent}% ${yPercent}%`;
        });

        zoomOverlay.addEventListener('mouseenter', function() {
            if (window.innerWidth < 768) return; // Disable on mobile
            mainImage.style.transform = 'scale(1.5)';
        });

        zoomOverlay.addEventListener('mouseleave', function() {
            mainImage.style.transform = 'scale(1)';
        });
    }
}

/**
 * Variant Selection
 * - Visual selection with swatches and buttons
 * - Update price, stock status, and availability
 * - Disable add to cart for unavailable variants
 */
function initVariantSelection() {
    const variantSelect = document.getElementById('product_variant_id');
    const variantSelector = document.getElementById('variant_selector');
    const priceDisplay = document.querySelector('.product-price');
    const comparePriceDisplay = document.querySelector('.product-compare-price');
    const discountDisplay = document.querySelector('.product-discount');
    const stockStatusDisplay = document.querySelector('.stock-status');
    const addToCartButton = document.querySelector('.add-to-cart-button');
    const buyNowButton = document.querySelector('.buy-now-button');
    const variantNotAvailableMessage = document.querySelector('.variant-not-available');

    // Visual variant selection elements
    const colorSwatches = document.querySelectorAll('.color-swatch');
    const sizeButtons = document.querySelectorAll('.size-button');
    const attributeButtons = document.querySelectorAll('.attribute-button');
    const variantAttributes = document.querySelectorAll('.variant-attribute');

    if (!variantSelect) return;

    // Check if this is a product with a single default variant (no attributes)
    let hasDefaultVariant = variantAttributes.length === 0 && variantSelect.options.length > 1;

    // Store all variant data for easy access
    const variants = [];
    Array.from(variantSelect.options).forEach(option => {
        if (option.value) {
            let attributesArray = [];
            try {
                attributesArray = JSON.parse(option.getAttribute('data-attributes') || '[]');
            } catch (e) {
                console.error('Error parsing variant attributes:', e);
            }

            const attributesObject = attributesArray.reduce((obj, attr) => {
                if(attr && attr.attribute && attr.value) obj[attr.attribute] = attr.value;
                return obj;
            }, {});

            variants.push({
                id: option.value,
                price: option.getAttribute('data-price'),
                comparePrice: option.getAttribute('data-compare-price'),
                isAvailable: option.getAttribute('data-available') === 'true',
                stockMessage: option.getAttribute('data-stock-message'),
                stockQuantity: parseInt(option.getAttribute('data-stock-quantity') || '0', 10),
                attributes: attributesObject,
                option: option
            });
        }
    });

    // Track selected attributes - make it accessible outside the function scope
    window.selectedAttributes = {};

    // Initialize visual variant selection
    function initVisualVariantSelection() {
        // Color swatches
        colorSwatches.forEach(swatch => {
            swatch.addEventListener('click', function() {
                const attributeName = this.getAttribute('data-attribute-name');
                const attributeValue = this.getAttribute('data-attribute-value');

                // Update selected attributes
                window.selectedAttributes[attributeName] = attributeValue;

                // Update UI
                colorSwatches.forEach(s => {
                    if (s.getAttribute('data-attribute-name') === attributeName) {
                        const checkmark = s.querySelector('.color-swatch-check');
                        if (s === this) {
                            s.classList.add('ring-2', 'ring-indigo-500', 'border-transparent');
                            if (checkmark) checkmark.classList.remove('hidden');
                        } else {
                            s.classList.remove('ring-2', 'ring-indigo-500', 'border-transparent');
                            if (checkmark) checkmark.classList.add('hidden');
                        }
                    }
                });

                // Find matching variant
                updateSelectedVariant();
            });
        });

        // Size buttons
        sizeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const attributeName = this.getAttribute('data-attribute-name');
                const attributeValue = this.getAttribute('data-attribute-value');

                // Update selected attributes
                window.selectedAttributes[attributeName] = attributeValue;

                // Update UI
                sizeButtons.forEach(b => {
                    if (b.getAttribute('data-attribute-name') === attributeName) {
                        if (b === this) {
                            b.classList.add('bg-indigo-600', 'text-white', 'border-indigo-600');
                            b.classList.remove('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border-gray-300', 'dark:border-gray-600');
                        } else {
                            b.classList.remove('bg-indigo-600', 'text-white', 'border-indigo-600');
                            b.classList.add('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border-gray-300', 'dark:border-gray-600');
                        }
                    }
                });

                // Find matching variant
                updateSelectedVariant();
            });
        });

        // Other attribute buttons
        attributeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const attributeName = this.getAttribute('data-attribute-name');
                const attributeValue = this.getAttribute('data-attribute-value');

                // Update selected attributes
                window.selectedAttributes[attributeName] = attributeValue;

                // Update UI
                attributeButtons.forEach(b => {
                    if (b.getAttribute('data-attribute-name') === attributeName) {
                        if (b === this) {
                            b.classList.add('bg-indigo-600', 'text-white', 'border-indigo-600');
                            b.classList.remove('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border-gray-300', 'dark:border-gray-600');
                        } else {
                            b.classList.remove('bg-indigo-600', 'text-white', 'border-indigo-600');
                            b.classList.add('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border-gray-300', 'dark:border-gray-600');
                        }
                    }
                });

                // Find matching variant
                updateSelectedVariant();
            });
        });

        // Fallback dropdown for no attributes
        if (variantSelector) {
            variantSelector.addEventListener('change', function() {
                const selectedVariantId = this.value;
                if (!selectedVariantId) return;

                // Update hidden select
                variantSelect.value = selectedVariantId;
                variantSelect.dispatchEvent(new Event('change'));
            });
        }

        // Select first available option for each attribute to initialize
        const attributeElements = document.querySelectorAll('.variant-attribute');
        attributeElements.forEach(attrElement => {
            const attributeName = attrElement.getAttribute('data-attribute');
            const firstButton = attrElement.querySelector('.color-swatch, .size-button, .attribute-button');

            if (firstButton) {
                firstButton.click();
            }
        });
    }

    // Find and select the matching variant based on selected attributes
    function updateSelectedVariant() {
        // Check if we have selected all required attributes
        const requiredAttributes = document.querySelectorAll('.variant-attribute');
        const allAttributesSelected = Array.from(requiredAttributes).every(attr => {
            const attributeName = attr.getAttribute('data-attribute');
            return window.selectedAttributes[attributeName] !== undefined;
        });

        if (!allAttributesSelected) return;

        // Find matching variant
        const matchingVariant = variants.find(variant => {
            return Object.entries(window.selectedAttributes).every(([key, value]) => {
                return variant.attributes[key] === value;
            });
        });

        if (matchingVariant) {
            // Update hidden select
            variantSelect.value = matchingVariant.id;

            // Trigger change event to ensure the value is properly updated
            variantSelect.dispatchEvent(new Event('change'));

            // Update UI
            updateVariantUI(matchingVariant);

            // Hide not available message
            if (variantNotAvailableMessage) {
                variantNotAvailableMessage.classList.add('hidden');
            }

            // Log for debugging
            console.log('Selected variant:', matchingVariant.id, 'with attributes:', window.selectedAttributes);
        } else {
            // No matching variant found
            variantSelect.value = '';

            // Disable add to cart button
            if (addToCartButton) {
                addToCartButton.disabled = true;
                addToCartButton.classList.add('opacity-50', 'cursor-not-allowed');
            }

            // Disable buy now button
            if (buyNowButton) {
                buyNowButton.disabled = true;
                buyNowButton.classList.add('opacity-50', 'cursor-not-allowed');
            }

            // Show not available message
            if (variantNotAvailableMessage) {
                variantNotAvailableMessage.classList.remove('hidden');
            }

            // Log for debugging
            console.log('No matching variant found for attributes:', window.selectedAttributes);
        }
    }

    // Update UI based on selected variant
    function updateVariantUI(variant) {
        // Update price
        if (priceDisplay) {
            priceDisplay.textContent = `$${variant.price}`;
        }

        // Update compare-at price and discount
        if (comparePriceDisplay && variant.comparePrice) {
            comparePriceDisplay.textContent = `$${variant.comparePrice}`;
            comparePriceDisplay.classList.remove('hidden');

            if (discountDisplay) {
                const price = parseFloat(variant.price);
                const compareAtPrice = parseFloat(variant.comparePrice);
                if (compareAtPrice > price) {
                    const discountPercentage = Math.round((1 - price / compareAtPrice) * 100);
                    discountDisplay.textContent = `${discountPercentage}% off`;
                    discountDisplay.classList.remove('hidden');
                } else {
                    discountDisplay.classList.add('hidden');
                }
            }
        } else if (comparePriceDisplay) {
            comparePriceDisplay.classList.add('hidden');
            if (discountDisplay) {
                discountDisplay.classList.add('hidden');
            }
        }

        // Update stock status
        if (stockStatusDisplay) {
            if (!variant.isAvailable) {
                stockStatusDisplay.textContent = 'Out of stock';
                stockStatusDisplay.className = 'stock-status text-red-600';
            } else if (variant.stockMessage) {
                stockStatusDisplay.textContent = variant.stockMessage;
                stockStatusDisplay.className = 'stock-status text-yellow-600';
            } else {
                stockStatusDisplay.textContent = 'In stock';
                stockStatusDisplay.className = 'stock-status text-green-600';
            }
        }

        // Update add to cart button
        if (addToCartButton) {
            addToCartButton.disabled = !variant.isAvailable;

            if (variant.isAvailable) {
                addToCartButton.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                addToCartButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // Update buy now button
        if (buyNowButton) {
            buyNowButton.disabled = !variant.isAvailable;

            if (variant.isAvailable) {
                buyNowButton.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                buyNowButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    }

    // Check for default variant product
    hasDefaultVariant = hasDefaultVariant || variantSelect.hasAttribute('data-default-variant');

    // Initialize variant selection
    if (colorSwatches.length > 0 || sizeButtons.length > 0 || attributeButtons.length > 0) {
        initVisualVariantSelection();
    } else if (hasDefaultVariant) {
        // This is a product with a single default variant (no attributes)
        console.log('Default variant product detected');

        // Select the first non-empty variant option
        if (variantSelect.options.length > 1) {
            // Skip the first option which is usually "Select a variant"
            variantSelect.value = variantSelect.options[1].value;

            // Find the corresponding variant data
            const defaultVariant = variants.find(v => v.id === variantSelect.value);
            if (defaultVariant) {
                console.log('Auto-selected default variant:', defaultVariant.id);
                updateVariantUI(defaultVariant);
            }
        }

        // If using the variant selector dropdown, sync it with the hidden select
        if (variantSelector) {
            variantSelector.value = variantSelect.value;

            // Add change event listener to keep them in sync
            variantSelector.addEventListener('change', function() {
                variantSelect.value = this.value;

                const selectedVariant = variants.find(v => v.id === this.value);
                if (selectedVariant) {
                    updateVariantUI(selectedVariant);
                }
            });
        }
    } else {
        // Fallback to traditional select behavior
        variantSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (!selectedOption.value) return;

            const selectedVariant = variants.find(v => v.id === selectedOption.value);
            if (selectedVariant) {
                updateVariantUI(selectedVariant);
            }
        });

        // Trigger change event to initialize displays
        variantSelect.dispatchEvent(new Event('change'));
    }
}

/**
 * Quantity Controls
 * - Plus/minus buttons for quantity input
 * - Validate min/max values
 */
function initQuantityControls() {
    const quantityInput = document.getElementById('quantity');
    const minusBtn = document.querySelector('.minus-btn');
    const plusBtn = document.querySelector('.plus-btn');

    if (!quantityInput || !minusBtn || !plusBtn) return;

    minusBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value, 10);
        const minValue = parseInt(quantityInput.getAttribute('min'), 10) || 1;

        if (currentValue > minValue) {
            quantityInput.value = currentValue - 1;
            quantityInput.dispatchEvent(new Event('change'));
        }
    });

    plusBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value, 10);
        const maxValue = parseInt(quantityInput.getAttribute('max'), 10) || 100;

        if (currentValue < maxValue) {
            quantityInput.value = currentValue + 1;
            quantityInput.dispatchEvent(new Event('change'));
        }
    });

    // Validate input on change
    quantityInput.addEventListener('change', function() {
        let value = parseInt(this.value, 10);
        const minValue = parseInt(this.getAttribute('min'), 10) || 1;
        const maxValue = parseInt(this.getAttribute('max'), 10) || 100;

        if (isNaN(value) || value < minValue) {
            value = minValue;
        } else if (value > maxValue) {
            value = maxValue;
        }

        this.value = value;
    });
}

/**
 * AJAX Cart Functionality
 * - Add to cart without page reload
 * - Show loading state and success/error messages
 * - Support for Buy Now functionality
 */
function initAjaxCart() {
    const addToCartForm = document.querySelector('.add-to-cart-form');
    const addToCartButton = document.querySelector('.add-to-cart-button');
    const buyNowButton = document.querySelector('.buy-now-button');

    if (!addToCartForm || !addToCartButton) return;

    // Add to Cart
    addToCartForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate form
        const variantSelect = document.getElementById('product_variant_id');

        // Check if this is a default variant product
        const isDefaultVariant = variantSelect.hasAttribute('data-default-variant');

        // If it's a default variant product but no value is selected, try to select the first variant
        if (!variantSelect.value && isDefaultVariant && variantSelect.options.length > 1) {
            variantSelect.value = variantSelect.options[1].value;
            console.log('Auto-selected default variant for add to cart:', variantSelect.value);
        }

        // If still no value, show appropriate error
        if (!variantSelect.value) {
            // Check if we have any variant attributes
            const variantAttributes = document.querySelectorAll('.variant-attribute');
            if (variantAttributes.length > 0) {
                // Find which attributes are not selected
                const missingAttributes = [];
                variantAttributes.forEach(attr => {
                    const attributeName = attr.getAttribute('data-attribute');
                    if (!window.selectedAttributes[attributeName]) {
                        missingAttributes.push(attributeName);
                    }
                });

                if (missingAttributes.length > 0) {
                    const missingList = missingAttributes.map(attr => attr.charAt(0).toUpperCase() + attr.slice(1)).join(', ');
                    showNotification('error', `Please select: ${missingList}`);
                } else {
                    showNotification('error', 'The selected combination is not available');
                }
            } else {
                showNotification('error', 'Please select a product variant');
            }
            return;
        }

        // Get form data
        const formData = new FormData(this);

    // Add to cart with AJAX, passing the clicked button
    addToCartWithAjax(formData, false, addToCartButton);
});

// Buy Now
if (buyNowButton) {
    // Ensure the Buy Now button has type="button" to prevent form submission
    buyNowButton.type = 'button';
    
    buyNowButton.addEventListener('click', function(e) {
        e.preventDefault(); // Prevent form submission
        e.stopPropagation(); // Prevent event bubbling
        if (this.disabled) return;

            // Validate form
            const variantSelect = document.getElementById('product_variant_id');

            // Check if this is a default variant product
            const isDefaultVariant = variantSelect.hasAttribute('data-default-variant');

            // If it's a default variant product but no value is selected, try to select the first variant
            if (!variantSelect.value && isDefaultVariant && variantSelect.options.length > 1) {
                variantSelect.value = variantSelect.options[1].value;
                console.log('Auto-selected default variant for buy now:', variantSelect.value);
            }

            // If still no value, show appropriate error
            if (!variantSelect.value) {
                // Check if we have any variant attributes
                const variantAttributes = document.querySelectorAll('.variant-attribute');
                if (variantAttributes.length > 0) {
                    // Find which attributes are not selected
                    const missingAttributes = [];
                    variantAttributes.forEach(attr => {
                        const attributeName = attr.getAttribute('data-attribute');
                        if (!window.selectedAttributes[attributeName]) {
                            missingAttributes.push(attributeName);
                        }
                    });

                    if (missingAttributes.length > 0) {
                        const missingList = missingAttributes.map(attr => attr.charAt(0).toUpperCase() + attr.slice(1)).join(', ');
                        showNotification('error', `Please select: ${missingList}`);
                    } else {
                        showNotification('error', 'The selected combination is not available');
                    }
                } else {
                    showNotification('error', 'Please select a product variant');
                }
                return;
            }

            // Get form data
            const formData = new FormData(addToCartForm);

            // Add a flag for buy now
            formData.append('buy_now', '1');

            // Add to cart with AJAX and redirect to checkout, passing the clicked button
            addToCartWithAjax(formData, true, buyNowButton);
        });
    }

    function addToCartWithAjax(formData, redirectToCheckout, clickedButton = null) {
        // Store original button content
        const originalAddToCartContent = addToCartButton.innerHTML;
        const originalBuyNowContent = buyNowButton ? buyNowButton.innerHTML : '';

        // Show loading state only on the clicked button
        if (clickedButton) {
            // Store the original content if not already stored
            if (!clickedButton.originalContent) {
                clickedButton.originalContent = clickedButton.innerHTML;
            }
            
            // Disable and show loading state
            clickedButton.disabled = true;
            clickedButton.innerHTML = `
                <span class="relative flex items-center justify-center">
                    <svg class="animate-spin w-5 h-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    ${redirectToCheckout ? 'Processing...' : 'Adding...'}
                </span>
            `;
        }

        // Add AJAX header
        const headers = new Headers({
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
        });

        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            headers.append('X-CSRF-TOKEN', csrfToken);
        }

        // Send request
        fetch(addToCartForm.action, {
            method: 'POST',
            body: formData,
            headers: headers
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Reset the clicked button state
            if (clickedButton && clickedButton.originalContent) {
                clickedButton.disabled = false;
                clickedButton.innerHTML = clickedButton.originalContent;
                delete clickedButton.originalContent;
            }

            if (data.success) {
                // Show success notification
                showNotification('success', data.message || 'Item added to cart');

                // Update cart count in header if it exists
                updateCartCount(data.cart?.total_items);

                // Add success animation to button
                addToCartButton.classList.add('add-to-cart-success');
                setTimeout(() => {
                    addToCartButton.classList.remove('add-to-cart-success');
                }, 700);

                // Redirect to checkout if buy now was clicked
                if (redirectToCheckout) {
                    // Get the checkout URL from the form's data attribute
                    const checkoutUrl = addToCartForm.getAttribute('data-checkout-url') || '/checkout';
                    // Small delay to allow notification to be seen
                    setTimeout(() => {
                        window.location.href = checkoutUrl;
                    }, 500);
                }
            } else {
                // Show error notification
                showNotification('error', data.message || 'Failed to add item to cart');
            }
        })
        .catch(error => {
            console.error('Error adding to cart:', error);

            // Reset the clicked button state
            if (clickedButton && clickedButton.originalContent) {
                clickedButton.disabled = false;
                clickedButton.innerHTML = clickedButton.originalContent;
                delete clickedButton.originalContent;
            }

            // Show error notification
            showNotification('error', 'An error occurred. Please try again.');
        });
    }

    function updateCartCount(count) {
        if (typeof count !== 'undefined') {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = count;

                // Make it visible if it was hidden
                element.classList.remove('hidden');
            });
        }
    }
}

/**
 * Social Sharing
 * - Share product on social media platforms
 */
function initSocialSharing() {
    const shareButtons = document.querySelectorAll('.social-share-btn');

    if (shareButtons.length === 0) return;

    const pageUrl = encodeURIComponent(window.location.href);
    const pageTitle = encodeURIComponent(document.title);

    shareButtons.forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.getAttribute('data-platform');
            let shareUrl = '';

            switch (platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${pageUrl}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${pageUrl}&text=${pageTitle}`;
                    break;
                case 'pinterest':
                    // Try to get the main product image
                    const mainImage = document.querySelector('.main-product-image');
                    const imageUrl = mainImage ? encodeURIComponent(mainImage.src) : '';
                    shareUrl = `https://pinterest.com/pin/create/button/?url=${pageUrl}&media=${imageUrl}&description=${pageTitle}`;
                    break;
                case 'email':
                    shareUrl = `mailto:?subject=${pageTitle}&body=Check out this product: ${pageUrl}`;
                    break;
            }

            if (shareUrl && platform !== 'email') {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            } else if (platform === 'email') {
                window.location.href = shareUrl;
            }
        });
    });
}

/**
 * Wishlist Functionality
 * - Toggle wishlist status
 * - Show notification
 */
function initWishlist() {
    const wishlistButton = document.querySelector('.wishlist-button');

    if (!wishlistButton) return;

    wishlistButton.addEventListener('click', function() {
        const wishlistIcon = this.querySelector('svg');

        // Toggle wishlist status (this would normally call an API)
        if (wishlistIcon.classList.contains('text-red-500')) {
            // Remove from wishlist
            wishlistIcon.classList.remove('text-red-500');
            wishlistIcon.classList.add('text-gray-400');
            wishlistIcon.setAttribute('fill', 'none');

            showNotification('info', 'Removed from wishlist');
        } else {
            // Add to wishlist
            wishlistIcon.classList.remove('text-gray-400');
            wishlistIcon.classList.add('text-red-500');
            wishlistIcon.setAttribute('fill', 'currentColor');

            showNotification('success', 'Added to wishlist');
        }
    });
}

/**
 * Reviews Section
 * - Write review button
 * - Load more reviews button
 */
function initReviewsSection() {
    const writeReviewButton = document.querySelector('.write-review-button');
    const loadMoreReviewsButton = document.querySelector('.load-more-reviews-button');

    if (writeReviewButton) {
        writeReviewButton.addEventListener('click', function() {
            // This would normally open a review form modal
            showNotification('info', 'Review form would open here');
        });
    }

    if (loadMoreReviewsButton) {
        loadMoreReviewsButton.addEventListener('click', function() {
            // This would normally load more reviews via AJAX
            this.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Loading...';

            setTimeout(() => {
                this.innerHTML = 'Load More Reviews';
                showNotification('info', 'More reviews would load here');
            }, 1500);
        });
    }
}

/**
 * Show Notification
 * - Dynamically creates and displays a notification element.
 */
function showNotification(type, message) {
    // Find or create the notification container
    let notificationContainer = document.getElementById('dynamic-notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'dynamic-notification-container';
        notificationContainer.className = 'fixed top-5 right-5 z-[100] space-y-4 w-full max-w-sm'; // Use high z-index
        document.body.appendChild(notificationContainer);
    }

    // Create the notification element (mimicking x-notification component structure)
    const notificationId = `notification-${Date.now()}`;
    const notificationElement = document.createElement('div');
    notificationElement.id = notificationId;
    notificationElement.className = `rounded-md p-4 shadow-lg transition-all duration-300 ease-out transform opacity-0 translate-x-4`;
    notificationElement.setAttribute('role', 'alert');

    // Apply type-specific classes
    const typeClasses = {
        success: 'bg-green-100 border-l-4 border-green-500 text-green-700 dark:bg-green-900 dark:border-green-600 dark:text-green-300',
        error: 'bg-red-100 border-l-4 border-red-500 text-red-700 dark:bg-red-900 dark:border-red-600 dark:text-red-300',
        warning: 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 dark:bg-yellow-900 dark:border-yellow-600 dark:text-yellow-300',
        info: 'bg-blue-100 border-l-4 border-blue-500 text-blue-700 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300',
    };
    notificationElement.classList.add(...(typeClasses[type] || typeClasses.info).split(' '));

    // Add content and close button
    notificationElement.innerHTML = `
        <div class="flex">
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-auto pl-3">
                <div class="-mx-1.5 -my-1.5">
                    <button type="button" class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2">
                        <span class="sr-only">Dismiss</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add to container
    notificationContainer.appendChild(notificationElement);

    // Trigger enter animation
    requestAnimationFrame(() => {
        notificationElement.classList.remove('opacity-0', 'translate-x-4');
        notificationElement.classList.add('opacity-100', 'translate-x-0');
    });

    // Auto-dismiss after 5 seconds
    const dismissTimeout = setTimeout(() => {
        notificationElement.classList.remove('opacity-100', 'translate-x-0');
        notificationElement.classList.add('opacity-0', 'translate-x-4');
        setTimeout(() => notificationElement.remove(), 300); // Remove from DOM after transition
    }, 5000);

    // Dismiss on click
    notificationElement.querySelector('button').addEventListener('click', () => {
        clearTimeout(dismissTimeout);
        notificationElement.classList.remove('opacity-100', 'translate-x-0');
        notificationElement.classList.add('opacity-0', 'translate-x-4');
        setTimeout(() => notificationElement.remove(), 300);
    });

    // Fallback just in case - dispatch event for potential Alpine listeners
    /*
    const event = new CustomEvent('show-notification', {
            detail: {
                type: type,
                message: message
            }
        });
        document.dispatchEvent(event);
    */
}
