<?php

use App\Http\Controllers\Admin\ContentBlockController;
use App\Http\Controllers\Admin\ShippingConfigurationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application.
|
*/

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // Content Blocks
    Route::resource('content-blocks', ContentBlockController::class);

    // Shipping Configuration
    Route::prefix('shipping')->name('shipping.')->group(function () {
        Route::get('configuration', [ShippingConfigurationController::class, 'index'])->name('configuration');
        Route::put('configuration', [ShippingConfigurationController::class, 'update'])->name('configuration.update');
        Route::get('configuration/reset', [ShippingConfigurationController::class, 'reset'])->name('configuration.reset');
        Route::post('configuration/test', [ShippingConfigurationController::class, 'test'])->name('configuration.test');
    });

    // Tax Classes (commented out as TaxController doesn't exist yet)
    // Route::resource('taxes', TaxController::class);
});
