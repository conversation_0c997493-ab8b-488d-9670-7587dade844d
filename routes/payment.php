<?php

use App\Http\Controllers\PaymentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Payment Routes
|--------------------------------------------------------------------------
|
| Here is where you can register payment routes for your application.
|
*/

// Payment routes
Route::middleware(['web'])->group(function () {
    // Payment page
    Route::get('/payments/{orderNumber}', [PaymentController::class, 'show'])
        ->name('payments.show');

    // Select payment method
    Route::post('/payments/{orderNumber}/select-method', [PaymentController::class, 'selectPaymentMethod'])
        ->name('payments.select-method');

    // Process payment
    Route::post('/payments/{orderNumber}/process', [PaymentController::class, 'process'])
        ->name('payments.process');

    // Stripe payment intent
    Route::post('/payments/{orderNumber}/stripe-intent', [PaymentController::class, 'createStripeIntent'])
        ->name('payments.stripe.intent');

    // PayPal order
    Route::post('/payments/{orderNumber}/paypal-order', [PaymentController::class, 'createPayPalOrder'])
        ->name('payments.paypal.order');

    // Payment callback routes
    Route::get('/payments/paypal/callback', [App\Http\Controllers\CheckoutController::class, 'paypalCallback'])
        ->name('checkout.paypal.callback')
        ->withoutMiddleware(['auth']);

    Route::get('/payments/paypal/cancel', [App\Http\Controllers\CheckoutController::class, 'paypalCancel'])
        ->name('checkout.paypal.cancel')
        ->withoutMiddleware(['auth']);

    // Stripe cancellation route
    Route::get('/payments/stripe/cancel', [App\Http\Controllers\CheckoutController::class, 'stripeCancel'])
        ->name('checkout.stripe.cancel')
        ->withoutMiddleware(['auth']);
});
