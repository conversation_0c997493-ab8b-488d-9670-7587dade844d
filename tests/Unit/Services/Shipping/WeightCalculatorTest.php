<?php

namespace Tests\Unit\Services\Shipping;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use App\Services\Shipping\Utilities\WeightCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WeightCalculatorTest extends TestCase
{
    use RefreshDatabase;

    protected Cart $cart;
    protected ProductVariant $variant1;
    protected ProductVariant $variant2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a cart with items
        $this->cart = Cart::factory()->create();

        // Create product variants with weight
        $this->variant1 = ProductVariant::factory()->create([
            'weight' => 2.5,
            'weight_unit' => 'kg',
            'price' => 25.00,
        ]);

        $this->variant2 = ProductVariant::factory()->create([
            'weight' => 1.5,
            'weight_unit' => 'lb',
            'price' => 15.00,
        ]);

        // Add items to cart
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_variant_id' => $this->variant1->id,
            'quantity' => 2,
            'unit_price' => $this->variant1->price,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_variant_id' => $this->variant2->id,
            'quantity' => 1,
            'unit_price' => $this->variant2->price,
        ]);
    }

    /** @test */
    public function it_calculates_cart_weight_in_kilograms()
    {
        $weight = WeightCalculator::calculateCartWeight($this->cart, 'kg');

        // 2.5kg * 2 + 1.5lb * 0.******** = 5.68kg
        $expectedWeight = (2.5 * 2) + (1.5 * 0.********);

        // Use assertEqualsWithDelta for floating point comparisons
        $this->assertEqualsWithDelta($expectedWeight, $weight, 0.01);
    }

    /** @test */
    public function it_calculates_cart_weight_in_pounds()
    {
        $weight = WeightCalculator::calculateCartWeight($this->cart, 'lb');

        // 2.5kg * 2.20462 + 1.5lb = 12.51lb
        $expectedWeight = (2.5 * 2 * 2.20462) + 1.5;

        // Use a delta of 0.01 to account for floating point precision
        $this->assertEqualsWithDelta($expectedWeight, $weight, 0.01);
    }

    /** @test */
    public function it_converts_weight_between_units()
    {
        // kg to lb
        $kgToLb = WeightCalculator::convertWeight(1, 'kg', 'lb');
        $this->assertEqualsWithDelta(2.20462, $kgToLb, 0.01);

        // lb to kg
        $lbToKg = WeightCalculator::convertWeight(1, 'lb', 'kg');
        $this->assertEqualsWithDelta(0.********, $lbToKg, 0.01);

        // kg to g
        $kgToG = WeightCalculator::convertWeight(1, 'kg', 'g');
        $this->assertEquals(1000, $kgToG);

        // g to kg
        $gToKg = WeightCalculator::convertWeight(1000, 'g', 'kg');
        $this->assertEquals(1, $gToKg);

        // lb to oz
        $lbToOz = WeightCalculator::convertWeight(1, 'lb', 'oz');
        $this->assertEqualsWithDelta(16, $lbToOz, 0.01);

        // oz to lb
        $ozToLb = WeightCalculator::convertWeight(16, 'oz', 'lb');
        $this->assertEqualsWithDelta(1, $ozToLb, 0.01);
    }

    /** @test */
    public function it_handles_same_unit_conversion()
    {
        $weight = WeightCalculator::convertWeight(5, 'kg', 'kg');
        $this->assertEquals(5, $weight);
    }
}
