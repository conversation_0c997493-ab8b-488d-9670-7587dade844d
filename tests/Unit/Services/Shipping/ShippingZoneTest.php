<?php

namespace Tests\Unit\Services\Shipping;

use App\Models\Address;
use App\Models\ShippingZone;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShippingZoneTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_applies_to_address_with_matching_country()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => null,
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);
        
        $this->assertTrue($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_does_not_apply_to_address_with_non_matching_country()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => null,
        ]);
        
        $address = Address::factory()->create([
            'country' => 'CA',
            'region' => 'ON',
            'postal_code' => 'M5V 2H1',
        ]);
        
        $this->assertFalse($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_applies_to_address_with_matching_country_and_region()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => ['CA', 'NY', 'TX'],
            'postal_codes' => null,
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);
        
        $this->assertTrue($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_does_not_apply_to_address_with_matching_country_but_non_matching_region()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => ['CA', 'NY', 'TX'],
            'postal_codes' => null,
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'FL',
            'postal_code' => '33139',
        ]);
        
        $this->assertFalse($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_applies_to_address_with_matching_postal_code_exact()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => ['90210', '10001', '60601'],
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);
        
        $this->assertTrue($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_applies_to_address_with_matching_postal_code_range()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => ['90000-99999', '10000-19999'],
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);
        
        $this->assertTrue($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_applies_to_address_with_matching_postal_code_wildcard()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => ['902*', '100*'],
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);
        
        $this->assertTrue($zone->appliesToAddress($address));
    }

    /** @test */
    public function it_does_not_apply_to_address_with_non_matching_postal_code()
    {
        $zone = ShippingZone::factory()->create([
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => ['90000-90100', '10000-19999'],
        ]);
        
        $address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);
        
        $this->assertFalse($zone->appliesToAddress($address));
    }
}
