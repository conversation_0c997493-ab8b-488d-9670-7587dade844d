<?php

namespace Tests\Unit\Services\Shipping;

use App\Models\Address;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use App\Models\ShippingMethod;
use App\Models\ShippingRate;
use App\Models\ShippingZone;
use App\Services\Shipping\FlatRateCalculator;
use App\Services\Shipping\ItemBasedCalculator;
use App\Services\Shipping\PriceBasedCalculator;
use App\Services\Shipping\WeightBasedCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShippingCalculatorsTest extends TestCase
{
    use RefreshDatabase;

    protected Cart $cart;
    protected Address $address;
    protected ShippingZone $zone;
    protected ShippingMethod $flatRateMethod;
    protected ShippingMethod $weightBasedMethod;
    protected ShippingMethod $priceBasedMethod;
    protected ShippingMethod $itemBasedMethod;
    protected ShippingRate $flatRateShipping;
    protected ShippingRate $weightBasedShipping;
    protected ShippingRate $priceBasedShipping;
    protected ShippingRate $itemBasedShipping;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a cart with items
        $this->cart = Cart::factory()->create();
        
        // Create product variants with weight
        $variant1 = ProductVariant::factory()->create([
            'weight' => 2.5,
            'weight_unit' => 'kg',
            'price' => 25.00,
        ]);
        
        $variant2 = ProductVariant::factory()->create([
            'weight' => 1.5,
            'weight_unit' => 'kg',
            'price' => 15.00,
        ]);
        
        // Add items to cart
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_variant_id' => $variant1->id,
            'quantity' => 2,
            'unit_price' => $variant1->price,
        ]);
        
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_variant_id' => $variant2->id,
            'quantity' => 1,
            'unit_price' => $variant2->price,
        ]);

        // Create address
        $this->address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
            'postal_code' => '90210',
        ]);

        // Create shipping zone
        $this->zone = ShippingZone::factory()->create([
            'name' => 'Domestic',
            'countries' => ['US'],
            'regions' => null,
            'postal_codes' => null,
        ]);

        // Create shipping methods
        $this->flatRateMethod = ShippingMethod::factory()->create([
            'code' => 'flat_rate',
            'name' => 'Flat Rate Shipping',
            'method_type' => 'flat_rate',
        ]);
        
        $this->weightBasedMethod = ShippingMethod::factory()->create([
            'code' => 'weight_based',
            'name' => 'Weight-Based Shipping',
            'method_type' => 'weight_based',
        ]);
        
        $this->priceBasedMethod = ShippingMethod::factory()->create([
            'code' => 'price_based',
            'name' => 'Price-Based Shipping',
            'method_type' => 'price_based',
            'is_free_shipping_eligible' => true,
            'minimum_order_amount' => 50,
        ]);
        
        $this->itemBasedMethod = ShippingMethod::factory()->create([
            'code' => 'item_based',
            'name' => 'Item-Based Shipping',
            'method_type' => 'item_based',
        ]);

        // Create shipping rates
        $this->flatRateShipping = ShippingRate::factory()->create([
            'shipping_zone_id' => $this->zone->id,
            'shipping_method_id' => $this->flatRateMethod->id,
            'base_rate' => 5.99,
        ]);
        
        $this->weightBasedShipping = ShippingRate::factory()->create([
            'shipping_zone_id' => $this->zone->id,
            'shipping_method_id' => $this->weightBasedMethod->id,
            'base_rate' => 3.99,
            'per_weight_rate' => 1.50,
            'weight_unit' => 'kg',
        ]);
        
        $this->priceBasedShipping = ShippingRate::factory()->create([
            'shipping_zone_id' => $this->zone->id,
            'shipping_method_id' => $this->priceBasedMethod->id,
            'base_rate' => 7.99,
            'conditions' => [
                'price_tiers' => [
                    [
                        'min' => 0,
                        'max' => 25,
                        'rate' => 7.99,
                    ],
                    [
                        'min' => 25,
                        'max' => 50,
                        'rate' => 5.99,
                    ],
                    [
                        'min' => 50,
                        'max' => null,
                        'rate' => 0,
                    ],
                ],
            ],
        ]);
        
        $this->itemBasedShipping = ShippingRate::factory()->create([
            'shipping_zone_id' => $this->zone->id,
            'shipping_method_id' => $this->itemBasedMethod->id,
            'base_rate' => 2.99,
            'per_item_rate' => 1.00,
        ]);
    }

    /** @test */
    public function flat_rate_calculator_returns_base_rate()
    {
        $calculator = new FlatRateCalculator();
        $cost = $calculator->calculate($this->flatRateShipping, $this->cart, $this->address);
        
        $this->assertEquals(5.99, $cost);
    }

    /** @test */
    public function weight_based_calculator_returns_correct_cost()
    {
        $calculator = new WeightBasedCalculator();
        $cost = $calculator->calculate($this->weightBasedShipping, $this->cart, $this->address);
        
        // Total weight is 6.5kg (2.5kg * 2 + 1.5kg)
        // Base rate is 3.99, per weight rate is 1.50
        // Total should be 3.99 + (6.5 * 1.50) = 13.74
        $expectedCost = 3.99 + (6.5 * 1.50);
        
        $this->assertEquals($expectedCost, $cost, '', 0.01);
    }

    /** @test */
    public function price_based_calculator_returns_correct_cost_for_tier()
    {
        $calculator = new PriceBasedCalculator();
        
        // Create a smaller cart to test the first tier
        $smallCart = Cart::factory()->create();
        CartItem::factory()->create([
            'cart_id' => $smallCart->id,
            'product_variant_id' => ProductVariant::factory()->create(['price' => 20.00])->id,
            'quantity' => 1,
            'unit_price' => 20.00,
        ]);
        
        $cost = $calculator->calculate($this->priceBasedShipping, $smallCart, $this->address);
        
        // Cart total is 20.00, which falls in the first tier (0-25)
        $this->assertEquals(7.99, $cost);
        
        // Create a medium cart to test the second tier
        $mediumCart = Cart::factory()->create();
        CartItem::factory()->create([
            'cart_id' => $mediumCart->id,
            'product_variant_id' => ProductVariant::factory()->create(['price' => 30.00])->id,
            'quantity' => 1,
            'unit_price' => 30.00,
        ]);
        
        $cost = $calculator->calculate($this->priceBasedShipping, $mediumCart, $this->address);
        
        // Cart total is 30.00, which falls in the second tier (25-50)
        $this->assertEquals(5.99, $cost);
        
        // The original cart total is 65.00 (25.00 * 2 + 15.00), which falls in the third tier (50+)
        $cost = $calculator->calculate($this->priceBasedShipping, $this->cart, $this->address);
        
        // Free shipping for orders over 50
        $this->assertEquals(0, $cost);
    }

    /** @test */
    public function item_based_calculator_returns_correct_cost()
    {
        $calculator = new ItemBasedCalculator();
        $cost = $calculator->calculate($this->itemBasedShipping, $this->cart, $this->address);
        
        // Total items is 3 (2 + 1)
        // Base rate is 2.99, per item rate is 1.00
        // Total should be 2.99 + (3 * 1.00) = 5.99
        $expectedCost = 2.99 + (3 * 1.00);
        
        $this->assertEquals($expectedCost, $cost, '', 0.01);
    }
}
