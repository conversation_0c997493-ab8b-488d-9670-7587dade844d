<?php

namespace Tests\Unit\Models;

use App\Models\Address;
use App\Models\ShippingZone;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShippingZoneTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_identifies_catch_all_zones_correctly()
    {
        $specificZone = ShippingZone::factory()->create([
            'name' => 'US Zone',
            'countries' => ['US'],
            'is_catch_all' => false,
        ]);

        $catchAllZone = ShippingZone::factory()->create([
            'name' => 'Rest of World',
            'countries' => null,
            'is_catch_all' => true,
        ]);

        $this->assertFalse($specificZone->isCatchAll());
        $this->assertTrue($catchAllZone->isCatchAll());
    }

    /** @test */
    public function it_applies_catch_all_zones_to_any_address()
    {
        $catchAllZone = ShippingZone::factory()->create([
            'name' => 'Rest of World',
            'countries' => null,
            'is_catch_all' => true,
        ]);

        $address1 = Address::factory()->create(['country' => 'US']);
        $address2 = Address::factory()->create(['country' => 'XY']);
        $address3 = Address::factory()->create(['country' => 'GB']);

        $this->assertTrue($catchAllZone->appliesToAddress($address1));
        $this->assertTrue($catchAllZone->appliesToAddress($address2));
        $this->assertTrue($catchAllZone->appliesToAddress($address3));
    }

    /** @test */
    public function it_applies_specific_zones_only_to_configured_countries()
    {
        $usZone = ShippingZone::factory()->create([
            'name' => 'US Zone',
            'countries' => ['US'],
            'is_catch_all' => false,
        ]);

        $usAddress = Address::factory()->create(['country' => 'US']);
        $gbAddress = Address::factory()->create(['country' => 'GB']);

        $this->assertTrue($usZone->appliesToAddress($usAddress));
        $this->assertFalse($usZone->appliesToAddress($gbAddress));
    }

    /** @test */
    public function it_gets_catch_all_zones_correctly()
    {
        ShippingZone::factory()->create([
            'name' => 'US Zone',
            'countries' => ['US'],
            'is_catch_all' => false,
            'is_active' => true,
        ]);

        ShippingZone::factory()->create([
            'name' => 'Europe Zone',
            'countries' => ['GB', 'DE', 'FR'],
            'is_catch_all' => false,
            'is_active' => true,
        ]);

        $catchAllZone1 = ShippingZone::factory()->create([
            'name' => 'Rest of World',
            'countries' => null,
            'is_catch_all' => true,
            'is_active' => true,
            'display_order' => 999,
        ]);

        $catchAllZone2 = ShippingZone::factory()->create([
            'name' => 'Backup Zone',
            'countries' => null,
            'is_catch_all' => true,
            'is_active' => true,
            'display_order' => 1000,
        ]);

        // Inactive catch-all zone
        ShippingZone::factory()->create([
            'name' => 'Inactive Catch-All',
            'countries' => null,
            'is_catch_all' => true,
            'is_active' => false,
        ]);

        $catchAllZones = ShippingZone::getCatchAllZones();

        $this->assertCount(2, $catchAllZones);
        $this->assertTrue($catchAllZones->contains($catchAllZone1));
        $this->assertTrue($catchAllZones->contains($catchAllZone2));
        
        // Should be ordered by display_order
        $this->assertEquals($catchAllZone1->id, $catchAllZones->first()->id);
    }

    /** @test */
    public function it_gets_specific_zones_correctly()
    {
        $usZone = ShippingZone::factory()->create([
            'name' => 'US Zone',
            'countries' => ['US'],
            'is_catch_all' => false,
            'is_active' => true,
        ]);

        $europeZone = ShippingZone::factory()->create([
            'name' => 'Europe Zone',
            'countries' => ['GB', 'DE', 'FR'],
            'is_catch_all' => false,
            'is_active' => true,
        ]);

        ShippingZone::factory()->create([
            'name' => 'Rest of World',
            'countries' => null,
            'is_catch_all' => true,
            'is_active' => true,
        ]);

        // Inactive specific zone
        ShippingZone::factory()->create([
            'name' => 'Inactive Zone',
            'countries' => ['CA'],
            'is_catch_all' => false,
            'is_active' => false,
        ]);

        $specificZones = ShippingZone::getSpecificZones();

        $this->assertCount(2, $specificZones);
        $this->assertTrue($specificZones->contains($usZone));
        $this->assertTrue($specificZones->contains($europeZone));
    }

    /** @test */
    public function it_checks_if_address_is_covered_by_specific_zone()
    {
        ShippingZone::factory()->create([
            'name' => 'US Zone',
            'countries' => ['US'],
            'is_catch_all' => false,
            'is_active' => true,
        ]);

        ShippingZone::factory()->create([
            'name' => 'Europe Zone',
            'countries' => ['GB', 'DE', 'FR'],
            'is_catch_all' => false,
            'is_active' => true,
        ]);

        ShippingZone::factory()->create([
            'name' => 'Rest of World',
            'countries' => null,
            'is_catch_all' => true,
            'is_active' => true,
        ]);

        $usAddress = Address::factory()->create(['country' => 'US']);
        $gbAddress = Address::factory()->create(['country' => 'GB']);
        $unconfiguredAddress = Address::factory()->create(['country' => 'XY']);

        $this->assertTrue(ShippingZone::isAddressCoveredBySpecificZone($usAddress));
        $this->assertTrue(ShippingZone::isAddressCoveredBySpecificZone($gbAddress));
        $this->assertFalse(ShippingZone::isAddressCoveredBySpecificZone($unconfiguredAddress));
    }

    /** @test */
    public function it_handles_postal_code_patterns_correctly()
    {
        $zone = ShippingZone::factory()->create([
            'name' => 'US Zone',
            'countries' => ['US'],
            'postal_codes' => ['90210', '100*', '20000-30000'],
            'is_catch_all' => false,
        ]);

        // Exact match
        $address1 = Address::factory()->create([
            'country' => 'US',
            'postal_code' => '90210'
        ]);

        // Wildcard match
        $address2 = Address::factory()->create([
            'country' => 'US',
            'postal_code' => '10001'
        ]);

        // Range match
        $address3 = Address::factory()->create([
            'country' => 'US',
            'postal_code' => '25000'
        ]);

        // No match
        $address4 = Address::factory()->create([
            'country' => 'US',
            'postal_code' => '50000'
        ]);

        $this->assertTrue($zone->appliesToAddress($address1));
        $this->assertTrue($zone->appliesToAddress($address2));
        $this->assertTrue($zone->appliesToAddress($address3));
        $this->assertFalse($zone->appliesToAddress($address4));
    }

    /** @test */
    public function it_handles_region_restrictions_correctly()
    {
        $zone = ShippingZone::factory()->create([
            'name' => 'US West Coast',
            'countries' => ['US'],
            'regions' => ['CA', 'OR', 'WA'],
            'is_catch_all' => false,
        ]);

        $caAddress = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA'
        ]);

        $nyAddress = Address::factory()->create([
            'country' => 'US',
            'region' => 'NY'
        ]);

        $this->assertTrue($zone->appliesToAddress($caAddress));
        $this->assertFalse($zone->appliesToAddress($nyAddress));
    }
}
