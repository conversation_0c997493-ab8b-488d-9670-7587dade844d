<?php

namespace Tests\Feature;

use App\Models\Address;
use App\Models\TaxRate;
use App\Services\TaxService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class TaxServiceTest extends TestCase
{
    use RefreshDatabase;

    private TaxService $taxService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set default country for testing
        Config::set('app.default_country', 'US');
        
        $this->taxService = app(TaxService::class);
        
        // Create a default tax rate for testing
        TaxRate::factory()->create([
            'name' => 'Global Default',
            'country' => 'ZZ',
            'rate' => 10.0,
            'is_default' => true,
            'is_active' => true,
            'priority' => 0,
            'is_compound' => false,
        ]);
        
        // Create some test tax rates
        TaxRate::factory()->create([
            'name' => 'California Tax',
            'country' => 'US',
            'region' => 'CA',
            'rate' => 7.25,
            'is_active' => true,
            'priority' => 1,
            'is_compound' => false,
        ]);
        
        TaxRate::factory()->create([
            'name' => 'New York Tax',
            'country' => 'US',
            'region' => 'NY',
            'rate' => 8.875,
            'is_active' => true,
            'priority' => 1,
            'is_compound' => false,
        ]);
    }
    
    /** @test */
    public function it_returns_default_tax_rate_when_no_match_found()
    {
        $taxRate = $this->taxService->getTaxRateByLocation('XX');
        
        $this->assertEquals('ZZ', $taxRate->country);
        $this->assertEquals(10.0, (float) $taxRate->rate);
        $this->assertTrue($taxRate->is_default);
    }
    
    /** @test */
    public function it_returns_country_level_tax_rate_when_region_not_provided()
    {
        // Create a country-level tax rate
        TaxRate::factory()->create([
            'name' => 'Canada Tax',
            'country' => 'CA',
            'region' => null,
            'rate' => 5.0,
            'is_active' => true,
            'priority' => 1,
            'is_compound' => false,
        ]);
        
        $taxRate = $this->taxService->getTaxRateByLocation('CA');
        
        $this->assertEquals('CA', $taxRate->country);
        $this->assertNull($taxRate->region);
        $this->assertEquals(5.0, (float) $taxRate->rate);
    }
    
    /** @test */
    public function it_returns_region_specific_tax_rate_when_available()
    {
        $taxRate = $this->taxService->getTaxRateByLocation('US', 'CA');
        
        $this->assertEquals('US', $taxRate->country);
        $this->assertEquals('CA', $taxRate->region);
        $this->assertEquals(7.25, (float) $taxRate->rate);
    }
    
    /** @test */
    public function it_handles_missing_address()
    {
        // This should use the default tax rate (which is for country 'ZZ' in our test setup)
        $taxRate = $this->taxService->getTaxRateByLocation('US');
        
        // The default tax rate is set to country 'ZZ' in the test setup
        $this->assertEquals('ZZ', $taxRate->country);
        $this->assertEquals(10.0, (float) $taxRate->rate);
    }
    
    /** @test */
    public function it_calculates_tax_correctly()
    {
        // Test with a specific region
        $tax = $this->taxService->calculateTax(100.0, 'US', 'CA');
        $this->assertEquals(7.25, $tax);
        
        // Test with default rate
        $tax = $this->taxService->calculateTax(100.0, 'XX');
        $this->assertEquals(10.0, $tax);
    }
    
    /** @test */
    public function it_handles_invalid_country_code()
    {
        // Should fall back to default rate
        $tax = $this->taxService->calculateTax(100.0, 'INVALID');
        $this->assertEquals(10.0, $tax);
        
        // Empty country code should also work
        $tax = $this->taxService->calculateTax(100.0, '');
        $this->assertEquals(10.0, $tax);
    }
    
    /** @test */
    public function it_handles_zero_amount()
    {
        $tax = $this->taxService->calculateTax(0, 'US', 'CA');
        $this->assertEquals(0, $tax);
    }
    
    /** @test */
    public function it_handles_negative_amount()
    {
        $tax = $this->taxService->calculateTax(-100, 'US', 'CA');
        $this->assertEquals(0, $tax);
    }
}
