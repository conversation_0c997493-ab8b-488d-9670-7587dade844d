<?php

namespace Tests\Feature\Checkout;

use App\Models\Address;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use App\Models\ShippingMethod;
use App\Models\ShippingRate;
use App\Models\ShippingZone;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class ShippingValidationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Cart $cart;
    protected Address $address;
    protected ShippingZone $domesticZone;
    protected ShippingMethod $standardMethod;
    protected ShippingMethod $expressMethod;
    protected string $checkoutId;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user
        $this->user = User::factory()->create();

        // Create cart
        $this->cart = Cart::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Create product variants
        $variant1 = ProductVariant::factory()->create([
            'price' => 25.00,
            'weight' => 2.5,
            'weight_unit' => 'kg',
        ]);

        $variant2 = ProductVariant::factory()->create([
            'price' => 15.00,
            'weight' => 1.5,
            'weight_unit' => 'kg',
        ]);

        // Add items to cart
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_variant_id' => $variant1->id,
            'quantity' => 2,
            'unit_price' => $variant1->price,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_variant_id' => $variant2->id,
            'quantity' => 1,
            'unit_price' => $variant2->price,
        ]);

        // Create address
        $this->address = Address::factory()->create([
            'country' => 'US',
            'region' => 'CA',
        ]);

        // Create shipping zone
        $this->domesticZone = ShippingZone::factory()->create([
            'name' => 'Domestic',
            'countries' => ['US'],
        ]);

        // Create shipping methods
        $this->standardMethod = ShippingMethod::factory()->create([
            'code' => 'standard',
            'name' => 'Standard Shipping',
            'method_type' => 'flat_rate',
        ]);

        $this->expressMethod = ShippingMethod::factory()->create([
            'code' => 'express',
            'name' => 'Express Shipping',
            'method_type' => 'flat_rate',
        ]);

        // Create shipping rates
        ShippingRate::factory()->create([
            'shipping_zone_id' => $this->domesticZone->id,
            'shipping_method_id' => $this->standardMethod->id,
            'base_rate' => 5.99,
        ]);

        ShippingRate::factory()->create([
            'shipping_zone_id' => $this->domesticZone->id,
            'shipping_method_id' => $this->expressMethod->id,
            'base_rate' => 12.99,
        ]);

        // Generate checkout ID
        $this->checkoutId = uniqid('checkout_', true);
        Session::put('checkout_id', $this->checkoutId);
    }

    /** @test */
    public function it_validates_shipping_method_during_checkout()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('checkout.process'), [
            'billing_address' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address_line1' => '123 Main St',
                'city' => 'Los Angeles',
                'region' => 'CA',
                'postal_code' => '90210',
                'country' => 'US',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
            ],
            'use_billing_for_shipping' => true,
            'payment_method' => 'stripe',
            'shipping_method' => 'standard', // Valid shipping method
            'terms_accepted' => true,
            'checkout_id' => $this->checkoutId,
        ]);

        $response->assertRedirect();
        $response->assertSessionDoesntHaveErrors();
    }

    /** @test */
    public function it_rejects_invalid_shipping_method_during_checkout()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('checkout.process'), [
            'billing_address' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address_line1' => '123 Main St',
                'city' => 'Los Angeles',
                'region' => 'CA',
                'postal_code' => '90210',
                'country' => 'US',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
            ],
            'use_billing_for_shipping' => true,
            'payment_method' => 'stripe',
            'shipping_method' => 'invalid_method', // Invalid shipping method
            'terms_accepted' => true,
            'checkout_id' => $this->checkoutId,
        ]);

        $response->assertSessionHasErrors('shipping_method');
    }

    /** @test */
    public function it_calculates_correct_shipping_cost_for_standard_shipping()
    {
        $this->actingAs($this->user);

        // Mock the session to store the cart ID
        Session::put('cart_id', $this->cart->id);

        // First, get the checkout page to see the available shipping methods
        $response = $this->get(route('checkout.index'));
        $response->assertStatus(200);

        // Check that the standard shipping method is available with the correct price
        $response->assertViewHas('shippingMethods', function ($shippingMethods) {
            return isset($shippingMethods['standard']) && 
                   $shippingMethods['standard']['price'] == 5.99;
        });
    }

    /** @test */
    public function it_calculates_correct_shipping_cost_for_express_shipping()
    {
        $this->actingAs($this->user);

        // Mock the session to store the cart ID
        Session::put('cart_id', $this->cart->id);

        // First, get the checkout page to see the available shipping methods
        $response = $this->get(route('checkout.index'));
        $response->assertStatus(200);

        // Check that the express shipping method is available with the correct price
        $response->assertViewHas('shippingMethods', function ($shippingMethods) {
            return isset($shippingMethods['express']) && 
                   $shippingMethods['express']['price'] == 12.99;
        });
    }
}
