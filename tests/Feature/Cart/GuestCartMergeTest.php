<?php

namespace Tests\Feature\Cart;

use App\Listeners\MergeGuestCartWithUserCart;
use App\Models\Cart;
use App\Models\InventoryItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Services\CartService;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GuestCartMergeTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected User $user;
    protected ProductVariant $productVariant;

    public function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create();

        // Create a test product and variant
        $product = Product::factory()->create();
        $this->productVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
        ]);

        // Create inventory item for the product variant
        InventoryItem::factory()->create([
            'product_variant_id' => $this->productVariant->id,
            'quantity_on_hand' => 100,
            'quantity_reserved' => 0,
            'track_inventory' => true,
            'allow_backorder' => false,
        ]);

        // Get the cart service
        $this->cartService = app(CartService::class);
    }

    public function test_guest_cart_is_merged_with_user_cart_on_login(): void
    {
        // Create a guest cart and add an item
        $guestCart = Cart::factory()->create([
            'session_id' => 'test-session-id',
            'status' => 'active',
            'user_id' => null,
        ]);

        $this->cartService->addItemToCart($guestCart, $this->productVariant->id, 2);

        // Refresh the cart to get the items
        $guestCart = $this->cartService->getCartById($guestCart->id);

        // Verify the guest cart has the item
        $this->assertEquals(1, $guestCart->items->count());
        $this->assertEquals(2, $guestCart->items->first()->quantity);

        // Login the user
        $this->actingAs($this->user);

        // Create and dispatch the Login event
        $loginEvent = new Login('web', $this->user, false);

        // Manually call the listener
        $listener = new MergeGuestCartWithUserCart($this->cartService);
        $listener->handle($loginEvent);

        // Get the user's cart
        $userCart = $this->cartService->getOrCreateCartForUser($this->user);

        // Verify the user's cart now has the item from the guest cart
        $this->assertEquals(1, $userCart->items->count());
        $this->assertEquals(2, $userCart->items->first()->quantity);

        // Verify the guest cart no longer exists
        $this->assertNull(Cart::where('id', $guestCart->id)->first());
    }

    public function test_guest_cart_items_are_added_to_existing_user_cart(): void
    {
        // Create a guest cart and add an item
        $guestCart = Cart::factory()->create([
            'session_id' => 'test-session-id-2',
            'status' => 'active',
            'user_id' => null,
        ]);

        $this->cartService->addItemToCart($guestCart, $this->productVariant->id, 2);

        // Refresh the guest cart to get the items
        $guestCart = $this->cartService->getCartById($guestCart->id);

        // Create a user cart with the same item but different quantity
        $userCart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($userCart, $this->productVariant->id, 3);

        // Refresh the user cart to get the items
        $userCart = $this->cartService->getCartByUserId($this->user->id);

        // Verify initial state
        $this->assertEquals(1, $guestCart->items->count());
        $this->assertEquals(2, $guestCart->items->first()->quantity);
        $this->assertEquals(1, $userCart->items->count());
        $this->assertEquals(3, $userCart->items->first()->quantity);

        // Login the user
        $this->actingAs($this->user);

        // Create and dispatch the Login event
        $loginEvent = new Login('web', $this->user, false);

        // Manually call the listener
        $listener = new MergeGuestCartWithUserCart($this->cartService);
        $listener->handle($loginEvent);

        // Get the user's cart
        $userCart = $this->cartService->getOrCreateCartForUser($this->user);

        // Verify the user's cart now has the combined quantity
        $this->assertEquals(1, $userCart->items->count());
        $this->assertEquals(5, $userCart->items->first()->quantity);

        // Verify the guest cart no longer exists
        $this->assertNull(Cart::where('id', $guestCart->id)->first());
    }
}
