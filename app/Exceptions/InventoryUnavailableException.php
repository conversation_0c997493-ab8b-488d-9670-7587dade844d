<?php

namespace App\Exceptions;

use Exception;

class InventoryUnavailableException extends Exception
{
    protected array $unavailableItems;

    /**
     * @param string $message
     * @param array $unavailableItems
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "", array $unavailableItems = [], int $code = 0, ?\Throwable $previous = null)
    {
        $this->unavailableItems = $unavailableItems;
        parent::__construct($message, $code, $previous);
    }

    /**
     * Get the list of unavailable items.
     *
     * @return array
     */
    public function getUnavailableItems(): array
    {
        return $this->unavailableItems;
    }
}
