<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ShippingZone
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property array|null $countries
 * @property array|null $regions
 * @property array|null $postal_codes
 * @property int $display_order
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\ShippingRate[] $rates
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property-read int|null $rates_count
 * @method static \Database\Factories\ShippingZoneFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereCountries($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone wherePostalCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereRegions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingZone withoutTrashed()
 * @mixin \Eloquent
 */
class ShippingZone extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'description',
        'countries',
        'regions',
        'postal_codes',
        'is_catch_all',
        'display_order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'countries' => 'array',
        'regions' => 'array',
        'postal_codes' => 'array',
        'is_catch_all' => 'boolean',
        'display_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the shipping rates for the zone.
     */
    public function rates(): HasMany
    {
        return $this->hasMany(ShippingRate::class);
    }

    /**
     * Check if this zone applies to a given address.
     *
     * @param Address $address
     * @return bool
     */
    public function appliesToAddress(Address $address): bool
    {
        // Catch-all zones apply to any address (handled by shipping service logic)
        if ($this->is_catch_all) {
            return true;
        }

        // Check if the zone has any countries defined
        if (empty($this->countries)) {
            return false;
        }

        // Check if the address country is in the zone countries
        if (!in_array($address->country, $this->countries)) {
            return false;
        }

        // Check regions if defined
        if (!empty($this->regions) && !empty($address->region)) {
            if (!in_array($address->region, $this->regions)) {
                return false;
            }
        }

        // Check postal codes if defined
        if (!empty($this->postal_codes) && !empty($address->postal_code)) {
            $matchesPostalCode = false;

            foreach ($this->postal_codes as $pattern) {
                // Check for range (e.g., 10000-20000)
                if (strpos($pattern, '-') !== false) {
                    list($min, $max) = explode('-', $pattern);
                    if ($address->postal_code >= $min && $address->postal_code <= $max) {
                        $matchesPostalCode = true;
                        break;
                    }
                }
                // Check for wildcard (e.g., 100*)
                elseif (strpos($pattern, '*') !== false) {
                    $regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';
                    if (preg_match($regex, $address->postal_code)) {
                        $matchesPostalCode = true;
                        break;
                    }
                }
                // Exact match
                elseif ($pattern === $address->postal_code) {
                    $matchesPostalCode = true;
                    break;
                }
            }

            if (!$matchesPostalCode) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if this zone is a catch-all zone.
     *
     * @return bool
     */
    public function isCatchAll(): bool
    {
        return $this->is_catch_all;
    }

    /**
     * Get all catch-all zones.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getCatchAllZones()
    {
        return static::where('is_catch_all', true)
            ->where('is_active', true)
            ->orderBy('display_order')
            ->get();
    }

    /**
     * Get all specific (non-catch-all) zones.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSpecificZones()
    {
        return static::where('is_catch_all', false)
            ->where('is_active', true)
            ->orderBy('display_order')
            ->get();
    }

    /**
     * Check if an address is covered by any specific (non-catch-all) zone.
     *
     * @param Address $address
     * @return bool
     */
    public static function isAddressCoveredBySpecificZone(Address $address): bool
    {
        $specificZones = static::getSpecificZones();

        foreach ($specificZones as $zone) {
            if ($zone->appliesToAddress($address)) {
                return true;
            }
        }

        return false;
    }
}
