<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\OrderItem
 *
 * @property string $id
 * @property string $order_id
 * @property string|null $product_variant_id
 * @property string $product_name
 * @property string|null $variant_name
 * @property string|null $sku
 * @property int $quantity // Cast to integer
 * @property string $unit_price // Cast to decimal:2
 * @property string $subtotal // Cast to decimal:2
 * @property string $tax_amount // Cast to decimal:2
 * @property string $discount_amount // Cast to decimal:2
 * @property string $total // Cast to decimal:2
 * @property array|null $product_snapshot // Cast to json
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_price
 * @property-read string $formatted_total
 * @property-read string $name
 * @property-read array|null $options
 * @property-read \App\Models\Order|null $order
 * @property-read \App\Models\ProductVariant|null $productVariant
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereDiscountAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereProductName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereProductSnapshot($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereProductVariantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereSubtotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereTaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereVariantName($value)
 * @mixin \Eloquent
 */
class OrderItem extends Model
{
    use HasFactory, HasUuids;
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'order_id',
        'product_variant_id',
        'product_name',
        'variant_name',
        'sku',
        'quantity',
        'unit_price',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total',
        'product_snapshot',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'product_snapshot' => 'json',
    ];

    /**
     * Get the order that owns the item.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product variant that owns the item.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->formatPrice($this->unit_price);
    }

    /**
     * Get the formatted total.
     */
    public function getFormattedTotalAttribute(): string
    {
        return $this->formatPrice($this->total);
    }

    /**
     * Get the item options.
     */
    public function getOptionsAttribute(): ?array
    {
        if (!$this->product_snapshot || !isset($this->product_snapshot['options'])) {
            return null;
        }

        return $this->product_snapshot['options'];
    }

    /**
     * Get the item name.
     */
    public function getNameAttribute(): string
    {
        $name = $this->product_name;

        if ($this->variant_name) {
            $name .= ' - ' . $this->variant_name;
        }

        return $name;
    }

    /**
     * Format a price with the order's currency.
     */
    protected function formatPrice($price): string
    {
        $currencySymbol = '$'; // Default to USD

        if ($this->order && $this->order->currency) {
            // Add more currency symbols as needed
            $symbols = [
                'USD' => '$',
                'EUR' => '€',
                'GBP' => '£',
                'JPY' => '¥',
            ];

            if (isset($symbols[$this->order->currency])) {
                $currencySymbol = $symbols[$this->order->currency];
            }
        }

        return $currencySymbol . number_format((float) $price, 2, '.', ',');
    }
}
