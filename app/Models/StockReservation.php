<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\StockReservation
 *
 * @property string $id
 * @property string $product_variant_id
 * @property string $cart_id
 * @property int $quantity
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property-read \App\Models\Cart|null $cart
 * @property-read \App\Models\ProductVariant|null $productVariant
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereCartId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereProductVariantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StockReservation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StockReservation extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'product_variant_id',
        'cart_id',
        'quantity',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'integer',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the product variant that owns the reservation.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the cart that owns the reservation.
     */
    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    /**
     * Check if the reservation is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }
}
