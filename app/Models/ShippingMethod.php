<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ShippingMethod
 *
 * @property string $id
 * @property string $code
 * @property string $name
 * @property string|null $description
 * @property string $method_type
 * @property int $display_order
 * @property bool $is_active
 * @property bool $is_free_shipping_eligible
 * @property float|null $minimum_order_amount
 * @property float|null $maximum_order_amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\ShippingRate[] $rates
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property-read int|null $rates_count
 * @method static \Database\Factories\ShippingMethodFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereIsFreeShippingEligible($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereMaximumOrderAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereMethodType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereMinimumOrderAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingMethod withoutTrashed()
 * @mixin \Eloquent
 */
class ShippingMethod extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'method_type',
        'display_order',
        'is_active',
        'is_free_shipping_eligible',
        'minimum_order_amount',
        'maximum_order_amount',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'display_order' => 'integer',
        'is_active' => 'boolean',
        'is_free_shipping_eligible' => 'boolean',
        'minimum_order_amount' => 'decimal:2',
        'maximum_order_amount' => 'decimal:2',
    ];

    /**
     * Get the shipping rates for the method.
     */
    public function rates(): HasMany
    {
        return $this->hasMany(ShippingRate::class);
    }

    /**
     * Check if this method is available for a given cart subtotal.
     *
     * @param float $subtotal
     * @return bool
     */
    public function isAvailableForSubtotal(float $subtotal): bool
    {
        // Check minimum order amount
        if ($this->minimum_order_amount !== null && $subtotal < $this->minimum_order_amount) {
            return false;
        }

        // Check maximum order amount
        if ($this->maximum_order_amount !== null && $subtotal > $this->maximum_order_amount) {
            return false;
        }

        return true;
    }
}
