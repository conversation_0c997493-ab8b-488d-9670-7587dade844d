<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * 
 *
 * @property string $id
 * @property string $key
 * @property string|null $location
 * @property bool $is_active
 * @property array<array-key, mixed>|null $title
 * @property array<array-key, mixed>|null $content
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \App\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read mixed $translations
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereJsonContainsLocale(string $column, string $locale, ?mixed $value, string $operand = '=')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereJsonContainsLocales(string $column, array $locales, ?mixed $value, string $operand = '=')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereLocale(string $column, string $locale)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereLocales(string $column, array $locales)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentBlock withoutTrashed()
 * @mixin \Eloquent
 */
class ContentBlock extends Model implements HasMedia
{
    use HasFactory, HasUuids, HasTranslations, InteractsWithMedia, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'location',
        'is_active',
        'title',
        'content',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * The attributes that are translatable.
     *
     * @var array<int, string>
     */
    public $translatable = [
        'title',
        'content',
    ];

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->withResponsiveImages();
    }
}
