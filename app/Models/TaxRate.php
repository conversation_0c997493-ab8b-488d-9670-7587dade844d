<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\TaxRate
 *
 * @property string $id
 * @property string $name
 * @property string $country ISO 3166-1 alpha-2 code
 * @property string|null $region
 * @property string $rate // Cast to decimal:4
 * @property string|null $description
 * @property string|null $tax_category
 * @property bool $is_compound // Cast to boolean
 * @property int $priority // Cast to integer
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $valid_from
 * @property \Illuminate\Support\Carbon|null $valid_until
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\TaxRule[] $activeTaxRules
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\TaxRule[] $taxRules
 * @method bool isValidForDate(\DateTime $date = null)
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property bool $is_default
 * @property-read int|null $active_tax_rules_count
 * @property-read int|null $tax_rules_count
 * @method static \Database\Factories\TaxRateFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereIsCompound($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereTaxCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereValidFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRate whereValidUntil($value)
 * @mixin \Eloquent
 */
class TaxRate extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'country',
        'region',
        'rate',
        'description',
        'tax_category',
        'is_compound',
        'priority',
        'is_active',
        'is_default',
        'valid_from',
        'valid_until',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rate' => 'decimal:4',
        'is_compound' => 'boolean',
        'priority' => 'integer',
        'is_active' => 'boolean',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
    ];

    /**
     * Check if the tax rate is valid for the given date.
     */
    public function isValidForDate(\DateTime $date = null): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $date = $date ?? now();

        if ($this->valid_from && $date < $this->valid_from) {
            return false;
        }

        if ($this->valid_until && $date > $this->valid_until) {
            return false;
        }

        return true;
    }

    /**
     * Get the tax rules for this tax rate.
     */
    public function taxRules(): HasMany
    {
        return $this->hasMany(TaxRule::class);
    }

    /**
     * Get active tax rules for this tax rate.
     */
    public function activeTaxRules(): HasMany
    {
        return $this->taxRules()->where('is_active', true);
    }
}
