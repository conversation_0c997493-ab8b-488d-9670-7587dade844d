<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Services\Shipping\Utilities\WeightCalculator;

/**
 * App\Models\ShippingRate
 *
 * @property string $id
 * @property string $shipping_zone_id
 * @property string $shipping_method_id
 * @property float $base_rate
 * @property float|null $per_item_rate
 * @property float|null $per_weight_rate
 * @property string $weight_unit
 * @property float|null $min_weight
 * @property float|null $max_weight
 * @property float|null $min_price
 * @property float|null $max_price
 * @property array|null $conditions
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ShippingMethod $method
 * @property-read \App\Models\ShippingZone $zone
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @method static \Database\Factories\ShippingRateFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereBaseRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereConditions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereMaxPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereMaxWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereMinPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereMinWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate wherePerItemRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate wherePerWeightRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereShippingMethodId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereShippingZoneId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ShippingRate whereWeightUnit($value)
 * @mixin \Eloquent
 */
class ShippingRate extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'shipping_zone_id',
        'shipping_method_id',
        'base_rate',
        'per_item_rate',
        'per_weight_rate',
        'weight_unit',
        'min_weight',
        'max_weight',
        'min_price',
        'max_price',
        'conditions',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'base_rate' => 'decimal:2',
        'per_item_rate' => 'decimal:2',
        'per_weight_rate' => 'decimal:2',
        'min_weight' => 'decimal:2',
        'max_weight' => 'decimal:2',
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',
        'conditions' => 'json',
        'is_active' => 'boolean',
    ];

    /**
     * Get the shipping zone that owns the rate.
     */
    public function zone(): BelongsTo
    {
        return $this->belongsTo(ShippingZone::class, 'shipping_zone_id');
    }

    /**
     * Get the shipping method that owns the rate.
     */
    public function method(): BelongsTo
    {
        return $this->belongsTo(ShippingMethod::class, 'shipping_method_id');
    }

    /**
     * Check if this rate applies to a given cart.
     *
     * @param Cart $cart
     * @return bool
     */
    public function appliesToCart(Cart $cart): bool
    {
        $totalWeight = $this->calculateCartWeight($cart);
        $subtotal = $cart->subtotal;

        \Log::debug("Checking if rate applies to cart: weight={$totalWeight}, subtotal={$subtotal}");

        // Check weight constraints
        if ($this->min_weight !== null && $totalWeight < $this->min_weight) {
            \Log::debug("Rate does not apply: weight {$totalWeight} < min_weight {$this->min_weight}");
            return false;
        }

        if ($this->max_weight !== null && $totalWeight > $this->max_weight) {
            \Log::debug("Rate does not apply: weight {$totalWeight} > max_weight {$this->max_weight}");
            return false;
        }

        // Check price constraints
        if ($this->min_price !== null && $subtotal < $this->min_price) {
            \Log::debug("Rate does not apply: subtotal {$subtotal} < min_price {$this->min_price}");
            return false;
        }

        if ($this->max_price !== null && $subtotal > $this->max_price) {
            \Log::debug("Rate does not apply: subtotal {$subtotal} > max_price {$this->max_price}");
            return false;
        }

        \Log::debug("Rate applies to cart");
        return true;
    }

    /**
     * Calculate the total weight of a cart.
     *
     * @param Cart $cart
     * @return float
     */
    protected function calculateCartWeight(Cart $cart): float
    {
        return WeightCalculator::calculateCartWeight($cart, $this->weight_unit);
    }
}
