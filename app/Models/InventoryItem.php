<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\InventoryItem
 *
 * @property string $id
 * @property string $product_variant_id
 * @property int $quantity_on_hand
 * @property int $quantity_reserved
 * @property bool $track_inventory
 * @property bool $allow_backorder
 * @property int|null $low_stock_threshold
 * @property bool $low_stock_notified
 * @property int|null $reorder_point
 * @property int|null $reorder_quantity
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\InventoryChangeLog[] $changeLogs
 * @property-read int $available_quantity
 * @property-read \App\Models\ProductVariant|null $productVariant
 * @method bool isInStock(int $quantity = 1)
 * @method bool isLowStock()
 * @method bool needsReorder()
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property-read int|null $change_logs_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereAllowBackorder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereLowStockNotified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereLowStockThreshold($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereProductVariantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereQuantityOnHand($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereQuantityReserved($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereReorderPoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereReorderQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereTrackInventory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryItem whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class InventoryItem extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'product_variant_id',
        'quantity_on_hand',
        'quantity_reserved',
        'track_inventory',
        'allow_backorder',
        'low_stock_threshold',
        'low_stock_notified',
        'reorder_point',
        'reorder_quantity',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity_on_hand' => 'integer',
        'quantity_reserved' => 'integer',
        'track_inventory' => 'boolean',
        'allow_backorder' => 'boolean',
        'low_stock_threshold' => 'integer',
        'low_stock_notified' => 'boolean',
        'reorder_point' => 'integer',
        'reorder_quantity' => 'integer',
    ];

    /**
     * Get the product variant that owns the inventory item.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the change logs for the inventory item.
     */
    public function changeLogs(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(InventoryChangeLog::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the available quantity.
     */
    public function getAvailableQuantityAttribute(): int
    {
        return max(0, $this->quantity_on_hand - $this->quantity_reserved);
    }

    /**
     * Check if the variant is in stock.
     */
    public function isInStock(int $quantity = 1): bool
    {
        if (!$this->track_inventory) {
            return true;
        }

        if ($this->allow_backorder) {
            return true;
        }

        return $this->available_quantity >= $quantity;
    }

    /**
     * Check if the inventory item is at or below the low stock threshold.
     */
    public function isLowStock(): bool
    {
        if (!$this->track_inventory) {
            return false;
        }

        $threshold = $this->low_stock_threshold ?? config('inventory.low_stock_threshold', 5);
        return $this->quantity_on_hand <= $threshold;
    }

    /**
     * Check if the inventory item needs to be reordered.
     */
    public function needsReorder(): bool
    {
        if (!$this->track_inventory || !$this->reorder_point) {
            return false;
        }

        return $this->quantity_on_hand <= $this->reorder_point;
    }
}
