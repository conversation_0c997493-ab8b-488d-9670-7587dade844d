<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\InventoryChangeLog
 *
 * @property string $id
 * @property string $inventory_item_id
 * @property string $product_variant_id
 * @property int $quantity_change
 * @property int $reserved_change
 * @property int $quantity_after
 * @property int $reserved_after
 * @property string $reason
 * @property string|null $reference_type
 * @property string|null $reference_id
 * @property string $user_id
 * @property array|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\InventoryItem|null $inventoryItem
 * @property-read \App\Models\ProductVariant|null $productVariant
 * @property-read \App\Models\User|null $user
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereInventoryItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereProductVariantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereQuantityAfter($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereQuantityChange($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereReferenceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereReservedAfter($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereReservedChange($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryChangeLog whereUserId($value)
 * @mixin \Eloquent
 */
class InventoryChangeLog extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'inventory_item_id',
        'product_variant_id',
        'quantity_change',
        'reserved_change',
        'quantity_after',
        'reserved_after',
        'reason',
        'reference_type',
        'reference_id',
        'user_id',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity_change' => 'integer',
        'reserved_change' => 'integer',
        'quantity_after' => 'integer',
        'reserved_after' => 'integer',
        'metadata' => 'json',
    ];

    /**
     * Get the inventory item that owns the log entry.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get the product variant that owns the log entry.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the user that created the log entry.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
