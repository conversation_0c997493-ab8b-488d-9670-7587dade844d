<?php

namespace App\Models;

use Illuminate\Support\Facades\Log;

/**
 * 
 *
 * @deprecated Use App\Models\Setting instead. This class will be removed in a future version.
 * @property string $id
 * @property string|null $key
 * @property string|null $value
 * @property string $category
 * @property string $type
 * @property array<array-key, mixed>|null $options
 * @property bool $is_public
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereIsPublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Settings whereValue($value)
 * @mixin \Eloquent
 */
class Settings extends Setting
{
    /**
     * @deprecated Use Setting::get() instead. This method will be removed in a future version.
     */
    public static function get(string $key, $default = null)
    {
        Log::warning('Deprecated: Using Settings::get() - use Setting::get() instead');
        return parent::get($key, $default);
    }

    /**
     * @deprecated Use Setting::set() instead. This method will be removed in a future version.
     */
    public static function set(string $key, $value, string $category = 'general', string $type = 'text', array $options = [], bool $isPublic = true)
    {
        Log::warning('Deprecated: Using Settings::set() - use Setting::set() instead');
        return parent::set($key, $value, $category, $type, $options, $isPublic);
    }

    /**
     * @deprecated This method is no longer supported and will be removed in a future version.
     */
    public static function getData()
    {
        Log::warning('Deprecated: Settings::getData() is no longer supported');
        return null;
    }
}
