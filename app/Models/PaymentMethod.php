<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\PaymentMethod
 *
 * @property string $id
 * @property string $payment_gateway_id
 * @property string $method_key
 * @property string|null $type
 * @property bool $default_card
 * @property string|null $fingerprint
 * @property string|null $owner_name
 * @property string|null $network
 * @property string|null $last4
 * @property string|null $expires
 * @property string $origin
 * @property string|null $verification_check
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\PaymentGateway $paymentGateway
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereDefaultCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereExpires($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereFingerprint($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereLast4($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereMethodKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereNetwork($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereOrigin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereOwnerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod wherePaymentGatewayId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereVerificationCheck($value)
 * @mixin \Eloquent
 */
class PaymentMethod extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'payment_gateway_id',
        'method_key',
        'type',
        'default_card',
        'fingerprint',
        'owner_name',
        'network',
        'last4',
        'expires',
        'origin',
        'verification_check',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'default_card' => 'boolean',
    ];

    /**
     * Get the payment gateway that owns the payment method.
     */
    public function paymentGateway(): BelongsTo
    {
        return $this->belongsTo(PaymentGateway::class);
    }
}
