<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\TaxRule
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property string $tax_rate_id
 * @property string|null $country
 * @property string|null $state
 * @property string|null $city
 * @property string|null $postal_code
 * @property string|null $postal_code_pattern
 * @property int $priority // Cast to integer
 * @property string|null $product_tax_category
 * @property string|null $customer_tax_class
 * @property bool $is_active // Cast to boolean
 * @property \Illuminate\Support\Carbon|null $starts_at // Cast to datetime
 * @property \Illuminate\Support\Carbon|null $ends_at // Cast to datetime
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\TaxRate|null $taxRate
 * @method bool isValidForDate(\DateTime $date = null)
 * @method bool appliesToAddress(\App\Models\Address $address)
 * @method bool appliesToProduct(\App\Models\Product $product)
 * @method bool appliesToCustomer(\App\Models\User $customer)
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereCustomerTaxClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereEndsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule wherePostalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule wherePostalCodePattern($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereProductTaxCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereStartsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereTaxRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TaxRule whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TaxRule extends Model
{
    use HasFactory, HasUuids;
    protected $fillable = [
        'name',
        'description',
        'tax_rate_id',
        'country',
        'state',
        'city',
        'postal_code',
        'postal_code_pattern',
        'priority',
        'product_tax_category',
        'customer_tax_class',
        'is_active',
        'starts_at',
        'ends_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'priority' => 'integer',
        'is_active' => 'boolean',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    /**
     * Get the tax rate that owns the rule.
     */
    public function taxRate(): BelongsTo
    {
        return $this->belongsTo(TaxRate::class);
    }

    /**
     * Check if the rule is valid for the current date.
     */
    public function isValidForDate(\DateTime $date = null): bool
    {
        $date = $date ?? now();
        
        if (!$this->is_active) {
            return false;
        }
        
        if ($this->starts_at && $date < $this->starts_at) {
            return false;
        }
        
        if ($this->ends_at && $date > $this->ends_at) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if the rule applies to a specific address.
     */
    public function appliesToAddress(Address $address): bool
    {
        // Check country
        if ($this->country && $this->country !== $address->country) {
            return false;
        }
        
        // Check state
        if ($this->state && $this->state !== $address->state) {
            return false;
        }
        
        // Check city
        if ($this->city && $this->city !== $address->city) {
            return false;
        }
        
        // Check postal code
        if ($this->postal_code && $this->postal_code !== $address->postal_code) {
            return false;
        }
        
        // Check postal code pattern
        if ($this->postal_code_pattern && !preg_match($this->postal_code_pattern, $address->postal_code)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if the rule applies to a specific product.
     */
    public function appliesToProduct(Product $product): bool
    {
        // Check product tax category
        if ($this->product_tax_category && $this->product_tax_category !== ($product->tax_category ?? 'standard')) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if the rule applies to a specific customer.
     */
    public function appliesToCustomer(User $customer): bool
    {
        // Check customer tax class
        if ($this->customer_tax_class && $this->customer_tax_class !== ($customer->tax_class ?? 'standard')) {
            return false;
        }
        
        return true;
    }
}
