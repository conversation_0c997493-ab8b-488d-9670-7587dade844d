<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\PaymentGateway
 *
 * @property string $id
 * @property string $user_id
 * @property string $gateway
 * @property string $customer_id
 * @property array|null $data // Cast to array
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\PaymentMethod[] $paymentMethods
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @property-read int|null $payment_methods_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereGateway($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentGateway whereUserId($value)
 * @mixin \Eloquent
 */
class PaymentGateway extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'gateway',
        'customer_id',
        'data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data' => 'array',
    ];

    /**
     * Get the payment methods for this payment gateway.
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class);
    }
}
