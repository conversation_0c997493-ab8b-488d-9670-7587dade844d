<?php

namespace App\Http\Controllers;

use App\Exceptions\InventoryUnavailableException;
use App\Services\InventoryService;
use App\Services\OrderService;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class PaymentController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected PaymentService $paymentService,
        protected OrderService $orderService,
        protected InventoryService $inventoryService
    ) {
        $this->middleware('auth')->except(['webhook']);
    }

    /**
     * Show the payment page for an order.
     *
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
     */
    public function show(string $orderNumber)
    {
        $order = $this->orderService->getOrderByNumber($orderNumber);

        if (!$order) {
            abort(404, 'We couldn\'t find the order you\'re looking for. Please check the order number and try again.');
        }

        // Check if the order belongs to the authenticated user
        if ($order->user_id && $order->user_id !== Auth::id()) {
            abort(403, 'You don\'t have permission to view this order. Please sign in with the correct account.');
        }

        // Check if the order is already paid
        if ($order->isPaid()) {
            return redirect()->route('orders.show', $order->order_number)
                ->with('success', 'This order has already been successfully paid. You can view your order details below.');
        }

        // Get available payment methods
        $paymentMethods = $this->getAvailablePaymentMethods();

        return view('payments.show', [
            'order' => $order,
            'paymentMethods' => $paymentMethods,
        ]);
    }

    /**
     * Select a payment method for an order.
     */
    public function selectPaymentMethod(Request $request, string $orderNumber): RedirectResponse
    {
        $order = $this->orderService->getOrderByNumber($orderNumber);

        if (!$order) {
            abort(404, 'We couldn\'t find the order you\'re looking for. Please check the order number and try again.');
        }

        // Check if the order belongs to the authenticated user
        if ($order->user_id && $order->user_id !== Auth::id()) {
            abort(403, 'You don\'t have permission to access this order. Please sign in with the correct account.');
        }

        // Check if the order is already paid
        if ($order->isPaid()) {
            return redirect()->route('orders.show', $order->order_number)
                ->with('success', 'This order has already been paid. You can view your order details below.');
        }

        // Verify inventory availability before processing payment
        try {
            $inventoryCheck = $this->inventoryService->verifyInventoryForExistingOrder($order);

            if (!$inventoryCheck['success']) {
                return redirect()->route('orders.show', $order->order_number)
                    ->with('error', $inventoryCheck['message']);
            }
        } catch (\Exception $e) {
            Log::error('Error checking inventory for order payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->with('error', 'We encountered an error while checking product availability. Please try again or contact support.');
        }

        // Validate the request
        $request->validate([
            'payment_method' => 'required|string|in:stripe,paypal,bank_transfer',
        ]);

        $paymentMethod = $request->input('payment_method');

        try {
            switch ($paymentMethod) {
                case 'stripe':
                    return $this->handleStripePayment($order);
                case 'paypal':
                    return $this->handlePayPalPayment($order);
                case 'bank_transfer':
                    return $this->handleBankTransferPayment($order);
                default:
                    return redirect()->back()
                        ->with('error', 'The selected payment method is not supported. Please choose from the available payment options.');
            }
        } catch (\Exception $e) {
            Log::error('Error selecting payment method', [
                'order_id' => $order->id,
                'payment_method' => $paymentMethod,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->with('error', 'We encountered an error while processing your payment method selection. Please try again or contact support.');
        }
    }

    /**
     * Process a payment for an order.
     */
    public function process(Request $request, string $orderNumber): JsonResponse
    {
        $order = $this->orderService->getOrderByNumber($orderNumber);

        if (!$order) {
            return response()->json(['error' => 'We couldn\'t find the order you\'re looking for. Please check the order number and try again.'], 404);
        }

        // Check if the order belongs to the authenticated user
        if ($order->user_id && $order->user_id !== Auth::id()) {
            return response()->json(['error' => 'You don\'t have permission to access this order. Please sign in with the correct account.'], 403);
        }

        // Check if the order is already paid
        if ($order->isPaid()) {
            return response()->json(['error' => 'This order has already been paid. Please check your order history or contact support if you need assistance.'], 400);
        }

        // Verify inventory availability before processing payment
        try {
            $inventoryCheck = $this->inventoryService->verifyInventoryForExistingOrder($order);

            if (!$inventoryCheck['success']) {
                return response()->json(['error' => $inventoryCheck['message']], 400);
            }
        } catch (\Exception $e) {
            Log::error('Error checking inventory for order payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json(['error' => 'We encountered an error while checking product availability. Please try again or contact support.'], 500);
        }

        // Validate the request
        $request->validate([
            'payment_method' => 'required|string|in:stripe,paypal,bank_transfer',
            'payment_data' => 'required|array',
        ]);

        try {
            // Generate an idempotency key based on order and timestamp to prevent duplicate payments
            $idempotencyKey = 'pay_' . $order->id . '_' . now()->timestamp;

            // Add idempotency key to payment data
            $paymentData = array_merge($request->input('payment_data', []), [
                'idempotency_key' => $idempotencyKey,
            ]);

            // Process the payment
            $payment = $this->paymentService->processPayment(
                $order,
                $request->input('payment_method'),
                $paymentData
            );

            if (!$payment) {
                return response()->json(['error' => 'We encountered an issue processing your payment. Please try again or contact support if the problem persists.'], 500);
            }

            return response()->json([
                'success' => true,
                'payment' => $payment,
                'redirect' => route('orders.show', $order->order_number),
            ]);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'payment_method' => $request->input('payment_method'),
                'exception' => $e,
            ]);

            $errorMessage = 'We encountered an unexpected error while processing your payment. ';
            $errorMessage .= 'Please try again or contact support if the problem continues. ';
            $errorMessage .= 'Error details: ' . $e->getMessage();
            return response()->json(['error' => $errorMessage], 500);
        }
    }

    /**
     * Create a Stripe payment intent.
     */
    public function createStripeIntent(string $orderNumber): JsonResponse
    {
        $order = $this->orderService->getOrderByNumber($orderNumber);

        if (!$order) {
            return response()->json(['error' => 'We couldn\'t find the order you\'re looking for. Please check the order number and try again.'], 404);
        }

        // Check if the order belongs to the authenticated user
        if ($order->user_id && $order->user_id !== Auth::id()) {
            return response()->json(['error' => 'You don\'t have permission to access this order. Please sign in with the correct account.'], 403);
        }

        $result = $this->paymentService->createStripePaymentIntent($order);

        if (!$result['success']) {
            $errorMessage = 'We couldn\'t process your payment at this time. ';
            $errorMessage .= 'Please try again or contact support if the issue persists. ';
            $errorMessage .= 'Error: ' . $result['error'];
            return response()->json(['error' => $errorMessage], 500);
        }

        return response()->json($result['payment_intent']);
    }

    /**
     * Create a PayPal order.
     */
    public function createPayPalOrder(string $orderNumber): JsonResponse
    {
        $order = $this->orderService->getOrderByNumber($orderNumber);

        if (!$order) {
            return response()->json(['error' => 'We couldn\'t find the order you\'re looking for. Please check the order number and try again.'], 404);
        }

        // Check if the order belongs to the authenticated user
        if ($order->user_id && $order->user_id !== Auth::id()) {
            return response()->json(['error' => 'You don\'t have permission to access this order. Please sign in with the correct account.'], 403);
        }

        $returnUrl = route('payments.success', ['gateway' => 'paypal', 'order_number' => $order->order_number]);
        $cancelUrl = route('payments.cancel', ['gateway' => 'paypal', 'order_number' => $order->order_number]);

        $result = $this->paymentService->createPayPalPayment($order, $returnUrl, $cancelUrl);

        if (!$result['success']) {
            $errorMessage = 'We couldn\'t process your PayPal payment at this time. ';
            $errorMessage .= 'Please try again or contact support if the issue persists. ';
            $errorMessage .= 'Error: ' . $result['error'];
            return response()->json(['error' => $errorMessage], 500);
        }

        return response()->json($result['paypal_order']);
    }

    /**
     * Handle payment webhook.
     */
    public function webhook(Request $request): JsonResponse
    {
        $paymentProvider = $request->input('provider');
        $eventType = $request->input('type');
        $payload = $request->input('data');

        Log::info('Payment webhook received', [
            'provider' => $paymentProvider,
            'type' => $eventType,
            'payload' => $payload,
        ]);

        try {
            switch ($paymentProvider) {
                case 'stripe':
                    $this->handleStripeWebhook($eventType, $payload);
                    break;
                case 'paypal':
                    $this->handlePayPalWebhook($eventType, $payload);
                    break;
                default:
                    return response()->json(['error' => 'The selected payment provider is not supported. Please choose a different payment method.'], 400);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Webhook processing error: ' . $e->getMessage(), [
                'provider' => $paymentProvider,
                'type' => $eventType,
                'payload' => $payload,
                'exception' => $e,
            ]);

            $errorMessage = 'We encountered an unexpected error while processing the webhook. ';
            $errorMessage .= 'Please check the logs for more details. ';
            $errorMessage .= 'Error: ' . $e->getMessage();
            return response()->json(['error' => $errorMessage], 500);
        }
    }

    /**
     * Handle Stripe webhook.
     */
    protected function handleStripeWebhook(string $eventType, array $payload): void
    {
        switch ($eventType) {
            case 'payment_intent.succeeded':
                $this->handleStripePaymentSucceeded($payload);
                break;
            case 'payment_intent.payment_failed':
                $this->handleStripePaymentFailed($payload);
                break;
            case 'charge.refunded':
                $this->handleStripeRefund($payload);
                break;
        }
    }

    /**
     * Handle PayPal webhook.
     */
    protected function handlePayPalWebhook(string $eventType, array $payload): void
    {
        switch ($eventType) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                $this->handlePayPalPaymentCompleted($payload);
                break;
            case 'PAYMENT.CAPTURE.DENIED':
                $this->handlePayPalPaymentDenied($payload);
                break;
            case 'PAYMENT.CAPTURE.REFUNDED':
                $this->handlePayPalRefund($payload);
                break;
        }
    }

    /**
     * Handle Stripe payment succeeded event.
     */
    protected function handleStripePaymentSucceeded(array $payload): void
    {
        $paymentIntentId = $payload['id'] ?? null;

        if (!$paymentIntentId) {
            Log::warning('Stripe payment succeeded but no payment intent ID found', ['payload' => $payload]);
            return;
        }

        $payment = $this->paymentService->getPaymentByTransactionId($paymentIntentId);

        if (!$payment) {
            Log::warning('Stripe payment succeeded but no matching payment found', ['payment_intent_id' => $paymentIntentId]);
            return;
        }

        $this->paymentService->updatePaymentStatus($payment, 'completed', [
            'stripe_event' => 'payment_intent.succeeded',
            'stripe_payload' => $payload,
        ]);
    }

    /**
     * Handle Stripe payment failed event.
     */
    protected function handleStripePaymentFailed(array $payload): void
    {
        $paymentIntentId = $payload['id'] ?? null;

        if (!$paymentIntentId) {
            Log::warning('Stripe payment failed but no payment intent ID found', ['payload' => $payload]);
            return;
        }

        $payment = $this->paymentService->getPaymentByTransactionId($paymentIntentId);

        if (!$payment) {
            Log::warning('Stripe payment failed but no matching payment found', ['payment_intent_id' => $paymentIntentId]);
            return;
        }

        $errorMessage = $payload['last_payment_error']['message'] ?? 'Payment failed';

        $this->paymentService->updatePaymentStatus($payment, 'failed', [
            'stripe_event' => 'payment_intent.payment_failed',
            'stripe_payload' => $payload,
            'error' => $errorMessage,
        ]);

        // Log the failure for further investigation
        Log::error('Stripe payment failed', [
            'payment_id' => $payment->id,
            'order_id' => $payment->order_id,
            'error' => $errorMessage,
            'payment_intent_id' => $paymentIntentId,
        ]);
    }

    /**
     * Handle Stripe refund event.
     */
    protected function handleStripeRefund(array $payload): void
    {
        $chargeId = $payload['id'] ?? null;

        if (!$chargeId) {
            Log::warning('Stripe refund event received but no charge ID found', ['payload' => $payload]);
            return;
        }

        $payment = $this->paymentService->getPaymentByTransactionId($chargeId);

        if (!$payment) {
            Log::warning('Stripe refund event received but no matching payment found', ['charge_id' => $chargeId]);
            return;
        }

        $refundAmount = ($payload['amount_refunded'] ?? 0) / 100; // Convert from cents

        if ($refundAmount > 0) {
            $this->paymentService->processRefund($payment, $refundAmount, 'Refund via Stripe webhook');

            Log::info('Processed Stripe refund', [
                'payment_id' => $payment->id,
                'order_id' => $payment->order_id,
                'refund_amount' => $refundAmount,
                'charge_id' => $chargeId,
            ]);
        }
    }

    /**
     * Handle PayPal payment completed event.
     */
    protected function handlePayPalPaymentCompleted(array $payload): void
    {
        $resourceId = $payload['resource']['id'] ?? null;

        if (!$resourceId) {
            Log::warning('PayPal payment completed but no resource ID found', ['payload' => $payload]);
            return;
        }

        $payment = $this->paymentService->getPaymentByTransactionId($resourceId);

        if (!$payment) {
            Log::warning('PayPal payment completed but no matching payment found', ['resource_id' => $resourceId]);
            return;
        }

        $this->paymentService->updatePaymentStatus($payment, 'completed', [
            'paypal_event' => 'PAYMENT.CAPTURE.COMPLETED',
            'paypal_payload' => $payload,
        ]);

        Log::info('Processed PayPal payment completion', [
            'payment_id' => $payment->id,
            'order_id' => $payment->order_id,
            'resource_id' => $resourceId,
        ]);
    }

    /**
     * Handle PayPal payment denied event.
     */
    protected function handlePayPalPaymentDenied(array $payload): void
    {
        $resourceId = $payload['resource']['id'] ?? null;

        if (!$resourceId) {
            Log::warning('PayPal payment denied but no resource ID found', ['payload' => $payload]);
            return;
        }

        $payment = $this->paymentService->getPaymentByTransactionId($resourceId);

        if (!$payment) {
            Log::warning('PayPal payment denied but no matching payment found', ['resource_id' => $resourceId]);
            return;
        }

        $errorMessage = $payload['resource']['status_details']['reason'] ?? 'Payment denied';

        $this->paymentService->updatePaymentStatus($payment, 'failed', [
            'paypal_event' => 'PAYMENT.CAPTURE.DENIED',
            'paypal_payload' => $payload,
            'error' => $errorMessage,
        ]);

        Log::error('PayPal payment denied', [
            'payment_id' => $payment->id,
            'order_id' => $payment->order_id,
            'resource_id' => $resourceId,
            'error' => $errorMessage,
        ]);
    }

    /**
     * Handle PayPal refund event.
     */
    protected function handlePayPalRefund(array $payload): void
    {
        $captureId = $payload['resource']['links'][1]['href'] ?? null;

        if (!$captureId) {
            Log::warning('PayPal refund event received but no capture ID found', ['payload' => $payload]);
            return;
        }

        // Extract the capture ID from the URL
        $captureId = basename($captureId);

        $payment = $this->paymentService->getPaymentByTransactionId($captureId);

        if (!$payment) {
            Log::warning('PayPal refund event received but no matching payment found', ['capture_id' => $captureId]);
            return;
        }

        $refundAmount = $payload['resource']['amount']['value'] ?? 0;

        if ($refundAmount > 0) {
            $this->paymentService->processRefund($payment, $refundAmount, 'Refund via PayPal webhook');

            Log::info('Processed PayPal refund', [
                'payment_id' => $payment->id,
                'order_id' => $payment->order_id,
                'refund_amount' => $refundAmount,
                'capture_id' => $captureId,
            ]);
        }
    }

    /**
     * Handle Stripe payment.
     */
    protected function handleStripePayment($order): RedirectResponse
    {
        // Create a Stripe payment intent
        $result = $this->paymentService->createStripePaymentIntent($order);

        if (!$result['success']) {
            throw new \Exception('Failed to create Stripe payment intent: ' . ($result['message'] ?? 'Unknown error'));
        }

        // Store client secret in session for the payment form
        Session::put('stripe_client_secret', $result['client_secret']);

        // Redirect to payment page with order number for better cancellation handling
        return redirect()->route('checkout.payment', [
            'order' => $order->order_number,
            'method' => 'stripe',
        ]);
    }

    /**
     * Handle PayPal payment.
     */
    protected function handlePayPalPayment($order): RedirectResponse
    {
        // Create a PayPal payment
        $result = $this->paymentService->createPayPalPayment(
            $order,
            route('checkout.paypal.callback'),
            route('checkout.paypal.cancel', ['order_id' => $order->id])
        );

        if (!$result['success']) {
            throw new \Exception('Failed to create PayPal payment: ' . ($result['message'] ?? 'Unknown error'));
        }

        // Check for the redirect URL provided by the service
        if (isset($result['redirect_url'])) {
            return redirect()->away($result['redirect_url']);
        }

        // If no redirect URL is found in the service result, throw an error
        throw new \Exception('No redirect URL found in PayPal service result.');
    }

    /**
     * Handle bank transfer payment.
     */
    protected function handleBankTransferPayment($order): RedirectResponse
    {
        // Update order status to awaiting payment
        $this->orderService->updateOrderStatus($order, 'awaiting_payment');

        // Update payment status
        $payment = $order->payments()->latest()->first();
        if ($payment instanceof \App\Models\Payment) {
            $this->paymentService->updatePayment($payment, [
                'status' => 'pending',
                'metadata' => [
                    'payment_instructions' => 'Please transfer the amount to the following bank account...',
                ],
            ]);
        }

        // Redirect to payment page with order number for consistency
        return redirect()->route('payments.show', ['orderNumber' => $order->order_number])
            ->with('success', 'Your order has been placed successfully! Please complete the bank transfer using the provided instructions.');
    }

    /**
     * Get available payment methods.
     */
    protected function getAvailablePaymentMethods(): array
    {
        return [
            'stripe' => [
                'name' => 'Credit Card (Stripe)',
                'description' => 'Pay securely with your credit card via Stripe.',
                'icon' => 'fa-credit-card',
            ],
            'paypal' => [
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account.',
                'icon' => 'fa-paypal',
            ],
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Pay via bank transfer. Your order will be processed after we receive the payment.',
                'icon' => 'fa-university',
            ],
        ];
    }
}
