<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ShippingConfigurationController extends Controller
{
    /**
     * Display the shipping configuration page.
     */
    public function index(): View
    {
        $shippingConfig = Setting::getValue('shipping_configuration', []);
        
        if (is_string($shippingConfig)) {
            $shippingConfig = json_decode($shippingConfig, true) ?: [];
        }

        // Provide default values
        $config = array_merge([
            'fallback_behavior' => 'catch_all',
            'enable_dynamic_calculations' => true,
            'log_shipping_calculations' => true,
            'require_shipping_address' => true,
            'prioritize_specific_zones' => true,
            'default_fallback_methods' => [
                'standard' => [
                    'name' => 'Standard Shipping',
                    'description' => 'Delivery in 3-5 business days',
                    'price' => 5.99,
                    'method_type' => 'flat_rate'
                ],
                'express' => [
                    'name' => 'Express Shipping',
                    'description' => 'Delivery in 1-2 business days',
                    'price' => 12.99,
                    'method_type' => 'flat_rate'
                ]
            ]
        ], $shippingConfig);

        return view('admin.shipping.configuration', [
            'config' => $config,
            'fallbackOptions' => [
                'catch_all' => 'Apply catch-all zone rates with dynamic calculations',
                'block' => 'Block shipping to unconfigured destinations',
                'contact_quote' => 'Show "Contact for quote" message'
            ]
        ]);
    }

    /**
     * Update the shipping configuration.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'fallback_behavior' => 'required|in:catch_all,block,contact_quote',
            'enable_dynamic_calculations' => 'boolean',
            'log_shipping_calculations' => 'boolean',
            'require_shipping_address' => 'boolean',
            'prioritize_specific_zones' => 'boolean',
            'default_fallback_methods' => 'array',
            'default_fallback_methods.*.name' => 'required|string|max:255',
            'default_fallback_methods.*.description' => 'nullable|string|max:500',
            'default_fallback_methods.*.price' => 'required|numeric|min:0',
            'default_fallback_methods.*.method_type' => 'required|in:flat_rate,weight_based,price_based,item_based',
        ]);

        $config = [
            'fallback_behavior' => $request->input('fallback_behavior'),
            'enable_dynamic_calculations' => $request->boolean('enable_dynamic_calculations'),
            'log_shipping_calculations' => $request->boolean('log_shipping_calculations'),
            'require_shipping_address' => $request->boolean('require_shipping_address'),
            'prioritize_specific_zones' => $request->boolean('prioritize_specific_zones'),
            'default_fallback_methods' => $request->input('default_fallback_methods', [])
        ];

        Setting::setValue(
            'shipping_configuration',
            $config,
            'shipping',
            'json',
            [
                'fallback_behavior' => [
                    'catch_all' => 'Apply catch-all zone rates with dynamic calculations',
                    'block' => 'Block shipping to unconfigured destinations',
                    'contact_quote' => 'Show "Contact for quote" message'
                ]
            ],
            false // Not public - admin only
        );

        return redirect()->route('admin.shipping.configuration')
            ->with('success', 'Shipping configuration updated successfully.');
    }

    /**
     * Reset shipping configuration to defaults.
     */
    public function reset(): RedirectResponse
    {
        $defaultConfig = [
            'fallback_behavior' => 'catch_all',
            'enable_dynamic_calculations' => true,
            'log_shipping_calculations' => true,
            'require_shipping_address' => true,
            'prioritize_specific_zones' => true,
            'default_fallback_methods' => [
                'standard' => [
                    'name' => 'Standard Shipping',
                    'description' => 'Delivery in 3-5 business days',
                    'price' => 5.99,
                    'method_type' => 'flat_rate'
                ],
                'express' => [
                    'name' => 'Express Shipping',
                    'description' => 'Delivery in 1-2 business days',
                    'price' => 12.99,
                    'method_type' => 'flat_rate'
                ]
            ]
        ];

        Setting::setValue(
            'shipping_configuration',
            $defaultConfig,
            'shipping',
            'json',
            [
                'fallback_behavior' => [
                    'catch_all' => 'Apply catch-all zone rates with dynamic calculations',
                    'block' => 'Block shipping to unconfigured destinations',
                    'contact_quote' => 'Show "Contact for quote" message'
                ]
            ],
            false
        );

        return redirect()->route('admin.shipping.configuration')
            ->with('success', 'Shipping configuration reset to defaults.');
    }

    /**
     * Test shipping configuration with a sample address.
     */
    public function test(Request $request): RedirectResponse
    {
        $request->validate([
            'test_country' => 'required|string|size:2',
            'test_region' => 'nullable|string|max:100',
            'test_postal_code' => 'nullable|string|max:20',
        ]);

        // This would typically create a test address and run shipping calculations
        // For now, we'll just return a success message
        return redirect()->route('admin.shipping.configuration')
            ->with('info', 'Shipping test completed. Check logs for detailed results.');
    }
}
