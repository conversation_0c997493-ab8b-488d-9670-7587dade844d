<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class ProductController extends Controller
{
    /**
     * Display a listing of the products.
     */
    public function index(Request $request): View
    {
        try {
            $query = Product::with(['category', 'variants'])
                ->where('products.is_active', true);

        // Filter by category if provided and not empty
        if ($request->has('category') && !empty($request->input('category'))) {
            $categoryId = (string) $request->input('category');
            $query->whereRaw("CAST(products.category_id AS TEXT) = ?", [$categoryId]);
        }

        // Filter by search term if provided
        if ($request->has('search') && !empty($request->input('search'))) {
            $search = $request->input('search');

            // For PostgreSQL, we need to use a different approach
            Log::info('Search term', ['search' => $search]);

            // Use a direct SQL approach for PostgreSQL JSON search
            $searchPattern = '%' . $search . '%';

            // Use a query grouping to ensure search doesn't override category filter
            $query->where(function($q) use ($searchPattern) {
                $q->whereRaw("(products.name->>'en')::text ILIKE ?", [$searchPattern])
                  ->orWhereRaw("(products.description->>'en')::text ILIKE ?", [$searchPattern]);
            });

            Log::info('After search query', ['count' => $query->count()]);
        }

        // Sort products
        $sortBy = $request->input('sort', 'newest');

        switch ($sortBy) {
            case 'price_low':
                // Join with variants to sort by minimum price
                $query->join('product_variants', 'products.id', '=', 'product_variants.product_id')
                      ->select('products.*')
                      ->groupBy('products.id')
                      ->orderBy(DB::raw('MIN(product_variants.price)'), 'asc');
                break;
            case 'price_high':
                // Join with variants to sort by maximum price
                $query->join('product_variants', 'products.id', '=', 'product_variants.product_id')
                      ->select('products.*')
                      ->groupBy('products.id')
                      ->orderBy(DB::raw('MAX(product_variants.price)'), 'desc');
                break;
            case 'name':
                // Sort by name using PostgreSQL's JSON operators with explicit casting
                $query->orderByRaw("(products.name->>'en')::text ASC");
                break;
            case 'newest':
            default:
                $query->orderBy('products.created_at', 'desc');
                break;
        }

        // Featured products first (only if not sorting by price)
        if (!in_array($sortBy, ['price_low', 'price_high'])) {
            $query->orderBy('products.is_featured', 'desc');
        }

            $products = $query->paginate(12)->withQueryString();

            // Get all categories for the filter sidebar
            $categories = Category::where('parent_id', null)
                ->with('children')
                ->orderBy('name->en')
                ->get();

            // Show message if no products found
            if ($products->isEmpty()) {
                $message = 'No products found';
                
                if ($request->has('search')) {
                    $message .= ' matching "' . e($request->input('search')) . '"';
                }
                
                if ($request->has('category')) {
                    $category = Category::find($request->input('category'));
                    if ($category) {
                        $message .= ' in category: ' . ($category->name['en'] ?? 'Selected Category');
                    }
                }
                
                $message .= '. Please try different search criteria.';
                
                session()->now('info', $message);
            }

            return view('store.products.index', [
                'products' => $products,
                'categories' => $categories,
                'currentCategory' => $request->input('category'),
                'search' => $request->input('search'),
                'sortBy' => $sortBy,
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading products page', [
                'error' => $e->getMessage(),
                'request' => $request->except(['_token', 'password']),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Return an empty result set in case of error
            return view('store.products.index', [
                'products' => collect([])->paginate(12),
                'categories' => collect([]),
                'search' => $request->input('search'),
                'sortBy' => 'newest',
            ])->with('error', 'We encountered an issue while loading products. Please try again or contact support if the problem persists.');
        }
    }

    /**
     * Display the specified product.
     */
    public function show(string $slug): View
    {
        if (empty($slug)) {
            abort(404, 'The requested product was not found. Please check the URL and try again.');
        }

        try {
            // Find the product by slug in the current locale using PostgreSQL's JSON operators with explicit casting
            $product = Product::whereRaw("(products.slug->>'en')::text = ?", [$slug])
                ->with(['category', 'variants.inventoryItem'])
                ->first();

            if (!$product) {
                // Log the 404 for monitoring
                Log::warning('Product not found', [
                    'slug' => $slug,
                    'ip' => request()->ip(),
                ]);
                
                // Suggest similar products if available
                $similarProducts = Product::where('is_active', true)
                    ->inRandomOrder()
                    ->take(4)
                    ->get();
                    
                return view('errors.404', [
                    'message' => 'We\'re sorry, but the product you\'re looking for cannot be found. It may have been moved or is no longer available.',
                    'similarProducts' => $similarProducts,
                ]);
            }
            
            Log::info('Found product by slug', ['slug' => $slug, 'product_id' => $product->id]);

            // Get related products from the same category
            $relatedProducts = $product->category ? 
                $product->category->products()
                    ->where('id', '!=', $product->id)
                    ->where('is_active', true)
                    ->inRandomOrder()
                    ->take(4)
                    ->get() : collect([]);
                    
            return view('store.products.show', [
                'product' => $product,
                'relatedProducts' => $relatedProducts,
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading product page', [
                'product_id' => $product->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Fallback to a simple error page if something goes wrong
            return view('errors.500', [
                'message' => 'We\'re having trouble loading this product. Our team has been notified and we\'re working on a fix. Please try again later.'
            ]);
        }
    }
}
