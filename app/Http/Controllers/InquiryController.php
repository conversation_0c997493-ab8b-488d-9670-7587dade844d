<?php

namespace App\Http\Controllers;

use App\Models\Inquiry;
use App\Services\InquiryService;
use App\Services\ServiceService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class InquiryController extends Controller
{
    protected InquiryService $inquiryService;
    protected ServiceService $serviceService;

    /**
     * Create a new controller instance.
     */
    public function __construct(InquiryService $inquiryService, ServiceService $serviceService)
    {
        $this->inquiryService = $inquiryService;
        $this->serviceService = $serviceService;
        $this->middleware('auth')->only(['userInquiries']);
    }

    /**
     * Show the form for creating a new inquiry.
     */
    public function create(): View
    {
        $services = $this->serviceService->getActiveServices();

        return view('inquiries.create', [
            'services' => $services
        ]);
    }

    /**
     * Store a newly created inquiry in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'service_id' => 'nullable|exists:services,id',
            'message' => 'required|string|min:10',
            'attachment' => 'nullable|file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png', // 10MB max, specific file types
        ], [
            'name.required' => 'Please provide your name so we know how to address you.',
            'email.required' => 'We need your email address to respond to your inquiry.',
            'email.email' => 'Please provide a valid email address.',
            'message.required' => 'Please provide details about your inquiry.',
            'message.min' => 'Your message should be at least 10 characters long to provide enough details.',
            'attachment.max' => 'The attachment size should not exceed 10MB.',
            'attachment.mimes' => 'Only PDF, Word documents, and images are accepted as attachments.',
        ]);

        // Add user_id if authenticated
        if (Auth::check()) {
            $validated['user_id'] = Auth::id();
        }

        try {
            // Create the inquiry
            $inquiry = $this->inquiryService->createInquiry($validated);

            // Handle file upload if present
            if ($request->hasFile('attachment')) {
                try {
                    $inquiry->addMedia($request->file('attachment'))
                        ->toMediaCollection('attachments');
                } catch (\Exception $e) {
                    Log::error('Failed to upload attachment for inquiry', [
                        'inquiry_id' => $inquiry->id,
                        'error' => $e->getMessage(),
                    ]);
                    // Continue without failing the entire request if file upload fails
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to create inquiry', [
                'email' => $validated['email'],
                'error' => $e->getMessage(),
            ]);
            
            return redirect()->back()
                ->withInput()
                ->with('error', 'We encountered an issue while processing your inquiry. Please try again or contact us directly if the problem persists.');
        }

        // Queue confirmation email
        // In a real implementation, you would dispatch a job to send an email
        // Mail::to($inquiry->email)->queue(new InquiryConfirmation($inquiry));

        return redirect()->route('inquiries.thankyou')
            ->with('success', 'Thank you for your inquiry! We\'ve received your message and our team will get back to you within 1-2 business days. We appreciate your patience.');
    }

    /**
     * Display the thank you page.
     */
    public function thankyou(): View
    {
        return view('inquiries.thankyou');
    }

    /**
     * Display a listing of the user's inquiries.
     */
    public function userInquiries(): View
    {
        $user = Auth::user();
        
        try {
            $inquiries = Inquiry::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->paginate(10);
                
            if ($inquiries->isEmpty()) {
                session()->now('info', 'You haven\'t submitted any inquiries yet. Feel free to contact us with any questions!');
            }
        } catch (\Exception $e) {
            Log::error('Failed to fetch user inquiries', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            
            // Return empty collection in case of error
            $inquiries = collect([])->paginate(10);
            session()->now('error', 'We encountered an issue while loading your inquiries. Please refresh the page or try again later.');
        }

        return view('inquiries.user-inquiries', [
            'inquiries' => $inquiries,
        ]);
    }
}
