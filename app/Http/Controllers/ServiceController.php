<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Services\ServiceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class ServiceController extends Controller
{
    protected ServiceService $serviceService;

    /**
     * Create a new controller instance.
     */
    public function __construct(ServiceService $serviceService)
    {
        $this->serviceService = $serviceService;
    }

    /**
     * Display a listing of the services.
     */
    public function index(): View
    {
        try {
            $services = $this->serviceService->getActiveServices();
            
            if ($services->isEmpty()) {
                session()->now('info', 'We currently don\'t have any services available. Please check back later or contact us for more information.');
            }

            return view('services.index', [
                'services' => $services
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading services page', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Return an empty collection in case of error
            return view('services.index', [
                'services' => collect([])
            ])->with('error', 'We\'re having trouble loading our services. Please try again later or contact support if the problem persists.');
        }
    }

    /**
     * Display the specified service.
     */
    public function show(string $slug): View
    {
        if (empty($slug)) {
            abort(404, 'The requested service page was not found. Please check the URL and try again.');
        }
        
        $locale = app()->getLocale();
        
        try {
            // Find the service by slug in the current locale
            $service = $this->serviceService->findBySlug($slug, $locale);

            if (!$service) {
                // Log the 404 for monitoring
                Log::warning('Service not found', [
                    'slug' => $slug,
                    'locale' => $locale,
                    'ip' => request()->ip(),
                ]);
                
                // Suggest other services
                $otherServices = $this->serviceService->getActiveServices()
                    ->where('slug', '!=', $slug)
                    ->take(4);
                    
                return view('errors.404', [
                    'message' => 'The service you\'re looking for could not be found. It may have been moved or is no longer available.',
                    'similarServices' => $otherServices,
                ]);
            }
            
            // Log the view for analytics
            Log::info('Service viewed', [
                'service_id' => $service->id,
                'slug' => $slug,
                'locale' => $locale,
            ]);

            return view('services.show', [
                'service' => $service,
                'otherServices' => $this->serviceService->getActiveServices()
                    ->where('id', '!=', $service->id)
                    ->take(3)
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error loading service page', [
                'slug' => $slug,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Fallback to a simple error page if something goes wrong
            return view('errors.500', [
                'message' => 'We\'re having trouble loading this service. Our team has been notified and we\'re working on a fix. Please try again later.'
            ]);
        }
    }
}
