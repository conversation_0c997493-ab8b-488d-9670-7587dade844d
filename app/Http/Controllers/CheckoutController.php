<?php

namespace App\Http\Controllers;

use App\Models\Address;
use App\Models\Cart;
use App\Models\Order;
use App\Services\CartService;
use App\Services\InventoryService;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\ShippingService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;
use App\Http\Requests\ProcessCheckoutRequest;
use Symfony\Component\Intl\Countries;
use App\Exceptions\InventoryUnavailableException;
use App\Repositories\CheckoutRepository;
use Illuminate\Validation\ValidationException;

class CheckoutController extends Controller
{
    protected CartService $cartService;
    protected OrderService $orderService;
    protected PaymentService $paymentService;
    protected InventoryService $inventoryService;
    protected ShippingService $shippingService;
    protected CheckoutRepository $checkoutRepository;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        CartService $cartService,
        OrderService $orderService,
        PaymentService $paymentService,
        InventoryService $inventoryService,
        ShippingService $shippingService,
        \App\Repositories\CheckoutRepository $checkoutRepository
    ) {
        $this->cartService = $cartService;
        $this->orderService = $orderService;
        $this->paymentService = $paymentService;
        $this->inventoryService = $inventoryService;
        $this->shippingService = $shippingService;
        $this->checkoutRepository = $checkoutRepository;
    }

    /**
     * Show the checkout page.
     */
    public function index(Request $request): View|RedirectResponse
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please log in or register to proceed with checkout.');
        }

        $cart = $this->getCurrentCart();

        // Debug cart data
        \Log::info('Checkout cart data:', [
            'cart_id' => $cart->id,
            'items_count' => $cart->items->count(),
            'has_items' => !$cart->items->isEmpty(),
            'total' => $cart->subtotal
        ]);

        // Check if cart is empty
        if ($cart->items->isEmpty()) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty. Please add items before checkout.');
        }

        // Verify inventory availability before proceeding to checkout
        $inventoryCheck = $this->verifyInventoryAvailability($cart);
        if (!$inventoryCheck['success']) {
            return redirect()->route('cart.index')
                ->with('error', $inventoryCheck['message']);
        }

        // Generate a unique checkout ID for this session
        if (!Session::has('checkout_id')) {
            Session::put('checkout_id', uniqid('checkout_', true));
        }

        // Get user addresses if logged in
        $displayAddresses = collect(); // Initialize as an empty Laravel Collection
        if ($request->user()) {
            // $request->user()->addresses is a Collection of UserAddress models
            $userAddressPivots = $request->user()->addresses()->with('address')->get();
            $displayAddresses = $userAddressPivots->map(function ($userAddressPivot) {
                if ($userAddressPivot->address) { // Ensure the related Address model exists
                    $addressModel = $userAddressPivot->address;
                    // Add default flags to the Address model instance for the view
                    $addressModel->is_default_billing = $userAddressPivot->is_default_billing;
                    $addressModel->is_default_shipping = $userAddressPivot->is_default_shipping;
                    // The name for saved addresses will be the authenticated user's name,
                    // which is accessible in Blade via Auth::user()->first_name etc.
                    // No need to add display_first_name here if we use Auth::user() in Blade for saved addresses.
                    return $addressModel;
                }
                return null;
            })->filter(); // Remove any nulls if an address was missing
        }


        // Get available payment methods from PaymentService
        $paymentMethods = $this->paymentService->getAvailablePaymentMethods();

        // Get shipping methods from OrderService
        $shippingMethods = $this->orderService->getShippingMethods($cart);

        // Get countries list using Symfony's Intl component
        $countries = Countries::getNames(app()->getLocale());
        asort($countries); // Sort countries alphabetically

        return view('checkout.index', [
            'cart' => $cart,
            'addresses' => $displayAddresses,
            'paymentMethods' => $paymentMethods,
            'shippingMethods' => $shippingMethods,
            'checkoutId' => Session::get('checkout_id'),
            'countries' => $countries,
        ]);
    }

    /**
     * Process the checkout.
     */
    public function process(ProcessCheckoutRequest $request): RedirectResponse
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please log in or register to complete your order.');
        }

        $validated = $request->validated();

        // Verify checkout ID to prevent duplicate submissions
        $sessionCheckoutId = Session::get('checkout_id');
        if (empty($sessionCheckoutId) || $sessionCheckoutId !== $validated['checkout_id']) {
            // If session checkout ID is missing, generate a new one
            if (empty($sessionCheckoutId)) {
                Session::put('checkout_id', uniqid('checkout_', true));
                Log::warning('Missing checkout ID in session, generated new one', [
                    'session_id' => Session::getId(),
                    'new_checkout_id' => Session::get('checkout_id'),
                ]);
            } else {
                Log::warning('Invalid checkout ID', [
                    'session_id' => Session::getId(),
                    'expected' => $sessionCheckoutId,
                    'received' => $validated['checkout_id'],
                ]);
            }

            return redirect()->route('checkout.index')
                ->with('error', 'Your checkout session has expired. For security reasons, please review your order details and try again.');
        }

        // Get the cart
        $cart = $this->getCurrentCart();

        // Check if cart is empty
        if ($cart->items->isEmpty()) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty. Please add items before checkout.');
        }

        // Verify inventory availability before processing
        $inventoryCheck = $this->verifyInventoryAvailability($cart);
        if (!$inventoryCheck['success']) {
            return redirect()->route('cart.index')
                ->with('error', $inventoryCheck['message']);
        }

        try {
            // Begin database transaction
            return DB::transaction(function () use ($request, $validated, $cart) {

                // --- 1. Determine Billing Address ---
                $billingAddress = null;
                if (!empty($validated['billing_address_id'])) {
                    // Use existing address
                    $billingAddress = Address::whereHas('userAddresses', function ($query) use ($request, $validated) {
                        $query->where('user_id', $request->user()?->id)
                              ->where('address_id', $validated['billing_address_id']);
                    })->find($validated['billing_address_id']);

                    if (!$billingAddress) {
                         // Handle error: User tried to use an address not belonging to them
                         throw new \Exception("The selected billing address is not associated with your account. Please choose another address or add a new one.");
                    }

                } elseif (!empty($validated['billing_address'])) {
                    // Create new address
                    $billingAddressData = $validated['billing_address'];

                    // Extract email and phone from billing address data (not part of Address model)
                    $billingEmail = $billingAddressData['email'] ?? null;
                    $billingPhone = $billingAddressData['phone'] ?? null;

                    // Remove email and phone from address data before creating Address
                    unset($billingAddressData['email'], $billingAddressData['phone']);

                    // Add user_id if user is logged in and you want to save the address
                    if ($request->user() && $request->input('save_billing_address')) { // Assuming a 'save address' checkbox
                         // You might want UserAddressService here to handle linking
                         $billingAddress = Address::create($billingAddressData);
                         $request->user()->addresses()->create(['address_id' => $billingAddress->id]); // Simple link
                    } else {
                         // Create address without linking to user (guest or didn't save)
                         $billingAddress = Address::create($billingAddressData);
                    }

                    // Store email and phone in session for later use
                    if ($billingEmail) {
                        Session::put('checkout_billing_email', $billingEmail);
                    }

                    if ($billingPhone) {
                        Session::put('checkout_billing_phone', $billingPhone);
                    }
                }
                 if (!$billingAddress) {
                     throw new \Exception("We couldn't determine your billing address. Please ensure all required billing information is provided and try again.");
                 }


                // --- 2. Determine Shipping Address ---
                $shippingAddress = null;
                if (!empty($validated['use_billing_for_shipping'])) {
                    $shippingAddress = $billingAddress;
                } elseif (!empty($validated['shipping_address_id'])) {
                     // Use existing address
                     $shippingAddress = Address::whereHas('userAddresses', function ($query) use ($request, $validated) {
                         $query->where('user_id', $request->user()?->id)
                               ->where('address_id', $validated['shipping_address_id']);
                     })->find($validated['shipping_address_id']);

                     if (!$shippingAddress) {
                          // Handle error: User tried to use an address not belonging to them
                          throw new \Exception("The selected shipping address is not associated with your account. Please choose another address or add a new one.");
                     }

                } elseif (!empty($validated['shipping_address'])) {
                     // Create new address
                     $shippingAddressData = $validated['shipping_address'];

                     // Extract email and phone from shipping address data (not part of Address model)
                     $shippingEmail = $shippingAddressData['email'] ?? null;
                     $shippingPhone = $shippingAddressData['phone'] ?? null;

                     // Remove email and phone from address data before creating Address
                     unset($shippingAddressData['email'], $shippingAddressData['phone']);

                     if ($request->user() && $request->input('save_shipping_address')) { // Assuming a 'save address' checkbox
                          $shippingAddress = Address::create($shippingAddressData);
                          $request->user()->addresses()->create(['address_id' => $shippingAddress->id]); // Simple link
                     } else {
                          $shippingAddress = Address::create($shippingAddressData);
                     }

                     // Store email and phone in session for later use
                     if ($shippingEmail) {
                         Session::put('checkout_shipping_email', $shippingEmail);
                     }

                     if ($shippingPhone) {
                         Session::put('checkout_shipping_phone', $shippingPhone);
                     }
                }
                 if (!$shippingAddress) {
                     throw new \Exception("We couldn't determine your shipping address. Please ensure all required shipping information is provided or select 'Use billing address' if applicable.");
                 }


                // --- 3. Calculate Totals & Tax ---
                $subtotal = $cart->subtotal;
                $shippingCost = $this->calculateShippingCost($validated['shipping_method'], $cart, $shippingAddress);

                // Validate shipping method and cost
                if (!$this->validateShipping($validated['shipping_method'], $shippingCost, $cart, $shippingAddress)) {
                    Log::warning('Invalid shipping method or cost at checkout', [
                        'method' => $validated['shipping_method'],
                        'cost' => $shippingCost,
                        'cart_id' => $cart->id,
                        'shipping_address' => $shippingAddress->toArray(),
                    ]);
                    throw new \Exception('The selected shipping method is no longer available or the shipping cost has changed. Please review your shipping options and try again.');
                }

                // --- Add Discount Logic Here ---
                $discountAmount = 0; // Placeholder: Implement actual discount calculation based on cart/coupons/etc.
                // Example: $discountAmount = $this->discountService->calculateDiscount($cart, $validated['coupon_code'] ?? null);
                // You would need to inject a DiscountService if you create one.
                // Ensure discountAmount does not exceed subtotal + shippingCost

                // Prepare address data specifically for tax calculation
                $shippingAddressDataForTax = [
                    'country' => $shippingAddress->country,
                    'state' => $shippingAddress->region, // Map Address.region to Tax.state
                    'city' => $shippingAddress->city,
                    'postal_code' => $shippingAddress->postal_code, // Map Address.postal_code to Tax.zip if needed by rules
                ];

                // Calculate tax using the CheckoutRepository
                $taxAmount = $this->checkoutRepository->calculateTax(
                    $shippingAddressDataForTax,
                    $shippingCost,
                    $subtotal - $discountAmount // Calculate tax on subtotal after discount
                );

                // Prepare tax details for storage (optional, adjust as needed)
                $taxDetails = null;
                // We need to call getTaxClass again, or modify calculateTax to return the class used
                $taxClassUsed = $this->checkoutRepository->getTaxClass($shippingAddressDataForTax);
                if ($taxClassUsed) {
                    $taxDetails = [
                        'tax_class_id' => $taxClassUsed->id,
                        'tax_class_name' => $taxClassUsed->name,
                        'rate_percentage' => $taxClassUsed->rate,
                        'applied_to_shipping' => $taxClassUsed->on_shipping && $shippingCost > 0,
                        'matched_location' => [ // Optional: Log how the match was made
                             'country' => $taxClassUsed->country,
                             'state' => $taxClassUsed->state,
                             'is_global' => $taxClassUsed->is_global,
                        ]
                    ];
                }

                $total = $subtotal + $shippingCost + $taxAmount - $discountAmount;

                // --- 4. Create Order ---
                // Gather customer details (prioritize logged-in user, fallback to billing address or session)
                $customerEmail = $request->user()?->email ??
                                Session::get('checkout_billing_email') ??
                                '<EMAIL>'; // Ensure email is present

                // Get customer name from billing address first_name and last_name if available
                $firstName = $validated['billing_address']['first_name'] ?? '';
                $lastName = $validated['billing_address']['last_name'] ?? '';
                $customerName = $request->user()?->name ??
                               ((!empty($firstName) || !empty($lastName)) ? trim($firstName . ' ' . $lastName) : 'Guest');

                $customerPhone = $request->user()?->phone_number ??
                                Session::get('checkout_billing_phone') ??
                                null;


                $order = $this->orderService->createOrderFromCart(
                    $cart,
                    [
                        'user_id' => $request->user()?->id,
                        'customer_email' => $customerEmail,
                        'customer_name' => $customerName,
                        'customer_phone' => $customerPhone,
                        'subtotal' => $subtotal,
                        'shipping_cost' => $shippingCost,
                        'tax_amount' => $taxAmount,
                        'tax_details' => $taxDetails, // Store the details
                        'discount_amount' => $discountAmount, // Add discount if calculated
                        'total' => $total,
                        'currency' => 'USD', // Get from settings or cart
                        'shipping_method' => $validated['shipping_method'],
                        'ip_address' => $request->ip(),
                        'user_agent' => $request->userAgent(),
                        'notes' => $request->input('order_notes'), // Example: if you have an order notes field
                    ],
                    $billingAddress,
                    $shippingAddress, // Pass the determined shipping address
                    $validated['payment_method']
                );

                if (!$order) {
                     throw new \Exception("We couldn't create your order due to a system error. Please try again in a few minutes. If the problem persists, please contact our support team.");
                }

                // --- 5. Post-Order Creation Steps ---
                // Generate a new checkout ID for next checkout attempt
                Session::put('checkout_id', uniqid('checkout_', true));

                // Store order ID in session for confirmation page
                Session::put('last_order_id', $order->id);

                // --- 6. Process Payment ---
                switch ($validated['payment_method']) {
                    case 'stripe':
                        // Redirect to Stripe payment page or handle intent confirmation
                        return $this->handleStripePayment($order);

                    case 'paypal':
                        // Redirect to PayPal
                        return $this->handlePayPalPayment($order);

                    case 'bank_transfer':
                        // Show bank transfer instructions/confirmation
                        return $this->handleBankTransferPayment($order);

                    default:
                        // This shouldn't happen due to validation, but good to have
                        Log::error("Invalid payment method selected during checkout process.", ['method' => $validated['payment_method']]);
                        throw new \Exception('The selected payment method is not available. Please choose a different payment option.');
                }
            }); // End DB::transaction

        } catch (ValidationException $e) {
            // Handle validation errors specifically
            Log::error('Checkout validation failed', [
                'errors' => $e->errors(),
                'user_id' => $request->user()?->id,
                'cart_id' => $cart->id ?? null,
                'session_id' => Session::getId(),
            ]);
            return redirect()->route('checkout.index')
                ->withInput()
                ->withErrors($e->errors())
                ->with('error', 'Please correct the errors below and try again.');

        } catch (InventoryUnavailableException $e) {
             // Handle specific inventory exceptions
             Log::error('Checkout inventory unavailable', [
                 'error' => $e->getMessage(),
                 'user_id' => $request->user()?->id,
                 'cart_id' => $cart->id ?? null,
                 'session_id' => Session::getId(),
             ]);
             return redirect()->route('cart.index') // Redirect back to cart for inventory issues
                 ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            // Handle other unexpected errors
            Log::error('Checkout process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id,
                'cart_id' => $cart->id ?? null, // Cart might not be available if error happened early
                'session_id' => Session::getId(),
            ]);

            // Redirect back with a generic error message
            return redirect()->route('checkout.index')
                ->withInput() // Keep form data
                ->with('error', 'We encountered an issue while processing your order. Please review your information and try again. If the problem continues, please contact our support team for assistance.');
        }
    }

    /**
     * Show the order confirmation page.
     */
    public function confirmation(): View|RedirectResponse
    {
        // Get the order ID from session
        $orderId = Session::get('last_order_id');

        if (!$orderId) {
            return redirect()->route('home')
                ->with('error', 'No order found. Please try again.');
        }

        // Get the order and eager load relationships
        $order = Order::with(['items', 'shippingAddress', 'billingAddress'])->find($orderId);

        if (!$order) {
            return redirect()->route('home')
                ->with('error', 'Order not found. Please contact support.');
        }

        // Clear the order ID from session
        Session::forget('last_order_id');

        return view('checkout.confirmation', [
            'order' => $order,
        ]);
    }

    /**
     * Handle payment callback from PayPal.
     */
    public function paypalCallback(Request $request): RedirectResponse
    {
        $paymentId = $request->input('paymentId');
        $payerId = $request->input('PayerID');
        $token = $request->input('token');

        // Basic validation of required parameters (PayPal returns token and PayerID)
        if (!$token || !$payerId) {
            Log::error('Invalid PayPal callback parameters', [
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
                'token' => $token,
            ]);

            return redirect()->route('checkout.index')
                ->with('error', 'Payment processing failed. Missing required parameters.');
        }

        try {
            // Execute the PayPal payment using the token (which is the PayPal Order ID)
            $result = $this->paymentService->executePayPalPayment($token, $payerId);

            if ($result['success']) {
                // Get the order from the payment
                $payment = $result['payment'];
                $order = $payment->order;

                if ($order) {
                    // Update order status
                    $this->orderService->updateOrderStatus($order, 'processing');

                    // Store order ID in session for confirmation page
                    Session::put('last_order_id', $order->id);

                    return redirect()->route('checkout.confirmation')
                        ->with('success', 'Your order has been placed successfully!');
                }
            }

            Log::error('PayPal payment execution failed', [
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
                'result' => $result,
            ]);

            return redirect()->route('checkout.index')
                ->with('error', 'We couldn\'t process your payment. Please verify your payment details and try again. If the issue persists, try a different payment method or contact your bank for assistance.');
        } catch (\Exception $e) {
            Log::error('PayPal callback processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
            ]);

            return redirect()->route('checkout.index')
                ->with('error', 'We encountered an issue while processing your payment. Please try again in a few minutes. If the problem continues, please contact our support team with your order details.');
        }
    }

    /**
     * Handle PayPal payment cancellation.
     */
    public function paypalCancel(Request $request): RedirectResponse
    {
        $token = $request->input('token');
        $orderId = $request->input('order_id');

        Log::info('PayPal payment cancelled by user', [
            'token' => $token,
            'order_id' => $orderId,
            'all_params' => $request->all()
        ]);

        if ($orderId) {
            try {
                // Try to find the order by UUID first
                $order = \App\Models\Order::find($orderId);
                
                if ($order) {
                    // Use the order's order_number for the route
                    return redirect()->route('payments.show', ['orderNumber' => $order->order_number])
                        ->with('warning', 'Payment was cancelled. Please select another payment method or try again.');
                }
                
                // If order not found by ID, try to find by order_number
                $order = \App\Models\Order::where('order_number', $orderId)->first();
                
                if ($order) {
                    return redirect()->route('payments.show', ['orderNumber' => $order->order_number])
                        ->with('warning', 'Payment was cancelled. Please select another payment method or try again.');
                }
                
                // If we still can't find the order, log it
                Log::error('Order not found for PayPal cancellation', [
                    'order_id' => $orderId,
                    'all_params' => $request->all()
                ]);
                
            } catch (\Exception $e) {
                Log::error('Error in PayPal cancellation handler', [
                    'order_id' => $orderId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        } else {
            Log::error('No order_id provided in PayPal cancellation', [
                'all_params' => $request->all()
            ]);
        }

        // Fallback to checkout page if order ID is not available or order not found
        return redirect()->route('checkout.index')
            ->with('warning', 'Payment was cancelled. Please try again when you\'re ready.');
    }

    /**
     * Handle Stripe payment.
     */
    protected function handleStripePayment(Order $order): RedirectResponse
    {
        // Create a Stripe payment intent
        $result = $this->paymentService->createStripePaymentIntent($order);

        if (!$result['success']) {
            throw new \Exception('Failed to create Stripe payment intent: ' . $result['message']);
        }

        // Store client secret in session for the payment form
        Session::put('stripe_client_secret', $result['client_secret']);

        // Redirect to payment page with order number for better cancellation handling
        return redirect()->route('checkout.payment', [
            'order' => $order->order_number, // Use order_number instead of id
            'method' => 'stripe',
        ]);
    }
    
    /**
     * Handle Stripe payment cancellation.
     */
    public function stripeCancel(Request $request): RedirectResponse
    {
        $orderId = $request->input('order_id');
        $paymentIntentId = $request->input('payment_intent');

        Log::info('Stripe payment cancelled by user', [
            'order_id' => $orderId,
            'payment_intent' => $paymentIntentId,
            'all_params' => $request->all()
        ]);

        if ($orderId) {
            try {
                // Try to find the order by UUID first
                $order = \App\Models\Order::find($orderId);
                
                if ($order) {
                    return redirect()->route('payments.show', ['orderNumber' => $order->order_number])
                        ->with('warning', 'Payment was cancelled. Please select another payment method or try again.');
                }
                
                // If order not found by ID, try to find by order_number
                $order = \App\Models\Order::where('order_number', $orderId)->first();
                
                if ($order) {
                    return redirect()->route('payments.show', ['orderNumber' => $order->order_number])
                        ->with('warning', 'Payment was cancelled. Please select another payment method or try again.');
                }
                
                // If we still can't find the order, log it
                Log::error('Order not found for Stripe cancellation', [
                    'order_id' => $orderId,
                    'payment_intent' => $paymentIntentId,
                    'all_params' => $request->all()
                ]);
                
            } catch (\Exception $e) {
                Log::error('Error in Stripe cancellation handler', [
                    'order_id' => $orderId,
                    'payment_intent' => $paymentIntentId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        } else {
            Log::error('No order_id provided in Stripe cancellation', [
                'payment_intent' => $paymentIntentId,
                'all_params' => $request->all()
            ]);
        }

        // Fallback to checkout page if order ID is not available or order not found
        return redirect()->route('checkout.index')
            ->with('warning', 'Payment was cancelled. Please try again when you\'re ready.');
    }

    /**
     * Handle PayPal payment.
     */
    protected function handlePayPalPayment(Order $order): RedirectResponse
    {
        // Create a PayPal payment
        $result = $this->paymentService->createPayPalPayment(
            $order,
            route('checkout.paypal.callback'),
            route('checkout.paypal.cancel', ['order_id' => $order->id])
        );

        // Log the PayPal service result for debugging
        Log::info('PayPal create payment service result:', ['order_id' => $order->id, 'result' => $result]);

        if (!$result['success']) {
            throw new \Exception('Failed to create PayPal payment: ' . ($result['message'] ?? 'Unknown error'));
        }

        // Check for the redirect URL provided by the service
        if (isset($result['redirect_url'])) {
            return redirect()->away($result['redirect_url']);
        }

        // If no redirect URL is found in the service result, throw an error
        throw new \Exception('No redirect URL found in PayPal service result.');
    }

    /**
     * Handle bank transfer payment.
     */
    protected function handleBankTransferPayment(Order $order): RedirectResponse
    {
        // Update order status to awaiting payment
        $this->orderService->updateOrderStatus($order, 'awaiting_payment');

        // Update payment status
        $payment = $order->payments()->latest()->first();
        if ($payment instanceof \App\Models\Payment) {
            $this->paymentService->updatePayment($payment, [
                'status' => 'pending',
                'metadata' => [
                    'payment_instructions' => 'Please transfer the amount to the following bank account...',
                ],
            ]);
        }

        // Redirect to payment page with order number for consistency
        return redirect()->route('payments.show', ['orderNumber' => $order->order_number])
            ->with('success', 'Your order has been placed successfully! Please complete the bank transfer using the provided instructions.');
    }

    /**
     * Show the payment page.
     */
    public function showPayment(Request $request, Order $order, string $method): View|RedirectResponse
    {
        // Verify that the order belongs to the current user or is a guest order
        if ($request->user() && $order->user_id !== $request->user()->id) {
            abort(403, 'Unauthorized');
        }

        // Verify that the order is in the correct status
        if (!in_array($order->status, ['pending', 'awaiting_payment'])) {
            return redirect()->route('orders.show', $order)
                ->with('error', 'This order cannot be paid at this time. It may have already been processed or canceled. Please check your order status or contact support for assistance.');
        }

        // Verify that the payment method matches
        $payment = $order->payments()->latest()->first();
        if (!$payment || !($payment instanceof \App\Models\Payment) || $payment->payment_method !== $method) {
            return redirect()->route('checkout.index')
                ->with('error', 'The selected payment method is not valid for this order. Please return to checkout and select an available payment option.');
        }

        switch ($method) {
            case 'stripe':
                return $this->showStripePayment($order, $payment);

            case 'bank_transfer':
                return $this->showBankTransferInstructions($order, $payment);

            default:
                return redirect()->route('checkout.index')
                    ->with('error', 'The selected payment method is not supported. Please choose from the available payment options.');
        }
    }

    /**
     * Show Stripe payment form.
     */
    protected function showStripePayment(Order $order, $payment): View
    {
        $clientSecret = Session::get('stripe_client_secret');

        if (!$clientSecret) {
            // If client secret is not in session, create a new payment intent
            $result = $this->paymentService->createStripePaymentIntent($order);

            if (!$result['success']) {
                abort(500, 'Failed to create payment intent');
            }

            $clientSecret = $result['client_secret'];
            Session::put('stripe_client_secret', $clientSecret);
        }

        return view('checkout.payment.stripe', [
            'order' => $order,
            'payment' => $payment,
            'clientSecret' => $clientSecret,
            'publicKey' => config('services.stripe.key'),
        ]);
    }

    /**
     * Show bank transfer instructions.
     */
    protected function showBankTransferInstructions(Order $order, $payment): View
    {
        return view('checkout.payment.bank_transfer', [
            'order' => $order,
            'payment' => $payment,
            'instructions' => $payment->metadata['payment_instructions'] ?? null,
        ]);
    }

    /**
     * Get the current cart for the user or session.
     */
    protected function getCurrentCart(): Cart
    {
        if (Auth::check()) {
            // Get or create cart for authenticated user
            return $this->cartService->getOrCreateCartForUser(Auth::user());
        } else {
            // Get or create cart for guest session
            $sessionId = Session::getId();
            return $this->cartService->getOrCreateCartForSession($sessionId);
        }
    }

    /**
     * Process Stripe payment confirmation.
     */
    public function confirmStripePayment(Request $request, Order $order): RedirectResponse
    {
        $paymentIntentId = $request->input('payment_intent');
        $paymentIntentStatus = $request->input('payment_intent_status');

        if (!$paymentIntentId || $paymentIntentStatus !== 'succeeded') {
            Log::error('Invalid Stripe payment confirmation', [
                'order_id' => $order->id,
                'payment_intent' => $paymentIntentId,
                'status' => $paymentIntentStatus,
            ]);

            return redirect()->route('checkout.payment', [
                'order' => $order->id,
                'method' => 'stripe',
            ])->with('error', 'We couldn\'t verify your payment. This might be due to incorrect payment details or a temporary issue with your payment method. Please try again or contact your bank for assistance.');
        }

        try {
            // Verify the payment with Stripe
            $result = $this->paymentService->verifyStripePayment($paymentIntentId);

            if ($result['success']) {
                // Update order status
                $this->orderService->updateOrderStatus($order, 'processing');

                // Update payment status
                $payment = $order->payments()->latest()->first();
                if ($payment instanceof \App\Models\Payment) {
                    $this->paymentService->updatePayment($payment, [
                        'status' => 'completed',
                        'gateway_transaction_id' => $result['charge_id'] ?? null,
                        'processed_at' => now(),
                        'metadata' => array_merge($payment->metadata ?? [], [
                            'stripe_payment_details' => $result['payment_details'] ?? null,
                        ]),
                    ]);
                }

                // Store order ID in session for confirmation page
                Session::put('last_order_id', $order->id);

                return redirect()->route('checkout.confirmation')
                    ->with('success', 'Your payment has been processed successfully!');
            }

            Log::error('Stripe payment verification failed', [
                'order_id' => $order->id,
                'payment_intent' => $paymentIntentId,
                'result' => $result,
            ]);

            return redirect()->route('checkout.payment', [
                'order' => $order->id,
                'method' => 'stripe',
            ])->with('error', 'Payment verification failed. Please try again or contact support.');
        } catch (\Exception $e) {
            Log::error('Stripe payment confirmation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $order->id,
                'payment_intent' => $paymentIntentId,
            ]);

            return redirect()->route('checkout.payment', [
                'order' => $order->id,
                'method' => 'stripe',
            ])->with('error', 'An error occurred during payment processing. Please try again or contact support.');
        }
    }

    /**
     * Verify inventory availability for all items in the cart.
     */
    protected function verifyInventoryAvailability(Cart $cart): array
    {
        $unavailableItems = [];

        foreach ($cart->items as $item) {
            $variant = $item->productVariant;

            if (!$variant) {
                $unavailableItems[] = 'Unknown product (ID: ' . $item->product_variant_id . ')';
                continue;
            }

            $inventoryItem = $variant->inventoryItem;

            if (!$inventoryItem) {
                $unavailableItems[] = $variant->product->getTranslation('name', app()->getLocale()) . ' - ' . $variant->getTranslation('name', app()->getLocale());
                continue;
            }

            if ($inventoryItem->track_inventory && !$inventoryItem->allow_backorder && $inventoryItem->available_quantity < $item->quantity) {
                $unavailableItems[] = $variant->product->getTranslation('name', app()->getLocale()) . ' - ' . $variant->getTranslation('name', app()->getLocale()) . ' (only ' . $inventoryItem->available_quantity . ' available)';
            }
        }

        if (!empty($unavailableItems)) {
            $message = 'We\'re sorry, but the following items are no longer available in the requested quantity:\n\n' .
                 implode('\n', array_map(function($item) {
                     return "- {$item}";
                 }, $unavailableItems)) .
                 '\n\nPlease adjust your cart and try again.';
            // Throw the specific exception, passing the message and the list of unavailable items
            throw new InventoryUnavailableException($message, $unavailableItems);
        }

        // If all items are available, no exception is thrown and the method implicitly returns true (or could explicitly return true)
        return [
            'success' => true, // Keep the return for consistency, although the catch block handles the 'false' case
        ];
    }

    /**
     * Calculate shipping cost based on the selected shipping method.
     */
    protected function calculateShippingCost(string $shippingMethod, Cart $cart, ?Address $shippingAddress = null): float
    {
        // Get available shipping methods
        $shippingMethods = $this->orderService->getShippingMethods($cart, $shippingAddress);

        if (!isset($shippingMethods[$shippingMethod])) {
            Log::warning('Invalid shipping method selected', [
                'method' => $shippingMethod,
                'available_methods' => array_keys($shippingMethods),
            ]);
            return 0;
        }

        $method = $shippingMethods[$shippingMethod];

        // Check if the method is available
        if (isset($method['available']) && !$method['available']) {
            Log::warning('Unavailable shipping method selected', [
                'method' => $shippingMethod,
                'details' => $method,
            ]);
            return 0;
        }

        return $method['price'];
    }

    /**
     * Validate shipping method and cost at checkout.
     */
    protected function validateShipping(string $shippingMethod, float $shippingCost, Cart $cart, Address $shippingAddress): bool
    {
        return $this->orderService->validateShippingMethodAndCost(
            $shippingMethod,
            $shippingCost,
            $cart,
            $shippingAddress
        );
    }

}
