<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the user's orders.
     */
    public function index(): View
    {
        $user = Auth::user();
        try {
            $orders = Order::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->paginate(10);
        } catch (\Exception $e) {
            Log::error('Failed to fetch orders: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'exception' => $e,
            ]);
            
            // Return an empty result set in case of error
            $orders = collect([])->paginate(10);
            
            // Show a user-friendly message
            session()->flash('error', 'We encountered an issue while loading your orders. Please refresh the page or contact support if the problem persists.');
        }

        return view('orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order): View
    {
        // Check if the order belongs to the authenticated user
        if ($order->user_id !== Auth::id()) {
            abort(403, 'You don\'t have permission to view this order. Please sign in with the correct account.');
        }

        return view('orders.show', [
            'order' => $order,
        ]);
    }
}
