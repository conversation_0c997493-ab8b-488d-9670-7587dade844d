<?php

namespace App\Http\Controllers;

use App\Services\ContentService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Models\Service;
use App\Models\Product;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(/* You might not need ContentService here if <PERSON> calls static helper */)
    {
    }

    /**
     * Show the home page.
     */
    public function index(): View
    {
        $featuredServices = Service::where('is_active', true)
            ->where('is_featured', true) // Assuming an is_featured flag
            ->take(3) // Or your desired number
            ->get();

        $featuredProducts = Product::where('is_active', true)
            ->where('is_featured', true)
            ->take(4) // Or your desired number
            ->with('variants') // Eager load variants for pricing
            ->get();
        
        
        return view('home.index', [
            'featuredServices' => $featuredServices,
            'featuredProducts' => $featuredProducts,
        ]);
    }
}
