<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Cart; // Import the Cart model
use App\Services\PaymentService;

use App\Services\ShippingService;

class ProcessCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(PaymentService $paymentService, ShippingService $shippingService): array
    {
        // Log the raw request data for debugging validation
        \Log::info('ProcessCheckoutRequest raw request data:', $this->all());

        // Dynamically get allowed payment methods
        $allowedPaymentMethods = array_keys($paymentService->getAvailablePaymentMethods());

        // Dynamically get allowed shipping methods
        // For validation purposes, we get all shipping methods without considering
        // the specific cart contents or shipping address
        $allDefinedShippingMethods = array_keys($shippingService->getAvailableShippingMethods(new Cart()));

        return [
            'billing_address_id' => 'nullable|required_without:billing_address',
            'billing_address' => 'required_without:billing_address_id|array', // Expect an array if new
            'billing_address.first_name' => 'required_with:billing_address|string|max:255',
            'billing_address.last_name' => 'required_with:billing_address|string|max:255',
            'billing_address.address_line1' => 'required_with:billing_address|string|max:255',
            'billing_address.address_line2' => 'nullable|string|max:255',
            'billing_address.city' => 'required_with:billing_address|string|max:255',
            'billing_address.region' => 'required_with:billing_address|string|max:255', // Use 'region' matching Address model
            'billing_address.postal_code' => 'required_with:billing_address|string|max:20',
            'billing_address.country' => 'required_with:billing_address|string|size:2', // ISO 3166-1 alpha-2
            'billing_address.email' => 'required_with:billing_address|email|max:255', // Example: if collecting email here
            'billing_address.phone' => 'required_with:billing_address|nullable|string|max:20', // Example: if collecting phone here

            // Add similar rules for shipping_address if it can be different
            'shipping_address_id' => 'nullable|required_without_all:shipping_address,use_billing_for_shipping',
            'shipping_address' => 'required_without_all:shipping_address_id,use_billing_for_shipping|array',
            'shipping_address.first_name' => 'required_with:shipping_address|string|max:255',
            'shipping_address.last_name' => 'required_with:shipping_address|string|max:255',
            'shipping_address.address_line1' => 'required_with:shipping_address|string|max:255',
            'shipping_address.address_line2' => 'nullable|string|max:255',
            'shipping_address.city' => 'required_with:shipping_address|string|max:255',
            'shipping_address.region' => 'required_with:shipping_address|string|max:255', // Use 'region'
            'shipping_address.postal_code' => 'required_with:shipping_address|string|max:20',
            'shipping_address.country' => 'required_with:shipping_address|string|size:2',

            'use_billing_for_shipping' => 'nullable|boolean', // Checkbox for "Shipping same as Billing"

            // Use dynamically fetched allowed payment methods
            'payment_method' => 'required|string|in:' . implode(',', $allowedPaymentMethods),
            // Use dynamically fetched allowed shipping methods (using the less accurate approach for now)
            'shipping_method' => 'required|string|in:' . implode(',', $allDefinedShippingMethods),
            'terms_accepted' => 'required|accepted',
            'checkout_id' => 'required|string', // Verify against session
        ];
    }
}
