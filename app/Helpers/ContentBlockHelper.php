<?php

namespace App\Helpers;

use App\Models\ContentBlock;

class ContentBlockHelper
{
    public static function get($key, $default = '')
    {
        $block = ContentBlock::where('key', $key)->where('is_active', true)->first();
        if ($block) {
            return $block->getTranslation('content', app()->getLocale(), false) ?: $default;
        }
        return $default;
    }

    public static function getTitle($key, $default = '')
    {
         $block = ContentBlock::where('key', $key)->where('is_active', true)->first();
         if ($block) {
             return $block->getTranslation('title', app()->getLocale(), false) ?: $default;
         }
         return $default;
    }
}
