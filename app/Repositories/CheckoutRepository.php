<?php

namespace App\Repositories;

use App\Models\Order;
use App\Models\TaxRate;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class CheckoutRepository
{
    /**
     * Calculate tax for an order.
     *
     * @param array $shippingAddressData
     * @param float $shipping_charge
     * @param float $amount
     * @return float
     */
    public function calculateTax(array $shippingAddressData, float $shipping_charge, float $amount): float
    {
        // Pass shipping address data to getTaxClass
        $tax_class = $this->getTaxClass($shippingAddressData); // <-- Pass the address data

        if ($tax_class) {
            $tax_amount = $this->getTotalTax($amount, $tax_class);

            // Add shipping tax if applicable (assuming shipping is taxed at the same rate)
            if ($shipping_charge > 0) {
                $tax_amount += $this->getTotalTax($shipping_charge, $tax_class);
            }

            // Log which tax class was used (optional, but helpful for debugging)
            Log::debug('Tax calculated using Tax Class', [
                'tax_class_id' => $tax_class->id,
                'tax_class_name' => $tax_class->name,
                'rate' => $tax_class->rate,
                'address_country' => $shippingAddressData['country'] ?? null,
                'address_state' => $shippingAddressData['state'] ?? null, // Use 'state' key
            ]);

            return round($tax_amount, 2); // Ensure rounding
        }

        Log::debug('No applicable tax class found. Tax amount is 0.');
        return 0;
    }

    /**
     * Get the tax class to use for calculation based on address.
     *
     * @param array $shippingAddressData ['country' => string, 'state' => string|null, ...]
     * @return TaxRate|null
     */
    public function getTaxClass(array $shippingAddressData): ?TaxRate
    {
        $country = strtoupper(trim($shippingAddressData['country'] ?? ''));
        $state = $shippingAddressData['state'] ? trim($shippingAddressData['state']) : null;

        Log::debug('Looking up tax class for', [
            'country' => $country,
            'state' => $state
        ]);

        if (empty($country) || $country === 'ZZ') {
            Log::warning('No country specified or using global default, using global default tax class');
            return $this->getGlobalDefaultTaxClass();
        }


        // 1. Try matching Country + State (most specific match)
        if ($state) {
            $taxClass = TaxRate::where('country', $country)
                ->where('region', $state)
                ->where('is_active', true)
                ->orderBy('priority', 'desc')
                ->first();
                
            if ($taxClass) {
                Log::debug("Tax class found matching Country+State: {$taxClass->name} (ID: {$taxClass->id})");
                return $taxClass;
            }
        }

        // 2. Try to find a country-wide default tax rate (no region specified)
        $taxClass = TaxRate::where('country', $country)
            ->whereNull('region')
            ->where('is_active', true)
            ->orderBy('is_default', 'desc') // Prefer default rates if multiple exist
            ->orderBy('priority', 'desc')
            ->first();
            
        if ($taxClass) {
            Log::debug("Tax class found matching Country only: {$taxClass->name} (ID: {$taxClass->id})");
            return $taxClass;
        }

        // 3. If no specific rate found, try to find any active rate for the country
        $taxClass = TaxRate::where('country', $country)
            ->where('is_active', true)
            ->orderBy('priority', 'desc')
            ->first();
            
        if ($taxClass) {
            Log::debug("Using any active tax rate for country: {$taxClass->name} (ID: {$taxClass->id})");
            return $taxClass;
        }

        // 4. Fall back to global default if no country-specific rate is found
        Log::debug('No tax rate found for the specified country, falling back to global default');
        $taxClass = TaxRate::where('country', $country)
            ->where('is_active', true)
            ->orderBy('priority', 'desc')
            ->first();
            
        if ($taxClass) {
            Log::debug("Tax class found for country (any region): {$taxClass->name} (ID: {$taxClass->id})");
            return $taxClass;
        }

        // 4. If still no match, use the global default
        $taxClass = $this->getGlobalDefaultTaxClass();
        if ($taxClass) {
            Log::debug("No specific tax class found for country '{$country}', falling back to global default: {$taxClass->name} (ID: {$taxClass->id})");
        } else {
            Log::warning('No tax class found and no global default available');
        }

        return $taxClass;
    }

    /**
     * Gets the globally configured default tax class from settings.
     *
     * @return \App\Models\TaxRate|null
     */
    protected function getGlobalDefaultTaxClass(): ?\App\Models\TaxRate
    {
        try {
            // First try to get the default tax rate from settings
            $defaultTaxClassId = Setting::getValue('default_tax_class');

            if ($defaultTaxClassId) {
                $taxRate = TaxRate::find($defaultTaxClassId);
                if ($taxRate instanceof \App\Models\TaxRate) {
                    return $taxRate;
                }
            }

            // If no default is set in settings or not found, get the first active default tax rate
            $defaultRate = TaxRate::where('is_default', true)
                ->where('is_active', true)
                ->orderBy('priority', 'desc')
                ->first();

            // If we have a valid default rate, return it
            if ($defaultRate instanceof \App\Models\TaxRate) {
                return $defaultRate;
            }

            // If still no default, get the first active tax rate
            $fallbackRate = TaxRate::where('is_active', true)
                ->orderBy('priority', 'desc')
                ->first();

            return $fallbackRate instanceof \App\Models\TaxRate ? $fallbackRate : null;

        } catch (\Throwable $th) {
            Log::error('Error getting default tax class', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ]);
            return null;
        }
    }


    /**
     * Calculate the total tax amount.
     *
     * @param float $amount
     * @param TaxRate $tax_class
     * @return float
     */
    protected function getTotalTax(float $amount, TaxRate $tax_class): float
    {
        return ($amount * $tax_class->rate) / 100;
    }

}
