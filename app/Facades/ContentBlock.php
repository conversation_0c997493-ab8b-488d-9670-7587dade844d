<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static mixed get(string $key, $default = null)
 * @method static string|null getTitle(string $key, string $default = null)
 *
 * @see \App\Helpers\ContentBlockHelper
 */
class ContentBlock extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'content_block_helper';
    }
}
