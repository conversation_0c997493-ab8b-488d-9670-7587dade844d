<?php

namespace App\Services;

use App\Models\Address;
use App\Models\Cart;
use App\Models\ShippingMethod;
use App\Models\ShippingRate;
use App\Models\ShippingZone;
use App\Services\Shipping\FlatRateCalculator;
use App\Services\Shipping\ItemBasedCalculator;
use App\Services\Shipping\PriceBasedCalculator;
use App\Services\Shipping\ShippingCalculatorInterface;
use App\Services\Shipping\WeightBasedCalculator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class ShippingService extends BaseService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        ShippingMethod $shippingMethod,
        protected ShippingZone $shippingZone,
        protected ShippingRate $shippingRate
    ) {
        $this->model = $shippingMethod;
    }

    /**
     * Get available shipping methods for a cart and shipping address.
     *
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return array
     */
    public function getAvailableShippingMethods(Cart $cart, ?Address $shippingAddress = null): array
    {
        $result = [];

        try {
            // If no shipping address is provided, return a limited set of methods
            if (!$shippingAddress) {
                \Log::debug('No shipping address provided, returning fallback methods');
                return $this->getFallbackShippingMethods($cart);
            }

            // Find applicable shipping zones for the address
            $applicableZones = $this->getApplicableShippingZones($shippingAddress);
            \Log::debug('Applicable zones count: ' . $applicableZones->count());
            \Log::debug('Applicable zones: ' . json_encode($applicableZones->pluck('name')->toArray()));

            if ($applicableZones->isEmpty()) {
                Log::warning('No applicable shipping zones found for address', [
                    'address' => $shippingAddress->toArray(),
                ]);
                return $this->getFallbackShippingMethods($cart);
            }

            // Get all active shipping methods
            $activeMethods = $this->model->where('is_active', true)
                ->orderBy('display_order')
                ->get();

            \Log::debug('Active shipping methods count: ' . $activeMethods->count());
            \Log::debug('Active shipping methods: ' . json_encode($activeMethods->pluck('code')->toArray()));

            // For each method, find the applicable rate in the applicable zones
            foreach ($activeMethods as $method) {
                \Log::debug('Checking method: ' . $method->code . ', subtotal: ' . $cart->subtotal);

                // Skip methods that don't apply to the cart subtotal
                if (!$method->isAvailableForSubtotal($cart->subtotal)) {
                    \Log::debug('Method not available for subtotal: ' . $method->code);
                    continue;
                }

                // Find the applicable rate for this method in the applicable zones
                $rate = $this->findApplicableRate($method, $applicableZones, $cart);
                \Log::debug('Rate found for method ' . $method->code . ': ' . ($rate ? 'yes' : 'no'));

                if ($rate) {
                    // Calculate the shipping cost
                    $cost = $this->calculateShippingCost($rate, $cart, $shippingAddress);
                    \Log::debug('Calculated cost for ' . $method->code . ': ' . $cost);

                    // Add the method to the result
                    $result[$method->code] = [
                        'name' => $method->name,
                        'description' => $method->description,
                        'price' => $cost,
                        'method_type' => $method->method_type,
                        'available' => true,
                    ];
                }
            }

            \Log::debug('Available methods: ' . json_encode(array_keys($result)));

            // If no methods are available, return fallback methods
            if (empty($result)) {
                \Log::debug('No methods available, returning fallback methods');
                return $this->getFallbackShippingMethods($cart, $shippingAddress);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Error getting available shipping methods', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->getFallbackShippingMethods($cart, $shippingAddress);
        }
    }

    /**
     * Get fallback shipping methods when no address is provided or no applicable zones are found.
     *
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return array
     */
    protected function getFallbackShippingMethods(Cart $cart, ?Address $shippingAddress = null): array
    {
        $shippingConfig = $this->getShippingConfiguration();
        $fallbackBehavior = $shippingConfig['fallback_behavior'];

        \Log::debug('Fallback behavior: ' . $fallbackBehavior);

        // Handle different fallback behaviors
        switch ($fallbackBehavior) {
            case 'block':
                \Log::debug('Shipping blocked for unconfigured destination');
                return [
                    'blocked' => [
                        'name' => 'Shipping Not Available',
                        'description' => 'Shipping is not available to this destination',
                        'price' => 0,
                        'method_type' => 'blocked',
                        'available' => false,
                    ]
                ];

            case 'contact_quote':
                \Log::debug('Contact for quote required');
                return [
                    'contact_quote' => [
                        'name' => 'Contact for Quote',
                        'description' => 'Please contact us for shipping rates to this destination',
                        'price' => 0,
                        'method_type' => 'contact_quote',
                        'available' => false,
                    ]
                ];

            case 'catch_all':
            default:
                // Try to use dynamic calculations with catch-all zones if available
                if ($shippingAddress && $shippingConfig['enable_dynamic_calculations']) {
                    $catchAllZones = ShippingZone::getCatchAllZones();
                    if ($catchAllZones->isNotEmpty()) {
                        \Log::debug('Using catch-all zones for dynamic calculations');
                        return $this->calculateMethodsForZones($cart, $shippingAddress, $catchAllZones);
                    }
                }

                // Fall back to configured default methods or hardcoded methods
                return $this->getConfiguredFallbackMethods($shippingConfig);
        }
    }

    /**
     * Calculate shipping methods for specific zones with dynamic calculations.
     *
     * @param Cart $cart
     * @param Address $shippingAddress
     * @param Collection $zones
     * @return array
     */
    protected function calculateMethodsForZones(Cart $cart, Address $shippingAddress, Collection $zones): array
    {
        $methods = [];

        // Get all active shipping methods
        $activeMethods = $this->model->where('is_active', true)
            ->orderBy('display_order')
            ->get();

        \Log::debug('Calculating methods for zones, active methods: ' . $activeMethods->count());

        foreach ($activeMethods as $method) {
            if (!$method->isAvailableForSubtotal($cart->subtotal)) {
                continue;
            }

            // Find applicable rate in the zones
            $rate = $this->findApplicableRate($method, $zones, $cart);

            if ($rate) {
                // Use dynamic calculation
                $cost = $this->calculateShippingCost($rate, $cart, $shippingAddress);
                \Log::debug("Dynamic calculation for {$method->code}: {$cost}");

                $methods[$method->code] = [
                    'name' => $method->name,
                    'description' => $method->description,
                    'price' => $cost,
                    'method_type' => $method->method_type,
                    'available' => true,
                ];
            }
        }

        // If no dynamic methods found, fall back to configured defaults
        if (empty($methods)) {
            \Log::debug('No dynamic methods found, using configured fallback');
            return $this->getConfiguredFallbackMethods($this->getShippingConfiguration());
        }

        return $methods;
    }

    /**
     * Get configured fallback methods.
     *
     * @param array $shippingConfig
     * @return array
     */
    protected function getConfiguredFallbackMethods(array $shippingConfig): array
    {
        $defaultMethods = $shippingConfig['default_fallback_methods'] ?? [];

        if (empty($defaultMethods)) {
            // Ultimate fallback - hardcoded methods
            \Log::debug('Using hardcoded fallback methods');
            return [
                'standard' => [
                    'name' => 'Standard Shipping',
                    'description' => 'Delivery in 3-5 business days',
                    'price' => 5.99,
                    'method_type' => 'flat_rate',
                    'available' => true,
                ],
                'express' => [
                    'name' => 'Express Shipping',
                    'description' => 'Delivery in 1-2 business days',
                    'price' => 12.99,
                    'method_type' => 'flat_rate',
                    'available' => true,
                ],
            ];
        }

        \Log::debug('Using configured fallback methods');

        // Process configured methods and mark them as available
        $methods = [];
        foreach ($defaultMethods as $code => $methodConfig) {
            $methods[$code] = array_merge($methodConfig, ['available' => true]);
        }

        return $methods;
    }

    /**
     * Get applicable shipping zones for an address using zone hierarchy.
     *
     * @param Address $address
     * @return Collection
     */
    protected function getApplicableShippingZones(Address $address): Collection
    {
        $shippingConfig = $this->getShippingConfiguration();

        // First, try to find specific zones (non-catch-all)
        $specificZones = ShippingZone::getSpecificZones();
        $applicableSpecificZones = $specificZones->filter(function ($zone) use ($address) {
            return $zone->appliesToAddress($address);
        });

        \Log::debug('Specific zones found: ' . $applicableSpecificZones->count());

        // If specific zones are found and we prioritize them, return only specific zones
        if ($applicableSpecificZones->isNotEmpty() && $shippingConfig['prioritize_specific_zones']) {
            return $applicableSpecificZones;
        }

        // If no specific zones found, check catch-all zones based on fallback behavior
        if ($applicableSpecificZones->isEmpty()) {
            $fallbackBehavior = $shippingConfig['fallback_behavior'];

            \Log::debug('No specific zones found, fallback behavior: ' . $fallbackBehavior);

            switch ($fallbackBehavior) {
                case 'catch_all':
                    // Return catch-all zones
                    $catchAllZones = ShippingZone::getCatchAllZones();
                    \Log::debug('Catch-all zones found: ' . $catchAllZones->count());
                    return $catchAllZones;

                case 'block':
                    // Return empty collection to block shipping
                    \Log::debug('Blocking shipping to unconfigured destination');
                    return collect();

                case 'contact_quote':
                    // Return empty collection, will be handled in getFallbackShippingMethods
                    \Log::debug('Contact for quote required for unconfigured destination');
                    return collect();

                default:
                    // Default to catch-all behavior
                    return ShippingZone::getCatchAllZones();
            }
        }

        // Return specific zones if found
        return $applicableSpecificZones;
    }

    /**
     * Find an applicable shipping rate for a method in a set of zones.
     *
     * @param ShippingMethod $method
     * @param Collection $zones
     * @param Cart $cart
     * @return ShippingRate|null
     */
    protected function findApplicableRate(ShippingMethod $method, Collection $zones, Cart $cart): ?ShippingRate
    {
        foreach ($zones as $zone) {
            \Log::debug("Checking zone {$zone->name} for method {$method->code}");

            $rate = $this->shippingRate->where('shipping_zone_id', $zone->id)
                ->where('shipping_method_id', $method->id)
                ->where('is_active', true)
                ->first();

            \Log::debug("Rate found: " . ($rate ? 'yes' : 'no'));

            if ($rate) {
                \Log::debug("Rate details: base_rate={$rate->base_rate}, min_price={$rate->min_price}, max_price={$rate->max_price}");

                $applies = $rate->appliesToCart($cart);
                \Log::debug("Rate applies to cart: " . ($applies ? 'yes' : 'no'));

                if ($applies) {
                    return $rate;
                }
            }
        }

        return null;
    }

    /**
     * Calculate the shipping cost for a rate.
     *
     * @param ShippingRate $rate
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return float
     */
    public function calculateShippingCost(ShippingRate $rate, Cart $cart, ?Address $shippingAddress = null): float
    {
        $calculator = $this->getCalculator($rate->method->method_type);
        return $calculator->calculate($rate, $cart, $shippingAddress);
    }

    /**
     * Get the appropriate calculator for a shipping method type.
     *
     * @param string $methodType
     * @return ShippingCalculatorInterface
     */
    protected function getCalculator(string $methodType): ShippingCalculatorInterface
    {
        return match ($methodType) {
            'weight_based' => new WeightBasedCalculator(),
            'price_based' => new PriceBasedCalculator(),
            'item_based' => new ItemBasedCalculator(),
            default => new FlatRateCalculator(),
        };
    }

    /**
     * Validate a shipping method for a cart and address.
     *
     * @param string $shippingMethod
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return bool
     */
    public function validateShippingMethod(string $shippingMethod, Cart $cart, ?Address $shippingAddress = null): bool
    {
        $availableMethods = $this->getAvailableShippingMethods($cart, $shippingAddress);

        return isset($availableMethods[$shippingMethod]) && $availableMethods[$shippingMethod]['available'];
    }

    /**
     * Validate a shipping cost for a method, cart, and address.
     *
     * @param string $shippingMethod
     * @param float $shippingCost
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @param float $tolerance
     * @return bool
     */
    public function validateShippingCost(
        string $shippingMethod,
        float $shippingCost,
        Cart $cart,
        ?Address $shippingAddress = null,
        float $tolerance = 0.01
    ): bool {
        $availableMethods = $this->getAvailableShippingMethods($cart, $shippingAddress);

        if (!isset($availableMethods[$shippingMethod]) || !$availableMethods[$shippingMethod]['available']) {
            return false;
        }

        $expectedCost = $availableMethods[$shippingMethod]['price'];

        // Allow for a small tolerance in floating-point comparisons
        return abs($shippingCost - $expectedCost) <= $tolerance;
    }

    /**
     * Get the default price for a shipping method when no rate is available.
     *
     * @param string $methodCode
     * @return float
     */
    protected function getDefaultPriceForMethod(string $methodCode): float
    {
        return match ($methodCode) {
            'standard' => 5.99,
            'express' => 12.99,
            'international' => 24.99,
            'weight_based' => 8.99,
            'local_pickup' => 0.00,
            default => 5.99,
        };
    }

    /**
     * Get shipping configuration settings.
     *
     * @return array
     */
    protected function getShippingConfiguration(): array
    {
        $config = \App\Models\Setting::getValue('shipping_configuration', []);

        if (is_string($config)) {
            $config = json_decode($config, true) ?: [];
        }

        // Provide default values
        return array_merge([
            'fallback_behavior' => 'catch_all',
            'enable_dynamic_calculations' => true,
            'log_shipping_calculations' => true,
            'require_shipping_address' => true,
            'prioritize_specific_zones' => true,
            'default_fallback_methods' => [
                'standard' => [
                    'name' => 'Standard Shipping',
                    'description' => 'Delivery in 3-5 business days',
                    'price' => 5.99,
                    'method_type' => 'flat_rate'
                ],
                'express' => [
                    'name' => 'Express Shipping',
                    'description' => 'Delivery in 1-2 business days',
                    'price' => 12.99,
                    'method_type' => 'flat_rate'
                ]
            ]
        ], $config);
    }
}
