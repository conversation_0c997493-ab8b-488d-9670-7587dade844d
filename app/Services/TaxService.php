<?php

namespace App\Services;

use App\Models\Address;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Product;
use App\Models\TaxRate;
use App\Models\TaxRule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class TaxService extends BaseService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        TaxRate $taxRate,
        protected ?TaxRule $taxRule = null
    ) {
        $this->model = $taxRate;
    }

    /**
     * Get all active tax rates.
     */
    public function getActiveTaxRates(): Collection
    {
        return $this->model->where('is_active', true)
            ->get();
    }

    /**
     * Get tax rate by country and region.
     * 
     * @param string $country The country code (2-letter ISO)
     * @param string|null $region The region/state code
     * @return TaxRate The tax rate (never returns null, falls back to default)
     * @throws \RuntimeException If no tax rates are configured at all
     */
    public function getTaxRateByLocation(string $country, string $region = null): TaxRate
    {
        // Validate country code
        if (empty($country) || strlen($country) !== 2) {
            Log::warning('Invalid or empty country code provided for tax calculation', [
                'country' => $country,
                'region' => $region
            ]);
            return $this->getDefaultTaxRate();
        }

        try {
            $query = $this->model->where('is_active', true)
                ->where(function($q) use ($country, $region) {
                    // First try to match by both country and region
                    $q->where('country', $country);
                    
                    if ($region) {
                        $q->where(function($q) use ($region) {
                            $q->where('region', $region)
                              ->orWhereNull('region');
                        });
                    } else {
                        $q->whereNull('region');
                    }
                })
                ->orderBy('region', 'desc') // Prefer more specific region matches
                ->orderBy('is_default', 'desc'); // Then prefer default rates

            $taxRate = $query->first();

            if ($taxRate) {
                return $taxRate;
            }

            // If no specific rate found, try to find a country-level rate
            $countryRate = $this->model->where('country', $country)
                ->whereNull('region')
                ->where('is_active', true)
                ->first();

            if ($countryRate) {
                return $countryRate;
            }

            // Fall back to default rate
            return $this->getDefaultTaxRate();

        } catch (\Exception $e) {
            Log::error('Error looking up tax rate', [
                'country' => $country,
                'region' => $region,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->getDefaultTaxRate();
        }
    }

    /**
     * Get the default tax rate.
     * 
     * @return TaxRate
     * @throws \RuntimeException If no default tax rate is configured
     */
    protected function getDefaultTaxRate(): TaxRate
    {
        $defaultRate = $this->model->where('is_default', true)
            ->where('is_active', true)
            ->first();

        if (!$defaultRate) {
            $error = 'No default tax rate is configured in the system. Please configure tax rates in the admin panel.';
            Log::emergency($error);
            throw new \RuntimeException($error);
        }

        return $defaultRate;
    }

    /**
     * Calculate tax for an amount.
     * 
     * @param float $amount The amount to calculate tax for
     * @param string $country The country code (2-letter ISO)
     * @param string|null $region The region/state code
     * @return float The calculated tax amount (never returns null)
     */
    public function calculateTax(float $amount, string $country, string $region = null): float
    {
        if ($amount <= 0) {
            return 0.0;
        }

        try {
            $taxRate = $this->getTaxRateByLocation($country, $region);
            
            if (!$taxRate->isValidForDate()) {
                Log::warning('Tax rate is not valid for current date', [
                    'tax_rate_id' => $taxRate->id,
                    'country' => $country,
                    'region' => $region
                ]);
                // Still use the rate but log the issue
            }

            $taxAmount = round($amount * ($taxRate->rate / 100), 2);
            
            // Log high-value transactions for audit
            if ($taxAmount > 1000) { // Adjust threshold as needed
                Log::info('High-value tax calculation', [
                    'amount' => $amount,
                    'tax_amount' => $taxAmount,
                    'rate' => $taxRate->rate,
                    'country' => $country,
                    'region' => $region,
                    'tax_rate_id' => $taxRate->id
                ]);
            }
            
            return $taxAmount;
            
        } catch (\Exception $e) {
            Log::error('Error calculating tax', [
                'amount' => $amount,
                'country' => $country,
                'region' => $region,
                'error' => $e->getMessage()
            ]);
            
            // In production, we might want to fail closed (throw exception)
            if (app()->environment('production')) {
                throw new \RuntimeException('Unable to calculate tax. Please try again or contact support.');
            }
            
            // In development, return 0 to avoid blocking the checkout flow
            return 0.0;
        }
    }

    /**
     * Calculate tax for a cart.
     */
    public function calculateCartTax(Cart $cart, Address $shippingAddress): array
    {
        $items = $cart->items->load('productVariant.product');

        return $this->calculateTaxForItems(
            $items,
            $shippingAddress,
            $cart->subtotal,
            $cart->shipping_cost ?? 0
        );
    }

    /**
     * Calculate tax for an order.
     */
    public function calculateOrderTax(Order $order): array
    {
        $items = $order->items;
        $shippingAddress = $order->shippingAddress;

        return $this->calculateTaxForItems(
            $items,
            $shippingAddress,
            $order->subtotal,
            $order->shipping_cost
        );
    }

    /**
     * Calculate tax for a collection of items.
     */
    protected function calculateTaxForItems(
        SupportCollection $items,
        Address $shippingAddress,
        float $subtotal,
        float $shippingCost
    ): array {
        // Get tax jurisdiction based on address
        $jurisdiction = $this->getTaxJurisdiction($shippingAddress);

        // Initialize tax breakdown
        $taxBreakdown = [
            'items' => [],
            'shipping' => 0,
            'total' => 0,
        ];

        // Calculate tax for each item
        foreach ($items as $item) {
            $product = $item->productVariant->product ?? null;

            if (!$product) {
                continue;
            }

            $taxRate = $this->getTaxRateForProduct($product, $jurisdiction);
            $itemTax = $this->calculateItemTax($item->unit_price, $item->quantity, $taxRate);

            $taxBreakdown['items'][] = [
                'item_id' => $item->id,
                'product_id' => $product->id,
                'product_name' => $product->getTranslation('name', app()->getLocale()),
                'tax_rate' => $taxRate,
                'tax_amount' => $itemTax,
            ];

            $taxBreakdown['total'] += $itemTax;
        }

        // Calculate shipping tax if applicable
        if ($shippingCost > 0) {
            $shippingTaxRate = $this->getShippingTaxRate($jurisdiction);
            $shippingTax = $this->calculateShippingTax($shippingCost, $shippingTaxRate);

            $taxBreakdown['shipping'] = $shippingTax;
            $taxBreakdown['total'] += $shippingTax;
        }

        // Apply any special tax rules
        $taxBreakdown = $this->applySpecialTaxRules($taxBreakdown, $subtotal, $jurisdiction);

        // Round the total tax
        $taxBreakdown['total'] = round($taxBreakdown['total'], 2);

        return $taxBreakdown;
    }

    /**
     * Create a new tax rate.
     */
    public function createTaxRate(array $data): TaxRate
    {
        return $this->create($data);
    }

    /**
     * Update a tax rate.
     */
    public function updateTaxRate(TaxRate $taxRate, array $data): TaxRate
    {
        return $this->update($taxRate, $data);
    }

    /**
     * Activate a tax rate.
     */
    public function activateTaxRate(TaxRate $taxRate): TaxRate
    {
        return $this->update($taxRate, [
            'is_active' => true,
        ]);
    }

    /**
     * Deactivate a tax rate.
     */
    public function deactivateTaxRate(TaxRate $taxRate): TaxRate
    {
        return $this->update($taxRate, [
            'is_active' => false,
        ]);
    }

    /**
     * Get tax jurisdiction based on address.
     * 
     * @param Address|null $address The shipping address (can be null for digital goods)
     * @return array The tax jurisdiction data
     */
    protected function getTaxJurisdiction(?Address $address): array
    {
        // If no address is provided, use the store's default location
        $country = $address->country ?? config('app.default_country', 'US');
        $state = $address->state ?? config('app.default_state');
        $city = $address->city ?? '';
        $postalCode = $address->postal_code ?? '';

        // Generate a cache key based on the available address components
        $cacheKey = "tax_jurisdiction:{$country}:{$state}:{$city}:{$postalCode}";

        return Cache::remember($cacheKey, now()->addHours(24), function () use ($country, $state, $city, $postalCode) {
            // Log when using fallback values
            if (empty($country)) {
                Log::warning('No country specified for tax jurisdiction, using default', [
                    'default_country' => config('app.default_country'),
                    'state' => $state,
                    'city' => $city
                ]);
                $country = config('app.default_country', 'US');
            }

            $jurisdiction = [
                'country' => strtoupper($country),
                'state' => $state ? strtoupper($state) : null,
                'city' => $city,
                'postal_code' => $postalCode,
                'is_eu' => in_array(strtoupper($country), [
                    'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR',
                    'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL',
                    'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE',
                ]),
                'is_remote' => empty($address),
            ];

            // Add US-specific data
            if ($jurisdiction['country'] === 'US') {
                $jurisdiction['has_nexus'] = true; // Simplified - in reality, would check if business has nexus in this state
                $jurisdiction['is_origin_based'] = in_array($jurisdiction['state'], 
                    ['AZ', 'IL', 'MO', 'NM', 'OH', 'PA', 'TN', 'TX', 'UT', 'VA']);
            }

            return $jurisdiction;
        });
    }

    /**
     * Get tax rate for a product in a specific jurisdiction.
     */
    protected function getTaxRateForProduct(Product $product, array $jurisdiction): float
    {
        // Cache the tax rate lookup to improve performance
        $cacheKey = "tax_rate:product:{$product->id}:jurisdiction:{$jurisdiction['country']}:{$jurisdiction['state']}";

        return Cache::remember($cacheKey, now()->addHours(24), function () use ($product, $jurisdiction) {
            // In a real implementation, this would query a tax rate database or API
            // based on the product category and jurisdiction

            // Check if the product is tax exempt
            if ($product->is_tax_exempt ?? false) {
                return 0;
            }

            // Get the product's tax category
            $taxCategory = $product->tax_category ?? 'standard';

            // Look up the tax rate based on jurisdiction and category
            return $this->getTaxRateByJurisdictionAndCategory($jurisdiction, $taxCategory);
        });
    }

    /**
     * Get tax rate by jurisdiction and category.
     */
    protected function getTaxRateByJurisdictionAndCategory(array $jurisdiction, string $category): float
    {
        // In a real implementation, this would query a tax rate database
        // For now, we'll use a simplified approach with hardcoded rates

        // US tax rates vary by state
        if ($jurisdiction['country'] === 'US') {
            $stateTaxRates = [
                'CA' => ['standard' => 0.0725, 'reduced' => 0.0725, 'food' => 0],
                'NY' => ['standard' => 0.04, 'reduced' => 0.04, 'food' => 0],
                'TX' => ['standard' => 0.0625, 'reduced' => 0.0625, 'food' => 0],
                // Add more states as needed
            ];

            $state = $jurisdiction['state'];
            return $stateTaxRates[$state][$category] ?? 0.05; // Default to 5%
        }

        // EU VAT rates
        if ($jurisdiction['is_eu']) {
            $euVatRates = [
                'DE' => ['standard' => 0.19, 'reduced' => 0.07, 'food' => 0.07],
                'FR' => ['standard' => 0.20, 'reduced' => 0.055, 'food' => 0.055],
                'UK' => ['standard' => 0.20, 'reduced' => 0.05, 'food' => 0],
                // Add more countries as needed
            ];

            $country = $jurisdiction['country'];
            return $euVatRates[$country][$category] ?? 0.21; // Default to 21%
        }

        // Other countries
        $otherTaxRates = [
            'CA' => ['standard' => 0.05, 'reduced' => 0.05, 'food' => 0], // GST only, not including PST/HST
            'AU' => ['standard' => 0.10, 'reduced' => 0.10, 'food' => 0],
            'JP' => ['standard' => 0.10, 'reduced' => 0.08, 'food' => 0.08],
            // Add more countries as needed
        ];

        $country = $jurisdiction['country'];
        return $otherTaxRates[$country][$category] ?? 0.10; // Default to 10%
    }

    /**
     * Calculate tax for an item.
     */
    protected function calculateItemTax(float $unitPrice, int $quantity, float $taxRate): float
    {
        $itemTotal = $unitPrice * $quantity;
        return $itemTotal * $taxRate;
    }

    /**
     * Get shipping tax rate for a jurisdiction.
     */
    protected function getShippingTaxRate(array $jurisdiction): float
    {
        // In many jurisdictions, shipping is taxed at the same rate as the items
        // In others, it may be exempt or have a different rate

        // For simplicity, we'll use a basic approach
        $shippingTaxRates = [
            'US' => 0, // Most US states don't tax shipping
            'CA' => 0.05, // GST
            'UK' => 0.20, // VAT
            'DE' => 0.19, // VAT
            'FR' => 0.20, // VAT
        ];

        return $shippingTaxRates[$jurisdiction['country']] ?? 0;
    }

    /**
     * Calculate shipping tax.
     */
    protected function calculateShippingTax(float $shippingCost, float $taxRate): float
    {
        return $shippingCost * $taxRate;
    }

    /**
     * Apply special tax rules.
     */
    protected function applySpecialTaxRules(array $taxBreakdown, float $subtotal, array $jurisdiction): array
    {
        // Apply tax thresholds
        // For example, in some jurisdictions, tax is only applied above a certain threshold

        // EU distance selling threshold example
        if (($jurisdiction['is_eu'] ?? false) && $subtotal < 10000 && !($jurisdiction['is_business'] ?? false)) {
            // Apply origin country tax rate instead of destination
            // This is a simplified example - real implementation would be more complex
        }

        // US economic nexus thresholds
        if ($jurisdiction['country'] === 'US' && !($jurisdiction['has_nexus'] ?? true)) {
            // If the business doesn't have nexus in this state, no tax is collected
            $taxBreakdown['total'] = 0;
            $taxBreakdown['items'] = array_map(function ($item) {
                $item['tax_amount'] = 0;
                return $item;
            }, $taxBreakdown['items']);
            $taxBreakdown['shipping'] = 0;
        }

        // Apply tax holidays
        // Some jurisdictions have tax-free periods for certain products
        $today = now();
        $taxHolidays = $this->getTaxHolidays($jurisdiction, $today);

        if (!empty($taxHolidays)) {
            // Apply tax holiday rules
            // This would reduce or eliminate tax on eligible items
        }

        return $taxBreakdown;
    }

    /**
     * Get tax holidays for a jurisdiction and date.
     */
    protected function getTaxHolidays(array $jurisdiction, \DateTime $date): array
    {
        // In a real implementation, this would query a database of tax holidays
        // For now, we'll return an empty array
        return [];
    }

    /**
     * Check if a customer is tax exempt.
     */
    public function isCustomerTaxExempt(string $customerId): bool
    {
        // In a real implementation, this would check if the customer has tax-exempt status
        // For now, we'll return false
        return false;
    }

    /**
     * Register a tax exemption certificate for a customer.
     */
    public function registerTaxExemptionCertificate(string $customerId, array $certificateData): bool
    {
        // In a real implementation, this would store the tax exemption certificate
        // and update the customer's tax-exempt status

        Log::info('Tax exemption certificate registered', [
            'customer_id' => $customerId,
            'certificate_data' => $certificateData,
        ]);

        return true;
    }

    /**
     * Validate a tax identification number (VAT, EIN, etc.).
     */
    public function validateTaxIdentificationNumber(string $number, string $country): array
    {
        // In a real implementation, this would validate the tax ID with the appropriate authority
        // For example, validating a VAT number with the EU VIES system

        // For now, we'll return a simplified response
        $valid = false;
        $message = 'Invalid tax identification number';

        // Simple format validation
        if ($country === 'US' && preg_match('/^\d{2}-\d{7}$/', $number)) {
            $valid = true;
            $message = 'Valid EIN format';
        } elseif ($country === 'UK' && preg_match('/^GB\d{9}$/', $number)) {
            $valid = true;
            $message = 'Valid UK VAT format';
        } elseif ($country === 'DE' && preg_match('/^DE\d{9}$/', $number)) {
            $valid = true;
            $message = 'Valid German VAT format';
        }

        return [
            'valid' => $valid,
            'message' => $message,
            'country' => $country,
            'number' => $number,
        ];
    }
}