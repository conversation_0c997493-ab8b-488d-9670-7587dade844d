<?php

namespace App\Services;

use App\Models\Address;
use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderService extends BaseService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        Order $order,
        protected OrderItem $orderItem,
        protected CartService $cartService,
        protected InventoryService $inventoryService,
        protected PaymentService $paymentService,
        protected ShippingService $shippingService,
        protected \App\Repositories\CheckoutRepository $checkoutRepository
    ) {
        $this->model = $order;
    }

    /**
     * Get all orders.
     */
    public function getAllOrders(): Collection
    {
        return $this->model->with(['user', 'items'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get orders by status.
     */
    public function getOrdersByStatus(string $status): Collection
    {
        return $this->model->where('status', $status)
            ->with(['user', 'items'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get orders by user.
     */
    public function getOrdersByUser(string $userId): Collection
    {
        return $this->model->where('user_id', $userId)
            ->with(['items'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get order by order number.
     */
    public function getOrderByNumber(string $orderNumber): ?Order
    {
        return $this->model->where('order_number', $orderNumber)
            ->with(['user', 'items', 'billingAddress', 'shippingAddress', 'payments'])
            ->first();
    }

    /**
     * Create a new order from cart.
     */
    public function createOrderFromCart(
        Cart $cart,
        array $orderData,
        Address $billingAddress,
        ?Address $shippingAddress = null,
        string $paymentMethod = 'stripe'
    ): ?Order {
        // Check if cart has items
        if ($cart->items->count() === 0) {
            return null;
        }

        // Begin transaction
        return DB::transaction(function () use ($cart, $orderData, $billingAddress, $shippingAddress, $paymentMethod) {
            // Calculate totals
            $subtotal = $cart->subtotal;
            $shippingCost = $orderData['shipping_cost'] ?? 0;
            $discountAmount = $orderData['discount_amount'] ?? 0;
            $taxAmount = $orderData['tax_amount'] ?? 0; // Get tax from orderData
            $taxDetails = $orderData['tax_details'] ?? null; // Get tax details from orderData

            $total = $subtotal + $shippingCost + $taxAmount - $discountAmount;

            // Create order
            $order = $this->create([
                'user_id' => $cart->user_id,
                'order_number' => $this->generateOrderNumber(),
                'billing_address_id' => $billingAddress->id,
                'shipping_address_id' => $shippingAddress ? $shippingAddress->id : $billingAddress->id,
                'customer_email' => $orderData['customer_email'],
                'customer_name' => $orderData['customer_name'],
                'customer_phone' => $orderData['customer_phone'] ?? null,
                'status' => 'pending',
                'subtotal' => $subtotal,
                'shipping_cost' => $shippingCost,
                'tax_amount' => $taxAmount,
                'tax_details' => $taxDetails, // Store tax details
                'discount_amount' => $discountAmount, // Add discount if calculated
                'total' => $total,
                'currency' => $orderData['currency'] ?? 'USD',
                'shipping_method' => $orderData['shipping_method'] ?? null,
                'notes' => $orderData['notes'] ?? null,
            ]);

            // Create order items
            foreach ($cart->items as $cartItem) {
                $variant = $cartItem->productVariant;
                $product = $variant->product;

                $this->orderItem->create([
                    'order_id' => $order->id,
                    'product_variant_id' => $variant->id,
                    'product_name' => $product->getTranslation('name', app()->getLocale()),
                    'variant_name' => $variant->getTranslation('name', app()->getLocale()),
                    'sku' => $variant->sku,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->unit_price,
                    'subtotal' => $cartItem->subtotal,
                    'tax_amount' => 0, // Calculate tax if needed
                    'discount_amount' => 0, // Calculate discount if needed
                    'total' => $cartItem->subtotal,
                    'product_snapshot' => [
                        'product' => $product->toArray(),
                        'variant' => $variant->toArray(),
                    ],
                ]);
            }

            // Adjust inventory
            $this->inventoryService->adjustInventoryAfterOrder($order->items);

            // Create payment
            $this->paymentService->createPayment([
                'order_id' => $order->id,
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'amount' => $total,
                'currency' => $orderData['currency'] ?? 'USD',
            ]);

            // Clear cart
            $this->cartService->clearCart($cart);

            return $order;
        });
    }

    /**
     * Update order status.
     */
    public function updateOrderStatus(Order $order, string $status): Order
    {
        return $this->update($order, [
            'status' => $status,
        ]);
    }

    /**
     * Update order shipping information.
     */
    public function updateShippingInfo(Order $order, string $shippingMethod, ?string $trackingNumber = null): Order
    {
        return $this->update($order, [
            'shipping_method' => $shippingMethod,
            'tracking_number' => $trackingNumber,
        ]);
    }

    /**
     * Cancel order.
     */
    public function cancelOrder(Order $order, ?string $reason = null): Order
    {
        if (!$order->canBeCancelled()) {
            throw new \Exception('Order cannot be cancelled.');
        }

        return DB::transaction(function () use ($order, $reason) {
            // Update order status
            $order = $this->updateOrderStatus($order, 'cancelled');

            // Add note if provided
            if ($reason) {
                $order = $this->update($order, [
                    'notes' => ($order->notes ? $order->notes . "\n" : '') . "Cancellation reason: {$reason}",
                ]);
            }

            // Restore inventory using the improved inventory service
            $this->inventoryService->restoreInventoryAfterOrderCancellation($order->items);

            return $order;
        });
    }

    /**
     * Generate a unique order number.
     */
    protected function generateOrderNumber(): string
    {
        $prefix = 'ORD-';
        $timestamp = now()->format('Ymd');
        $random = strtoupper(Str::random(4));
        $orderNumber = $prefix . $timestamp . $random;

        // Check if the order number already exists
        while ($this->model->where('order_number', $orderNumber)->exists()) {
            $random = strtoupper(Str::random(4));
            $orderNumber = $prefix . $timestamp . $random;
        }

        return $orderNumber;
    }

    /**
     * Get available shipping methods for the cart.
     *
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return array
     */
    public function getShippingMethods(Cart $cart, ?Address $shippingAddress = null): array
    {
        // Use the ShippingService to get available shipping methods
        return $this->shippingService->getAvailableShippingMethods($cart, $shippingAddress);
    }

    /**
     * Validate shipping method and cost.
     *
     * @param string $shippingMethod
     * @param float $shippingCost
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return bool
     */
    public function validateShippingMethodAndCost(
        string $shippingMethod,
        float $shippingCost,
        Cart $cart,
        ?Address $shippingAddress = null
    ): bool {
        return $this->shippingService->validateShippingMethod($shippingMethod, $cart, $shippingAddress) &&
               $this->shippingService->validateShippingCost($shippingMethod, $shippingCost, $cart, $shippingAddress);
    }
}
