<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CartService extends BaseService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        Cart $cart,
        protected CartItem $cartItem,
        protected InventoryService $inventoryService
    ) {
        $this->model = $cart;
    }

    /**
     * Get cart by ID.
     */
    public function getCartById(string $cartId): ?Cart
    {
        $cart = $this->model->with('items.productVariant.product')
            ->find($cartId);

        // Ensure we return the correct type
        return $cart instanceof Cart ? $cart : null;
    }

    /**
     * Get cart by session ID.
     */
    public function getCartBySessionId(string $sessionId): ?Cart
    {
        $cart = $this->model->with('items.productVariant.product')
            ->where('session_id', $sessionId)
            ->first();

        // Ensure we return the correct type
        return $cart instanceof Cart ? $cart : null;
    }

    /**
     * Get cart by user ID.
     */
    public function getCartByUserId(string $userId): ?Cart
    {
        $cart = $this->model->with('items.productVariant.product')
            ->where('user_id', $userId)
            ->first();

        // Ensure we return the correct type
        return $cart instanceof Cart ? $cart : null;
    }

    /**
     * Create a new cart.
     */
    public function createCart(array $data): Cart
    {
        // Set expiration date if not provided
        if (!isset($data['expires_at'])) {
            $data['expires_at'] = now()->addDays(30);
        }

        $cart = $this->create($data);

        // Ensure we return the correct type
        if (!($cart instanceof Cart)) {
            // This should never happen, but just in case
            $cart = Cart::find($cart->getKey());
        }

        return $cart;
    }

    /**
     * Get or create a cart for a session.
     */
    public function getOrCreateCartForSession(string $sessionId): Cart
    {
        $cart = $this->getCartBySessionId($sessionId);

        if (!$cart) {
            $cart = $this->createCart([
                'session_id' => $sessionId,
            ]);
        }

        return $cart;
    }

    /**
     * Get or create a cart for a user.
     */
    public function getOrCreateCartForUser(User $user): Cart
    {
        $cart = $this->getCartByUserId($user->id);

        if (!$cart) {
            $cart = $this->createCart([
                'user_id' => $user->id,
            ]);
        }

        return $cart;
    }

    /**
     * Merge guest cart into user cart.
     */
    public function mergeGuestCartIntoUserCart(string $sessionId, User $user): Cart
    {
        $guestCart = $this->getCartBySessionId($sessionId);
        $userCart = $this->getOrCreateCartForUser($user);

        if ($guestCart && $guestCart->items->count() > 0) {
            // Begin transaction
            DB::transaction(function () use ($guestCart, $userCart) {
                foreach ($guestCart->items as $item) {
                    $this->addItemToCart($userCart, $item->product_variant_id, $item->quantity);
                }

                // Delete guest cart
                $guestCart->items()->delete();
                $guestCart->stockReservations()->delete();
                $guestCart->delete();
            });
        }

        return $userCart->fresh(['items.productVariant.product']);
    }

    /**
     * Add item to cart.
     */
    public function addItemToCart(Cart $cart, string $variantId, int $quantity = 1): ?CartItem
    {
        $variant = ProductVariant::with('product')->find($variantId);

        if (!$variant || !$variant->is_active || !$variant->product->is_active) {
            return null;
        }

        // Check if the item is already in the cart
        $cartItem = $cart->items()->where('product_variant_id', $variantId)->first();

        if ($cartItem) {
            // Ensure we have a CartItem instance
            if (!($cartItem instanceof CartItem)) {
                $cartItem = CartItem::find($cartItem->getKey());
            }

            // Update quantity
            return $this->updateCartItemQuantity($cartItem, $cartItem->quantity + $quantity);
        }

        // Check if there's enough stock
        if (!$this->inventoryService->isInStock($variant, $quantity)) {
            return null;
        }

        // Begin transaction
        return DB::transaction(function () use ($cart, $variant, $quantity) {
            // Reserve stock
            $reservation = $this->inventoryService->reserveStock($variant, $cart->id, $quantity);

            // Check if reservation failed
            if (!$reservation) {
                Log::error('Failed to reserve stock when adding item to cart', [
                    'cart_id' => $cart->id,
                    'variant_id' => $variant->id,
                    'quantity' => $quantity,
                ]);
                // Returning null from the transaction callback will cause the transaction to rollback
                return null;
            }

            // Create cart item
            return $this->cartItem->create([
                'cart_id' => $cart->id,
                'product_variant_id' => $variant->id,
                'quantity' => $quantity,
                'unit_price' => $variant->price,
            ]);
        });
    }

    /**
     * Update cart item quantity.
     */
    public function updateCartItemQuantity(CartItem $cartItem, int $quantity): ?CartItem
    {
        if ($quantity <= 0) {
            $this->removeItemFromCart($cartItem);
            return null;
        }

        $variant = $cartItem->productVariant;

        if (!$variant) {
            return null;
        }

        // Calculate quantity difference
        $quantityDiff = $quantity - $cartItem->quantity;

        if ($quantityDiff > 0) {
            // Check if there's enough stock for the additional quantity
            if (!$this->inventoryService->isInStock($variant, $quantityDiff)) {
                return null;
            }

            // Reserve additional stock
            $reservation = $this->inventoryService->reserveStock($variant, $cartItem->cart_id, $quantityDiff);

            // Check if reservation failed
            if (!$reservation) {
                Log::error('Failed to reserve additional stock when updating cart item quantity', [
                    'cart_item_id' => $cartItem->id,
                    'variant_id' => $variant->id,
                    'quantity_diff' => $quantityDiff,
                ]);
                return null; // Indicate failure
            }
        } elseif ($quantityDiff < 0) {
            // Release excess stock
            $reservation = $variant->stockReservations()
                ->where('cart_id', $cartItem->cart_id)
                ->first();

            if ($reservation) {
                // Ensure we have a StockReservation instance
                if (!($reservation instanceof \App\Models\StockReservation)) {
                    $reservation = \App\Models\StockReservation::find($reservation->getKey());
                }

                // Create a new reservation with the reduced quantity
                $this->inventoryService->releaseReservedStock($reservation);
                $newReservation = $this->inventoryService->reserveStock($variant, $cartItem->cart_id, $quantity);

                // Check if the new reservation failed
                if (!$newReservation) {
                     Log::error('Failed to create new reservation after releasing old one when updating cart item quantity', [
                        'cart_item_id' => $cartItem->id,
                        'variant_id' => $variant->id,
                        'quantity' => $quantity,
                    ]);
                    return null; // Indicate failure
                }
            } else {
                 // This case should ideally not happen if there was a reservation to release,
                 // but adding a log for awareness.
                 Log::warning('Attempted to decrease cart item quantity but no reservation found to release', [
                    'cart_item_id' => $cartItem->id,
                    'variant_id' => $variant->id,
                    'quantity_diff' => $quantityDiff,
                 ]);
                 // Depending on desired behavior, might return null or proceed.
                 // Proceeding might lead to inconsistent state if stock wasn't reserved.
                 // Returning null is safer to indicate an issue.
                 return null;
            }
        }

        // Update cart item
        $cartItem->update([
            'quantity' => $quantity,
        ]);

        return $cartItem->fresh();
    }

    /**
     * Remove item from cart.
     */
    public function removeItemFromCart(CartItem $cartItem): bool
    {
        $variant = $cartItem->productVariant;

        if ($variant) {
            // Release stock reservation
            $reservation = $variant->stockReservations()
                ->where('cart_id', $cartItem->cart_id)
                ->first();

            if ($reservation) {
                // Ensure we have a StockReservation instance
                if (!($reservation instanceof \App\Models\StockReservation)) {
                    $reservation = \App\Models\StockReservation::find($reservation->getKey());
                }

                $this->inventoryService->releaseReservedStock($reservation);
            }
        }

        // Delete cart item
        return $cartItem->delete();
    }

    /**
     * Clear cart.
     */
    public function clearCart(Cart $cart): bool
    {
        // Release all stock reservations
        foreach ($cart->stockReservations as $reservation) {
            // Ensure we have a StockReservation instance
            if (!($reservation instanceof \App\Models\StockReservation)) {
                $reservation = \App\Models\StockReservation::find($reservation->getKey());
            }

            $this->inventoryService->releaseReservedStock($reservation);
        }

        // Delete all cart items
        $cart->items()->delete();

        return true;
    }

    /**
     * Generate a unique cart ID.
     */
    public function generateCartId(): string
    {
        return (string) Str::uuid();
    }

    /**
     * Recover an abandoned cart.
     */
    public function recoverCart(string $cartId): ?Cart
    {
        $cart = $this->getCartById($cartId);

        if (!$cart) {
            return null;
        }

        // Check if cart is expired
        if ($cart->isExpired()) {
            Log::info('Attempted to recover expired cart', [
                'cart_id' => $cart->id,
                'user_id' => $cart->user_id,
                'last_updated' => $cart->updated_at->toIso8601String(),
            ]);

            return null;
        }

        // Mark cart as recovered
        $cart->update([
            'status' => 'recovered',
            'recovered_at' => now(),
        ]);

        Log::info('Cart recovered', [
            'cart_id' => $cart->id,
            'user_id' => $cart->user_id,
        ]);

        return $cart->fresh(['items.productVariant.product']);
    }

    /**
     * Find abandoned carts for recovery emails.
     */
    public function findAbandonedCarts(): array
    {
        // Find carts that are between 1 and 24 hours old
        $abandonedCarts = $this->model->where('updated_at', '<', now()->subHour())
            ->where('updated_at', '>', now()->subHours(24))
            ->where('status', 'active')
            ->whereNull('recovery_email_sent_at')
            ->whereNotNull('user_id')
            ->whereHas('items') // Only carts with items
            ->get();

        return [
            'total' => $abandonedCarts->count(),
            'carts' => $abandonedCarts,
        ];
    }

    /**
     * Mark a cart as having received a recovery email.
     */
    public function markCartRecoveryEmailSent(Cart $cart): Cart
    {
        $cart->update([
            'recovery_email_sent_at' => now(),
        ]);

        return $cart->fresh();
    }
}
