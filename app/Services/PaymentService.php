<?php

namespace App\Services;

use App\Contracts\PaymentGateway;
use App\Models\Order;
use App\Models\Payment;
use App\Services\Gateways\PaymentGatewayFactory;
use Illuminate\Database\Eloquent\Collection;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentService extends BaseService
{
    /**
     * Maximum number of retry attempts for payment operations.
     */
    protected const MAX_RETRY_ATTEMPTS = 3;
    /**
     * Create a new service instance.
     */
    public function __construct(Payment $payment)
    {
        $this->model = $payment;
    }

    /**
     * Get payments by order.
     */
    public function getPaymentsByOrder(string $orderId): Collection
    {
        return $this->model->where('order_id', $orderId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get payment by transaction ID.
     */
    public function getPaymentByTransactionId(string $transactionId): ?Payment
    {
        return $this->model->where('transaction_id', $transactionId)
            ->first();
    }

    /**
     * Create a new payment.
     */
    public function createPayment(array $data): Payment
    {
        return $this->create($data);
    }

    /**
     * Update payment status.
     */
    public function updatePaymentStatus(Payment $payment, string $status, array $paymentDetails = null): Payment
    {
        $data = ['status' => $status];

        if ($paymentDetails !== null) {
            $data['payment_details'] = $paymentDetails;
        }

        $payment = $this->update($payment, $data);

        // If payment is completed, update order status
        if ($status === 'completed') {
            $this->updateOrderStatusAfterPayment($payment->order);
        }

        return $payment;
    }

    /**
     * Update payment with the given data.
     */
    public function updatePayment(Payment $payment, array $data): Payment
    {
        return $this->update($payment, $data);
    }

    /**
     * Process payment.
     *
     * This method handles all payment processing, including cases where
     * additional actions are required (like 3D Secure authentication).
     * 
     * @param Order $order The order to process payment for
     * @param string $paymentMethod The payment method to use
     * @param array $paymentData Payment data including optional idempotency_key
     * @return Payment|null The processed payment or null on failure
     * @throws \Exception If payment processing fails
     */
    public function processPayment(Order $order, string $paymentMethod, array $paymentData): ?Payment
    {
        // Generate or use provided idempotency key
        $idempotencyKey = $paymentData['idempotency_key'] ?? (string) Str::uuid();
        
        // Check for existing payment with this idempotency key
        $existingPayment = $this->model->where('idempotency_key', $idempotencyKey)->first();
        if ($existingPayment) {
            Log::info('Found existing payment with the same idempotency key', [
                'idempotency_key' => $idempotencyKey,
                'payment_id' => $existingPayment->id,
                'order_id' => $order->id
            ]);
            return $existingPayment;
        }

        // Begin transaction
        return DB::transaction(function () use ($order, $paymentMethod, $paymentData, $idempotencyKey) {
            // Create payment record
            $payment = $this->createPayment([
                'order_id' => $order->id,
                'payment_method' => $paymentMethod,
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'status' => 'processing',
                'amount' => $order->total,
                'currency' => $order->currency,
                'payment_details' => $paymentData,
                'idempotency_key' => $idempotencyKey,
            ]);

            // Process payment based on payment method
            $success = false;
            $requiresAction = false;
            $paymentDetails = [];

            try {
                switch ($paymentMethod) {
                    case 'stripe':
                        $result = $this->processStripePayment($order, $paymentData);
                        $success = $result['success'];
                        $requiresAction = $result['requires_action'] ?? false;
                        $paymentDetails = $result['details'];
                        break;
                    case 'paypal':
                        $result = $this->processPayPalPayment($order, $paymentData);
                        $success = $result['success'];
                        $requiresAction = $result['requires_action'] ?? false;
                        $paymentDetails = $result['details'];
                        break;
                    case 'bank_transfer':
                        // Process bank transfer payment
                        $gateway = $this->getGateway('bank_transfer');
                        $result = $gateway->createPaymentIntent($order, $paymentData);
                        $success = $result['success'];
                        $paymentDetails = $result['payment_details'] ?? [];
                        break;
                    default:
                        throw new \Exception("Unsupported payment method: {$paymentMethod}");
                }
            } catch (\Exception $e) {
                Log::error('Payment processing error: ' . $e->getMessage(), [
                    'order_id' => $order->id,
                    'payment_method' => $paymentMethod,
                    'exception' => $e,
                ]);

                $success = false;
                $paymentDetails = [
                    'error' => $e->getMessage(),
                    'error_code' => 'payment_processing_error',
                ];
            }

            // Update payment status based on result
            if ($success) {
                if ($requiresAction) {
                    // Payment requires additional action (like 3D Secure)
                    $this->updatePaymentStatus($payment, 'requires_action', $paymentDetails);
                } else if ($paymentMethod === 'bank_transfer') {
                    // Bank transfers are pending manual verification
                    $this->updatePaymentStatus($payment, 'pending', $paymentDetails);
                } else {
                    // Payment is completed
                    $this->updatePaymentStatus($payment, 'completed', $paymentDetails);

                    // Update order status if needed
                    if ($order->status === 'pending') {
                        $order->update(['status' => 'processing']);
                    }
                }
            } else {
                $this->updatePaymentStatus($payment, 'failed', $paymentDetails);

                // Update order status if needed
                if ($order->status === 'pending') {
                    $order->update(['status' => 'payment_failed']);
                }
            }

            return $payment->fresh();
        });
    }

    /**
     * Process refund.
     */
    public function processRefund(Payment $payment, float $amount = null, string $reason = null): ?Payment
    {
        if (!$payment->isCompleted()) {
            throw new \Exception('Payment is not completed and cannot be refunded.');
        }

        $amount = $amount ?? $payment->amount;

        if ($amount <= 0 || $amount > $payment->amount) {
            throw new \Exception('Invalid refund amount.');
        }

        // Begin transaction
        return DB::transaction(function () use ($payment, $amount, $reason) {
            // Create refund record
            $refund = $this->createPayment([
                'order_id' => $payment->order_id,
                'payment_method' => $payment->payment_method,
                'transaction_id' => 'refund_' . ($payment->transaction_id ?? uniqid()),
                'status' => 'processing',
                'amount' => -$amount, // Negative amount for refund
                'currency' => $payment->currency,
                'payment_details' => [
                    'refunded_payment_id' => $payment->id,
                    'reason' => $reason,
                    'refund_date' => now()->toIso8601String(),
                ],
            ]);

            // Process refund based on payment method
            $success = false;
            $refundDetails = [];

            try {
                switch ($payment->payment_method) {
                    case 'stripe':
                        $result = $this->processStripeRefund($payment, $amount, $reason);
                        $success = $result['success'];
                        $refundDetails = $result['details'];
                        break;
                    case 'paypal':
                        $result = $this->processPayPalRefund($payment, $amount, $reason);
                        $success = $result['success'];
                        $refundDetails = $result['details'];
                        break;
                    default:
                        throw new \Exception("Unsupported payment method for refund: {$payment->payment_method}");
                }
            } catch (\Exception $e) {
                Log::error('Refund processing error: ' . $e->getMessage(), [
                    'payment_id' => $payment->id,
                    'amount' => $amount,
                    'exception' => $e,
                ]);

                $success = false;
                $refundDetails = [
                    'error' => $e->getMessage(),
                    'error_code' => 'refund_processing_error',
                ];
            }

            // Update refund status based on result
            if ($success) {
                $this->updatePaymentStatus($refund, 'completed', $refundDetails);

                // Update original payment status if full refund
                if ($amount === $payment->amount) {
                    $this->updatePaymentStatus($payment, 'refunded');

                    // Update order status
                    $order = $payment->order;
                    if ($order) {
                        $order->update(['status' => 'refunded']);
                    }
                }
            } else {
                $this->updatePaymentStatus($refund, 'failed', $refundDetails);
            }

            return $refund->fresh();
        });
    }

    /**
     * Update order status after payment.
     */
    protected function updateOrderStatusAfterPayment(Order $order): void
    {
        if ($order->status === 'pending') {
            $order->update(['status' => 'processing']);
        }
    }

    /**
     * Process Stripe payment.
     */
    protected function processStripePayment(Order $order, array $paymentData): array
    {
        try {
            // Validate required data
            if (!isset($paymentData['payment_method_id'])) {
                throw new \Exception('Stripe payment method ID is required.');
            }

            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Create a payment intent with the payment method
            $result = $gateway->createPaymentIntent($order, [
                'payment_method' => $paymentData['payment_method_id'],
                'confirm' => true,
            ]);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Payment failed',
                        'error_code' => 'stripe_payment_error',
                    ],
                ];
            }

            // If payment requires further action, return that info
            if (isset($result['requires_action']) && $result['requires_action']) {
                return [
                    'success' => true,
                    'requires_action' => true,
                    'details' => [
                        'transaction_id' => $result['payment_intent_id'],
                        'payment_method' => 'stripe',
                        'payment_date' => now()->toIso8601String(),
                        'client_secret' => $result['client_secret'],
                        'stripe_response' => $result,
                    ],
                ];
            }

            // Payment succeeded
            return [
                'success' => true,
                'details' => [
                    'transaction_id' => $result['payment_intent_id'],
                    'payment_method' => 'stripe',
                    'payment_date' => now()->toIso8601String(),
                    'stripe_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing Stripe payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'stripe_payment_error',
                ],
            ];
        }
    }

    /**
     * Process PayPal payment.
     */
    protected function processPayPalPayment(Order $order, array $paymentData): array
    {
        try {
            // Validate required data
            if (!isset($paymentData['paypal_order_id'])) {
                throw new \Exception('PayPal order ID is required.');
            }

            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Execute the payment
            $result = $gateway->executePayment($paymentData['paypal_order_id'], [
                'payer_id' => $paymentData['payer_id'] ?? null,
            ]);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Payment failed',
                        'error_code' => 'paypal_payment_error',
                    ],
                ];
            }

            // Payment succeeded
            return [
                'success' => true,
                'details' => [
                    'transaction_id' => $result['payment_intent_id'] ?? $paymentData['paypal_order_id'],
                    'payment_method' => 'paypal',
                    'payment_date' => now()->toIso8601String(),
                    'paypal_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing PayPal payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'paypal_payment_error',
                ],
            ];
        }
    }

    /**
     * Process Stripe refund.
     */
    protected function processStripeRefund(Payment $payment, float $amount, string $reason = null): array
    {
        try {
            // Validate payment has a transaction ID
            if (!isset($payment->transaction_id)) {
                throw new \Exception('Payment has no transaction ID.');
            }

            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Process the refund
            $result = $gateway->refundPayment($payment, $amount, $reason);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Refund failed',
                        'error_code' => 'stripe_refund_error',
                    ],
                ];
            }

            // Refund succeeded
            return [
                'success' => true,
                'details' => [
                    'refund_id' => $result['refund_id'],
                    'refund_date' => now()->toIso8601String(),
                    'stripe_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing Stripe refund', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'stripe_refund_error',
                ],
            ];
        }
    }

    /**
     * Process PayPal refund.
     */
    protected function processPayPalRefund(Payment $payment, float $amount, string $reason = null): array
    {
        try {
            // Validate payment has a transaction ID
            if (!isset($payment->transaction_id)) {
                throw new \Exception('Payment has no transaction ID.');
            }

            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Process the refund
            $result = $gateway->refundPayment($payment, $amount, $reason);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Refund failed',
                        'error_code' => 'paypal_refund_error',
                    ],
                ];
            }

            // Refund succeeded
            return [
                'success' => true,
                'details' => [
                    'refund_id' => $result['refund_id'],
                    'refund_date' => now()->toIso8601String(),
                    'paypal_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing PayPal refund', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'paypal_refund_error',
                ],
            ];
        }
    }

    /**
     * Create a payment intent with Stripe.
     */
    public function createStripePaymentIntent(Order $order, bool $retry = true): array
    {
        try {
            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Create a payment intent
            return $gateway->createPaymentIntent($order);
        } catch (\Exception $e) {
            Log::error('Error creating Stripe payment intent: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'exception' => $e,
            ]);

            // Retry if enabled and not already retrying
            if ($retry && $this->shouldRetry('stripe_intent_' . $order->id)) {
                Log::info('Retrying Stripe payment intent creation', [
                    'order_id' => $order->id,
                    'attempt' => $this->getRetryAttempt('stripe_intent_' . $order->id),
                ]);

                return $this->createStripePaymentIntent($order, false);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while creating the payment intent',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a PayPal order.
     */
    public function createPayPalPayment(Order $order, string $returnUrl, string $cancelUrl, bool $retry = true): array
    {
        try {
            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Create a payment intent (PayPal order)
            return $gateway->createPaymentIntent($order, [
                'return_url' => $returnUrl,
                'cancel_url' => $cancelUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating PayPal payment: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'exception' => $e,
            ]);

            // Retry if enabled and not already retrying
            if ($retry && $this->shouldRetry('paypal_payment_' . $order->id)) {
                Log::info('Retrying PayPal payment creation', [
                    'order_id' => $order->id,
                    'attempt' => $this->getRetryAttempt('paypal_payment_' . $order->id),
                ]);

                return $this->createPayPalPayment($order, $returnUrl, $cancelUrl, false);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while creating the PayPal payment',
                'error' => $e->getMessage(),
            ];
        }
    }
    /**
     * Execute a PayPal payment.
     *
     * This method captures a PayPal payment and handles connection loss recovery.
     * It includes robust error handling and retry mechanisms to ensure payments
     * are properly processed even if there are network issues.
     */
    public function executePayPalPayment(string $paymentId, string $payerId, bool $retry = true): array
    {
        try {
            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Check if payment already exists and is completed
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();

            if ($payment && $payment->status === 'completed') {
                Log::info('PayPal payment already completed', [
                    'payment_id' => $paymentId,
                    'internal_payment_id' => $payment->id,
                ]);

                return [
                    'success' => true,
                    'message' => 'Payment already completed',
                    'payment' => $payment,
                ];
            }

            // Execute the payment
            $result = $gateway->executePayment($paymentId, [
                'payer_id' => $payerId,
            ]);

            // If successful, log the success
            if ($result['success']) {
                Log::info('PayPal payment executed successfully', [
                    'payment_id' => $paymentId,
                    'payer_id' => $payerId,
                    'internal_payment_id' => $result['payment']->id ?? null,
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            // Check if this is a network error or connection loss
            $isConnectionError = (
                strpos($e->getMessage(), 'cURL error') !== false ||
                strpos($e->getMessage(), 'Connection') !== false ||
                strpos($e->getMessage(), 'timeout') !== false ||
                $e instanceof \GuzzleHttp\Exception\ConnectException
            );

            Log::error('Error executing PayPal payment: ' . $e->getMessage(), [
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
                'is_connection_error' => $isConnectionError,
                'exception' => $e,
            ]);

            // For connection errors, we should check if the payment was actually processed
            if ($isConnectionError) {
                try {
                    // Get the payment from our database
                    $payment = Payment::where('gateway_payment_id', $paymentId)->first();

                    if ($payment) {
                        // Check with PayPal if the order was actually captured
                        $verifyResult = $gateway->verifyPayment($paymentId);

                        if ($verifyResult['success'] && isset($verifyResult['status']) && $verifyResult['status'] === 'completed') {
                            Log::info('PayPal payment verified after connection error', [
                                'payment_id' => $paymentId,
                                'internal_payment_id' => $payment->id,
                            ]);

                            // Update the payment status if needed
                            if ($payment->status !== 'completed') {
                                $payment->update([
                                    'status' => 'completed',
                                    'processed_at' => now(),
                                    'metadata' => array_merge($payment->metadata ?? [], [
                                        'recovery_method' => 'connection_error_verification',
                                        'verification_result' => $verifyResult,
                                    ]),
                                ]);

                                // Update order status if needed
                                $order = $payment->order;
                                if ($order && $order->status === 'pending') {
                                    $order->update(['status' => 'processing']);
                                }
                            }

                            return [
                                'success' => true,
                                'message' => 'Payment verified after connection error',
                                'payment' => $payment,
                                'verification_result' => $verifyResult,
                            ];
                        }
                    }
                } catch (\Exception $verifyException) {
                    Log::error('Error verifying PayPal payment after connection error: ' . $verifyException->getMessage(), [
                        'payment_id' => $paymentId,
                        'exception' => $verifyException,
                    ]);
                }
            }

            // Retry if enabled and not already retrying
            if ($retry && $this->shouldRetry('paypal_execute_' . $paymentId)) {
                $attempt = $this->getRetryAttempt('paypal_execute_' . $paymentId);

                Log::info('Retrying PayPal payment execution', [
                    'payment_id' => $paymentId,
                    'payer_id' => $payerId,
                    'attempt' => $attempt,
                ]);

                // Add exponential backoff for retries
                $backoffSeconds = min(pow(2, $attempt - 1), 30); // Max 30 seconds
                if ($backoffSeconds > 0) {
                    sleep($backoffSeconds);
                }

                return $this->executePayPalPayment($paymentId, $payerId, false);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while executing the PayPal payment',
                'error' => $e->getMessage(),
                'is_connection_error' => $isConnectionError,
            ];
        }
    }

    /**
     * Verify a Stripe payment.
     */
    public function verifyStripePayment(string $paymentIntentId): array
    {
        try {
            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Verify the payment
            return $gateway->verifyPayment($paymentIntentId);
        } catch (\Exception $e) {
            Log::error('Error verifying Stripe payment: ' . $e->getMessage(), [
                'payment_intent_id' => $paymentIntentId,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while verifying the payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process a refund.
     */
    public function refundPayment(Payment $payment, ?float $amount = null, ?string $reason = null): array
    {
        try {
            // Get the appropriate gateway
            $gateway = $this->getGateway($payment->payment_method);

            // Process the refund
            return $gateway->refundPayment($payment, $amount, $reason);
        } catch (\Exception $e) {
            Log::error('Error processing refund: ' . $e->getMessage(), [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'reason' => $reason,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing the refund',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate a webhook payload.
     *
     * This method validates the webhook signature to ensure it was sent by the payment
     * gateway and has not been tampered with. It supports different validation methods
     * for each gateway.
     */
    public function validateWebhook(string $gateway, array $payload, string $signature, string $signatureHeader): bool
    {
        try {
            // Get the appropriate gateway
            $gatewayInstance = $this->getGateway($gateway);

            // Add specific logging for PayPal webhooks
            if ($gateway === 'paypal') {
                Log::debug("Validating PayPal webhook signature", [
                    'event_type' => $payload['event_type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                    'signature_length' => strlen($signature),
                    'header_length' => strlen($signatureHeader),
                ]);
            }

            // Validate the webhook
            $isValid = $gatewayInstance->validateWebhook($payload, $signature, $signatureHeader);

            if (!$isValid) {
                Log::warning("Invalid {$gateway} webhook signature", [
                    'event_type' => $payload['event_type'] ?? $payload['type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                ]);
            }

            return $isValid;
        } catch (\Exception $e) {
            Log::error('Error validating webhook: ' . $e->getMessage(), [
                'gateway' => $gateway,
                'event_type' => $payload['event_type'] ?? $payload['type'] ?? 'unknown',
                'event_id' => $payload['id'] ?? null,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Process a webhook event.
     *
     * This method handles webhook events from payment gateways, updating the
     * payment and order status accordingly. It includes robust error handling
     * and recovery mechanisms.
     */
    public function processWebhookEvent(string $gateway, array $payload): array
    {
        try {
            // Get the appropriate gateway
            $gatewayInstance = $this->getGateway($gateway);

            // Log the event processing
            if ($gateway === 'paypal') {
                Log::info("Processing PayPal webhook event", [
                    'event_type' => $payload['event_type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                    'resource_type' => $payload['resource_type'] ?? 'unknown',
                ]);
            } else {
                Log::info("Processing {$gateway} webhook event", [
                    'event_type' => $payload['type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                ]);
            }

            // Process the webhook event
            $result = $gatewayInstance->processWebhookEvent($payload);

            // Log the result
            if ($result['success']) {
                Log::info("{$gateway} webhook event processed successfully", [
                    'event_type' => $payload['event_type'] ?? $payload['type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                    'result' => $result,
                ]);
            } else {
                Log::warning("{$gateway} webhook event processing failed", [
                    'event_type' => $payload['event_type'] ?? $payload['type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                    'error' => $result['message'] ?? 'Unknown error',
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error processing webhook event: ' . $e->getMessage(), [
                'gateway' => $gateway,
                'event_type' => $payload['event_type'] ?? $payload['type'] ?? 'unknown',
                'event_id' => $payload['id'] ?? null,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing the webhook event',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get a payment gateway instance.
     */
    protected function getGateway(string $gateway): PaymentGateway
    {
        return PaymentGatewayFactory::create($gateway);
    }

    /**
     * Check if a retry should be attempted.
     */
    protected function shouldRetry(string $operationKey): bool
    {
        $attempts = Cache::get($operationKey . '_attempts', 0);

        if ($attempts >= self::MAX_RETRY_ATTEMPTS) {
            return false;
        }

        Cache::put($operationKey . '_attempts', $attempts + 1, now()->addMinutes(30));

        return true;
    }

    /**
     * Get the current retry attempt number.
     */
    protected function getRetryAttempt(string $operationKey): int
    {
        return Cache::get($operationKey . '_attempts', 0);
    }

    /**
     * Get available payment methods.
     */
    public function getAvailablePaymentMethods(): array
    {
        return [
            'stripe' => [
                'name' => 'Credit Card (Stripe)',
                'description' => 'Pay securely with your credit card via Stripe.',
                'icon' => 'fa-credit-card',
            ],
            'paypal' => [
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account.',
                'icon' => 'fa-paypal',
            ],
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Pay via bank transfer. Your order will be processed after we receive the payment.',
                'icon' => 'fa-university',
            ],
        ];
    }
}
