<?php

namespace App\Services\Shipping;

use App\Models\Address;
use App\Models\Cart;
use App\Models\ShippingRate;

class ItemBasedCalculator implements ShippingCalculatorInterface
{
    /**
     * Calculate the shipping cost based on number of items.
     *
     * @param ShippingRate $rate
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return float
     */
    public function calculate(ShippingRate $rate, Cart $cart, ?Address $shippingAddress = null): float
    {
        $totalItems = $cart->items->sum('quantity');
        
        // Base rate + (number of items * per_item_rate)
        $cost = $rate->base_rate;
        
        if ($rate->per_item_rate) {
            $cost += $totalItems * $rate->per_item_rate;
        }
        
        return $cost;
    }
}
