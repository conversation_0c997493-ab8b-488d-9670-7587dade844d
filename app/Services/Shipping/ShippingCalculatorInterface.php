<?php

namespace App\Services\Shipping;

use App\Models\Address;
use App\Models\Cart;
use App\Models\ShippingRate;

interface ShippingCalculatorInterface
{
    /**
     * Calculate the shipping cost.
     *
     * @param ShippingRate $rate
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return float
     */
    public function calculate(ShippingRate $rate, Cart $cart, ?Address $shippingAddress = null): float;
}
