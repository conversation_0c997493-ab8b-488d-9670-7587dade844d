<?php

namespace App\Services\Shipping\Utilities;

use App\Models\Cart;
use App\Models\ProductVariant;

class WeightCalculator
{
    /**
     * Calculate the total weight of a cart.
     *
     * @param Cart $cart
     * @param string $targetUnit
     * @return float
     */
    public static function calculateCartWeight(Cart $cart, string $targetUnit = 'kg'): float
    {
        $totalWeight = 0;

        foreach ($cart->items as $item) {
            $variant = $item->productVariant;
            if ($variant && $variant->weight) {
                // Convert weight to the target unit if needed
                $weight = $variant->weight;
                if ($variant->weight_unit !== $targetUnit) {
                    $weight = self::convertWeight($weight, $variant->weight_unit, $targetUnit);
                }
                
                $totalWeight += $weight * $item->quantity;
            }
        }

        return $totalWeight;
    }

    /**
     * Convert weight from one unit to another.
     *
     * @param float $weight
     * @param string $fromUnit
     * @param string $toUnit
     * @return float
     */
    public static function convertWeight(float $weight, string $fromUnit, string $toUnit): float
    {
        if ($fromUnit === $toUnit) {
            return $weight;
        }

        // Convert to grams first (as a common base unit)
        $inGrams = match ($fromUnit) {
            'kg' => $weight * 1000,
            'g' => $weight,
            'lb' => $weight * 453.59237,
            'oz' => $weight * 28.3495231,
            default => $weight,
        };

        // Convert from grams to target unit
        return match ($toUnit) {
            'kg' => $inGrams / 1000,
            'g' => $inGrams,
            'lb' => $inGrams / 453.59237,
            'oz' => $inGrams / 28.3495231,
            default => $inGrams,
        };
    }
}
