<?php

namespace App\Services\Shipping;

use App\Models\Address;
use App\Models\Cart;
use App\Models\ShippingRate;
use App\Services\Shipping\Utilities\WeightCalculator;

class WeightBasedCalculator implements ShippingCalculatorInterface
{
    /**
     * Calculate the shipping cost based on weight.
     *
     * @param ShippingRate $rate
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return float
     */
    public function calculate(ShippingRate $rate, Cart $cart, ?Address $shippingAddress = null): float
    {
        $totalWeight = $this->calculateCartWeight($cart, $rate->weight_unit);

        // Base rate + (weight * per_weight_rate)
        $cost = $rate->base_rate;

        if ($rate->per_weight_rate) {
            $cost += $totalWeight * $rate->per_weight_rate;
        }

        return $cost;
    }

    /**
     * Calculate the total weight of a cart.
     *
     * @param Cart $cart
     * @param string $targetUnit
     * @return float
     */
    protected function calculateCartWeight(Cart $cart, string $targetUnit): float
    {
        return WeightCalculator::calculateCartWeight($cart, $targetUnit);
    }
}
