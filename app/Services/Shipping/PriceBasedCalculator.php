<?php

namespace App\Services\Shipping;

use App\Models\Address;
use App\Models\Cart;
use App\Models\ShippingRate;

class PriceBasedCalculator implements ShippingCalculatorInterface
{
    /**
     * Calculate the shipping cost based on order price.
     *
     * @param ShippingRate $rate
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return float
     */
    public function calculate(ShippingRate $rate, Cart $cart, ?Address $shippingAddress = null): float
    {
        $subtotal = $cart->subtotal;
        
        // Check if the order qualifies for free shipping
        if ($rate->method->is_free_shipping_eligible) {
            $freeShippingThreshold = $this->getFreeShippingThreshold();
            if ($freeShippingThreshold && $subtotal >= $freeShippingThreshold) {
                return 0;
            }
        }
        
        // Base calculation
        $cost = $rate->base_rate;
        
        // If there are price tiers in the conditions, apply them
        if (!empty($rate->conditions) && isset($rate->conditions['price_tiers'])) {
            foreach ($rate->conditions['price_tiers'] as $tier) {
                if ($subtotal >= $tier['min'] && (!isset($tier['max']) || $subtotal <= $tier['max'])) {
                    return $tier['rate'];
                }
            }
        }
        
        return $cost;
    }
    
    /**
     * Get the free shipping threshold from settings.
     *
     * @return float|null
     */
    protected function getFreeShippingThreshold(): ?float
    {
        $shippingSettings = \App\Models\Setting::get('shipping_settings', []);
        
        if (is_string($shippingSettings)) {
            $shippingSettings = json_decode($shippingSettings, true) ?: [];
        }
        
        if (isset($shippingSettings['freeShipping']) && $shippingSettings['freeShipping']) {
            return $shippingSettings['freeShippingAmount'] ?? null;
        }
        
        return null;
    }
}
