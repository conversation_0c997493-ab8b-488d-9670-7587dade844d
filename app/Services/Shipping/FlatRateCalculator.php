<?php

namespace App\Services\Shipping;

use App\Models\Address;
use App\Models\Cart;
use App\Models\ShippingRate;

class FlatRateCalculator implements ShippingCalculatorInterface
{
    /**
     * Calculate the shipping cost using a flat rate.
     *
     * @param ShippingRate $rate
     * @param Cart $cart
     * @param Address|null $shippingAddress
     * @return float
     */
    public function calculate(ShippingRate $rate, Cart $cart, ?Address $shippingAddress = null): float
    {
        // For flat rate, we simply return the base rate
        return $rate->base_rate;
    }
}
