<?php

namespace App\Services;

use App\Models\InventoryChangeLog;
use App\Models\InventoryItem;
use App\Models\ProductVariant;
use App\Models\StockReservation;
use App\Notifications\LowStockNotification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class InventoryService extends BaseService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        InventoryItem $inventoryItem,
        protected StockReservation $stockReservation
    ) {
        $this->model = $inventoryItem;
    }

    /**
     * Get inventory item by product variant ID.
     */
    public function getByVariantId(string $variantId, bool $lockForUpdate = false): ?InventoryItem
    {
        $query = $this->model->where('product_variant_id', $variantId);

        if ($lockForUpdate) {
            $query->lockForUpdate();
        }

        return $query->first();
    }

    /**
     * Create a new inventory item.
     */
    public function createInventoryItem(array $data): InventoryItem
    {
        $inventoryItem = $this->create($data);

        // Log the creation
        $this->logInventoryChange(
            $inventoryItem,
            'created',
            $data['quantity_on_hand'] ?? 0,
            $data['quantity_reserved'] ?? 0
        );

        return $inventoryItem;
    }

    /**
     * Update an inventory item.
     */
    public function updateInventoryItem(InventoryItem $inventoryItem, array $data): InventoryItem
    {
        $oldQuantity = $inventoryItem->quantity_on_hand;
        $oldReserved = $inventoryItem->quantity_reserved;

        $inventoryItem = DB::transaction(function () use ($inventoryItem, $data, $oldQuantity, $oldReserved) {
            $updatedItem = $this->update($inventoryItem, $data);

            // Calculate changes
            $quantityChange = ($data['quantity_on_hand'] ?? $oldQuantity) - $oldQuantity;
            $reservedChange = ($data['quantity_reserved'] ?? $oldReserved) - $oldReserved;

            // Log the update if quantities changed
            if ($quantityChange !== 0 || $reservedChange !== 0) {
                    $this->logInventoryChange(
                        $updatedItem,
                        'manual_update',
                        $quantityChange,
                        $reservedChange,
                        null, // reference_type
                        null, // reference_id
                        ['old_data' => $inventoryItem->toArray(), 'new_data' => $data] // metadata
                    );
            }

            // Check for low stock
            $this->checkLowStock($updatedItem);

            return $updatedItem;
        });

        return $inventoryItem;
    }

    /**
     * Check if a product variant is in stock.
     */
    public function isInStock(ProductVariant $variant, int $quantity = 1): bool
    {
        $inventoryItem = $this->getByVariantId($variant->id);

        if (!$inventoryItem) {
            return false;
        }

        return $inventoryItem->isInStock($quantity);
    }

    /**
     * Get available quantity for a product variant.
     */
    public function getAvailableQuantity(ProductVariant $variant): int
    {
        $inventoryItem = $this->getByVariantId($variant->id);

        if (!$inventoryItem) {
            return 0;
        }

        return $inventoryItem->available_quantity;
    }

    /**
     * Reserve stock for a product variant.
     */
    public function reserveStock(ProductVariant $variant, string $cartId, int $quantity, int $expiresInMinutes = null): ?StockReservation
    {
        // Use config value if not provided
        $expiresInMinutes = $expiresInMinutes ?? config('inventory.reservation_expiration', 15);

        // Use distributed locking to prevent race conditions
        $lockKey = "inventory_lock:{$variant->id}";

        try {
            return cache()->lock($lockKey, 10)->block(5, function () use ($variant, $cartId, $quantity, $expiresInMinutes) {
                $inventoryItem = $this->getByVariantId($variant->id, true); // Lock for update

                if (!$inventoryItem) {
                    return null;
                }

                // Check if there's enough stock
                if (!$inventoryItem->track_inventory || $inventoryItem->allow_backorder || $inventoryItem->available_quantity >= $quantity) {
                    // Begin transaction
                    return DB::transaction(function () use ($variant, $cartId, $quantity, $expiresInMinutes, $inventoryItem) {
                        // Update inventory item
                        if ($inventoryItem->track_inventory) {
                            $inventoryItem->increment('quantity_reserved', $quantity);

                            // Log the reservation
                            $this->logInventoryChange(
                                $inventoryItem,
                                'stock_reserved',
                                0,
                                $quantity,
                                'cart',
                                $cartId,
                                ['expires_at' => now()->addMinutes($expiresInMinutes)]
                            );
                        }

                        // Create stock reservation
                        $reservation = $this->stockReservation->create([
                            'product_variant_id' => $variant->id,
                            'cart_id' => $cartId,
                            'quantity' => $quantity,
                            'expires_at' => now()->addMinutes($expiresInMinutes),
                        ]);

                        // Check for low stock after reservation
                        $this->checkLowStock($inventoryItem);

                        return $reservation;
                    });
                }

                return null;
            });
        } catch (\Illuminate\Contracts\Cache\LockTimeoutException $e) {
            Log::error('Failed to acquire lock for inventory reservation', [
                'variant_id' => $variant->id,
                'cart_id' => $cartId,
                'quantity' => $quantity,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Release reserved stock.
     */
    public function releaseReservedStock(StockReservation $reservation): bool
    {
        // Use distributed locking to prevent race conditions
        $lockKey = "inventory_lock:{$reservation->product_variant_id}";

        try {
            return cache()->lock($lockKey, 10)->block(5, function () use ($reservation) {
                $inventoryItem = $this->getByVariantId($reservation->product_variant_id, true); // Lock for update

                if (!$inventoryItem) {
                    return false;
                }

                // Begin transaction
                return DB::transaction(function () use ($reservation, $inventoryItem) {
                    // Update inventory item
                    if ($inventoryItem->track_inventory) {
                        $inventoryItem->decrement('quantity_reserved', $reservation->quantity);

                        // Log the release
                        $this->logInventoryChange(
                            $inventoryItem,
                            'stock_released',
                            0,
                            -$reservation->quantity,
                            'cart',
                            $reservation->cart_id,
                            ['reservation_id' => $reservation->id]
                        );
                    }

                    // Delete stock reservation
                    return $reservation->delete();
                });
            });
        } catch (\Illuminate\Contracts\Cache\LockTimeoutException $e) {
            Log::error('Failed to acquire lock for releasing reserved stock', [
                'reservation_id' => $reservation->id,
                'product_variant_id' => $reservation->product_variant_id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Release all expired stock reservations.
     */
    public function releaseExpiredReservations(): int
    {
        $expiredReservations = $this->stockReservation->where('expires_at', '<', now())->get();
        $count = 0;

        foreach ($expiredReservations as $reservation) {
            try {
                if ($this->releaseReservedStock($reservation)) {
                    $count++;

                    // Log the expired reservation
                    Log::info('Released expired stock reservation', [
                        'reservation_id' => $reservation->id,
                        'product_variant_id' => $reservation->product_variant_id,
                        'cart_id' => $reservation->cart_id,
                        'quantity' => $reservation->quantity,
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to release expired stock reservation', [
                    'reservation_id' => $reservation->id,
                    'product_variant_id' => $reservation->product_variant_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $count;
    }

    /**
     * Adjust inventory after order is placed.
     */
    public function adjustInventoryAfterOrder(Collection $orderItems): void
    {
        foreach ($orderItems as $orderItem) {
            // Use distributed locking to prevent race conditions
            $lockKey = "inventory_lock:{$orderItem->product_variant_id}";

            try {
                cache()->lock($lockKey, 10)->block(5, function () use ($orderItem) {
                    $inventoryItem = $this->getByVariantId($orderItem->product_variant_id, true); // Lock for update

                    if ($inventoryItem && $inventoryItem->track_inventory) {
                        DB::transaction(function () use ($inventoryItem, $orderItem) {
                            // Get current values before decrementing
                            $quantityBefore = $inventoryItem->quantity_on_hand;
                            $reservedBefore = $inventoryItem->quantity_reserved;

                            // Decrement quantity on hand and reserved
                            $inventoryItem->decrement('quantity_on_hand', $orderItem->quantity);
                            $inventoryItem->decrement('quantity_reserved', $orderItem->quantity);

                            // Refresh the model to get updated values
                            $inventoryItem->refresh();

                            // Log the inventory change
                            $this->logInventoryChange(
                                $inventoryItem,
                                'order_placed',
                                -$orderItem->quantity,
                                -$orderItem->quantity,
                                'order',
                                $orderItem->order_id,
                                [
                                    'order_item_id' => $orderItem->id,
                                    'quantity_before' => $quantityBefore,
                                    'reserved_before' => $reservedBefore,
                                ]
                            );

                            // Check for low stock after order
                            $this->checkLowStock($inventoryItem);
                        });
                    }
                });
            } catch (\Illuminate\Contracts\Cache\LockTimeoutException $e) {
                Log::error('Failed to acquire lock for inventory adjustment after order', [
                    'order_item_id' => $orderItem->id,
                    'product_variant_id' => $orderItem->product_variant_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Verify inventory availability for an existing order.
     *
     * @param \App\Models\Order $order The order to verify inventory for
     * @return array ['success' => bool, 'message' => string, 'unavailable_items' => array]
     */
    public function verifyInventoryForExistingOrder(\App\Models\Order $order): array
    {
        $unavailableItems = [];

        foreach ($order->items as $orderItem) {
            $variant = $orderItem->productVariant;

            if (!$variant) {
                $unavailableItems[] = "Item #{$orderItem->id} (product no longer exists)";
                continue;
            }

            $inventoryItem = $this->getByVariantId($variant->id);

            if (!$inventoryItem) {
                $unavailableItems[] = "{$variant->name} (inventory record not found)";
                continue;
            }

            if ($inventoryItem->track_inventory) {
                // Check if there's enough stock available
                if ($inventoryItem->quantity_on_hand < $orderItem->quantity) {
                    $unavailableItems[] = "{$variant->name} (requested: {$orderItem->quantity}, available: {$inventoryItem->quantity_on_hand})";
                }
            }
        }

        if (!empty($unavailableItems)) {
            $message = 'We\'re sorry, but the following items in your order are no longer available in the requested quantity: ' .
                implode(', ', $unavailableItems);

            return [
                'success' => false,
                'message' => $message,
                'unavailable_items' => $unavailableItems
            ];
        }

        return [
            'success' => true,
            'message' => 'All items are available',
            'unavailable_items' => []
        ];
    }
    /**
     * Restore inventory after order cancellation.
     */
    public function restoreInventoryAfterOrderCancellation(Collection $orderItems): void
    {
        foreach ($orderItems as $orderItem) {
            // Use distributed locking to prevent race conditions
            $lockKey = "inventory_lock:{$orderItem->product_variant_id}";

            try {
                cache()->lock($lockKey, 10)->block(5, function () use ($orderItem) {
                    $inventoryItem = $this->getByVariantId($orderItem->product_variant_id, true); // Lock for update

                    if ($inventoryItem && $inventoryItem->track_inventory) {
                        DB::transaction(function () use ($inventoryItem, $orderItem) {
                            // Get current values before incrementing
                            $quantityBefore = $inventoryItem->quantity_on_hand;
                            $reservedBefore = $inventoryItem->quantity_reserved; // Get reserved quantity before

                            // Increment quantity on hand and reserved
                            $inventoryItem->increment('quantity_on_hand', $orderItem->quantity);
                            $inventoryItem->increment('quantity_reserved', $orderItem->quantity);

                            // Refresh the model to get updated values
                            $inventoryItem->refresh();

                            // Log the inventory change
                            $this->logInventoryChange(
                                $inventoryItem,
                                'order_cancelled',
                                $orderItem->quantity, // quantity_change
                                $orderItem->quantity, // reserved_change
                                'order',
                                $orderItem->order_id,
                                [
                                    'order_item_id' => $orderItem->id,
                                    'quantity_before' => $quantityBefore,
                                    'reserved_before' => $reservedBefore, // Add reserved_before to log
                                ]
                            );
                        });
                    }
                });
            } catch (\Illuminate\Contracts\Cache\LockTimeoutException $e) {
                Log::error('Failed to acquire lock for inventory restoration after order cancellation', [
                    'order_item_id' => $orderItem->id,
                    'product_variant_id' => $orderItem->product_variant_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Check for low stock and send notifications if needed.
     */
    public function checkLowStock(InventoryItem $inventoryItem): void
    {
        if (!$inventoryItem->track_inventory) {
            return;
        }

        // Check if inventory is low and notification hasn't been sent
        if ($inventoryItem->isLowStock() && !$inventoryItem->low_stock_notified && config('inventory.send_low_stock_notifications', true)) {
            // Send notification
            $adminEmail = config('inventory.admin_email');
            if ($adminEmail) {
                Notification::route('mail', $adminEmail)
                    ->notify(new LowStockNotification($inventoryItem));
            }

            // Mark as notified
            $inventoryItem->update(['low_stock_notified' => true]);

            // Log the notification
            Log::info('Low stock notification sent', [
                'inventory_item_id' => $inventoryItem->id,
                'product_variant_id' => $inventoryItem->product_variant_id,
                'quantity_on_hand' => $inventoryItem->quantity_on_hand,
                'available_quantity' => $inventoryItem->available_quantity,
                'low_stock_threshold' => $inventoryItem->low_stock_threshold ?? config('inventory.low_stock_threshold', 5),
            ]);
        }

        // Reset notification flag if stock is back above threshold
        if (!$inventoryItem->isLowStock() && $inventoryItem->low_stock_notified) {
            $inventoryItem->update(['low_stock_notified' => false]);
        }
    }

    /**
     * Check all inventory items for low stock.
     */
    public function checkAllLowStockLevels(): int
    {
        $lowStockThreshold = config('inventory.low_stock_threshold', 5);

        $lowStockItems = $this->model->where('track_inventory', true)
            ->where(function ($query) use ($lowStockThreshold) {
                $query->where('low_stock_threshold', '>=', DB::raw('quantity_on_hand'))
                    ->orWhere(function ($query) use ($lowStockThreshold) {
                        $query->whereNull('low_stock_threshold')
                            ->where('quantity_on_hand', '<=', $lowStockThreshold);
                    });
            })
            ->where('low_stock_notified', false)
            ->get();

        $count = 0;
        foreach ($lowStockItems as $item) {
            $this->checkLowStock($item);
            $count++;
        }

        return $count;
    }

    /**
     * Log an inventory change.
     */
    protected function logInventoryChange(
        InventoryItem $inventoryItem,
        string $reason,
        int $quantityChange,
        int $reservedChange = 0,
        ?string $referenceType = null,
        ?string $referenceId = null,
        ?array $metadata = null
    ): InventoryChangeLog {
        return InventoryChangeLog::create([
            'inventory_item_id' => $inventoryItem->id,
            'product_variant_id' => $inventoryItem->product_variant_id,
            'quantity_change' => $quantityChange,
            'reserved_change' => $reservedChange,
            'quantity_after' => $inventoryItem->quantity_on_hand,
            'reserved_after' => $inventoryItem->quantity_reserved,
            'reason' => $reason,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'user_id' => Auth::id(),
            'metadata' => $metadata,
        ]);
    }
}
