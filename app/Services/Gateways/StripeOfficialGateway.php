<?php

namespace App\Services\Gateways;

use App\Contracts\PaymentGateway;
use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway as PaymentGatewayModel;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod as StripePaymentMethod;
use Stripe\SetupIntent;
use Stripe\Stripe;
use Stripe\StripeClient;
use Stripe\Customer;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;

class StripeOfficialGateway implements PaymentGateway
{
    /**
     * The Stripe client instance.
     */
    protected StripeClient $stripeClient;

    /**
     * Create a new Stripe gateway instance.
     */
    public function __construct()
    {
        // Set the Stripe API key
        $apiKey = config('payment-gateways.stripe.secret');

        if (empty($apiKey)) {
            throw new \Exception('Stripe API key is not configured');
        }

        // Initialize Stripe client directly as in the reference implementation
        $this->stripeClient = new StripeClient($apiKey);
    }

    /**
     * Create a customer in Stripe.
     */
    public function createCustomer(array $data): array
    {
        try {
            $customerData = [
                'email' => $data['email'] ?? null,
                'name' => $data['name'] ?? null,
                'phone' => $data['phone'] ?? null,
                'metadata' => [
                    'user_id' => $data['user_id'] ?? null,
                ],
            ];

            // Create the customer in Stripe
            $customer = Customer::create($customerData);

            // Create or update the payment gateway record
            $paymentGateway = PaymentGatewayModel::updateOrCreate(
                [
                    'user_id' => $data['user_id'],
                    'gateway' => 'stripe',
                ],
                [
                    'customer_id' => $customer->id,
                    'data' => [
                        'customer' => $customer->toArray(),
                    ],
                ]
            );

            return [
                'success' => true,
                'customer_id' => $customer->id,
                'payment_gateway_id' => $paymentGateway->id,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error creating Stripe customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the customer',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Attach a payment method to a customer.
     */
    public function attachPaymentMethodToCustomer(string $paymentMethodId, array $data): array
    {
        try {
            // Get the customer ID
            $customerId = $data['customer_id'] ?? null;

            if (!$customerId) {
                // Try to get the customer ID from the payment gateway
                $paymentGateway = PaymentGatewayModel::where('user_id', $data['user_id'])
                    ->where('gateway', 'stripe')
                    ->first();

                if ($paymentGateway) {
                    $customerId = $paymentGateway->customer_id;
                }
            }

            if (!$customerId) {
                // Create a new customer
                $customerResult = $this->createCustomer($data);
                if (!$customerResult['success']) {
                    return $customerResult;
                }
                $customerId = $customerResult['customer_id'];
            }

            // Retrieve the payment method
            $paymentMethod = StripePaymentMethod::retrieve($paymentMethodId);

            // Attach the payment method to the customer
            $paymentMethod->attach(['customer' => $customerId]);

            // If this is the default card, set it as the default payment method
            if (isset($data['default_card']) && $data['default_card']) {
                Customer::update($customerId, [
                    'invoice_settings' => [
                        'default_payment_method' => $paymentMethodId,
                    ],
                ]);
            }

            // Get payment method details
            $card = $paymentMethod->card;

            // Create a payment method record
            $paymentMethodRecord = PaymentMethod::create([
                'method_key' => $paymentMethodId,
                'payment_gateway_id' => $data['payment_gateway_id'],
                'default_card' => $data['default_card'] ?? false,
                'fingerprint' => $card->fingerprint,
                'owner_name' => $paymentMethod->billing_details->name,
                'network' => $card->brand,
                'type' => $paymentMethod->type,
                'last4' => $card->last4,
                'expires' => $card->exp_month . '/' . $card->exp_year,
                'origin' => $data['origin'] ?? 'added',
                'verification_check' => $card->checks->cvc_check,
            ]);

            return [
                'success' => true,
                'payment_method_id' => $paymentMethodId,
                'payment_method' => $paymentMethodRecord,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error attaching payment method to customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_method_id' => $paymentMethodId,
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while attaching the payment method',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Detach a payment method from a customer.
     */
    public function detachPaymentMethodFromCustomer(string $paymentMethodId): array
    {
        try {
            // Retrieve the payment method
            $paymentMethod = StripePaymentMethod::retrieve($paymentMethodId);

            // Detach the payment method from the customer
            $paymentMethod->detach();

            // Delete the payment method record
            $paymentMethodRecord = PaymentMethod::where('method_key', $paymentMethodId)->first();
            if ($paymentMethodRecord) {
                $paymentMethodRecord->delete();
            }

            return [
                'success' => true,
                'message' => 'Payment method detached successfully',
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error detaching payment method from customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_method_id' => $paymentMethodId,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while detaching the payment method',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a payment intent.
     *
     * This method follows the reference implementation to create a Stripe payment intent.
     */
    public function createPaymentIntent(Order $order, array $data = []): array
    {
        try {
            // Get the customer ID if available
            $customerId = $data['customer_id'] ?? null;

            if (!$customerId && isset($data['user_id'])) {
                // Try to get the customer ID from the payment gateway
                $paymentGateway = PaymentGatewayModel::where('user_id', $data['user_id'])
                    ->where('gateway', 'stripe')
                    ->first();

                if ($paymentGateway) {
                    $customerId = $paymentGateway->customer_id;
                }
            }

            // Prepare payment intent data following the reference implementation
            $intentArray = [
                'amount' => round($order->total, 2) * 100, // Stripe expects amount in cents
                'currency' => strtolower($order->currency),
                'description' => "Order #{$order->tracking_number}",
                // Use automatic payment methods as in the reference implementation
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'metadata' => [
                    'order_tracking_number' => $order->tracking_number,
                ],
            ];

            // Add customer if available
            if ($customerId) {
                $intentArray['customer'] = $customerId;
            }

            // Create the payment intent using the Stripe client
            $paymentIntent = $this->stripeClient->paymentIntents->create($intentArray);

            // Create a payment record
            $payment = Payment::create([
                'order_id' => $order->id,
                'payment_method' => 'stripe',
                'amount' => $order->total,
                'currency' => $order->currency,
                'status' => 'pending',
                'gateway_payment_id' => $paymentIntent->id,
                'metadata' => [
                    'stripe_payment_intent' => $paymentIntent->toArray(),
                ],
            ]);

            return [
                'success' => true,
                'payment_id' => $paymentIntent->id,
                'client_secret' => $paymentIntent->client_secret,
                'payment' => $payment,
                'is_redirect' => false,
            ];
        } catch (\Stripe\Exception\CardException $e) {
            Log::error('Invalid card', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'Invalid card',
                'error' => $e->getMessage(),
            ];
        } catch (\Stripe\Exception\RateLimitException $e) {
            Log::error('Too many requests', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'Too many requests',
                'error' => $e->getMessage(),
            ];
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            Log::error('Invalid request', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'Invalid request',
                'error' => $e->getMessage(),
            ];
        } catch (\Stripe\Exception\AuthenticationException $e) {
            Log::error('Authentication failed', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'Authentication failed',
                'error' => $e->getMessage(),
            ];
        } catch (\Stripe\Exception\ApiConnectionException $e) {
            Log::error('API connection failed', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'API connection failed',
                'error' => $e->getMessage(),
            ];
        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('API error', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'API error',
                'error' => $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::error('Something went wrong with payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $order->id,
            ]);
            return [
                'success' => false,
                'message' => 'Something went wrong with payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Execute a payment.
     */
    public function executePayment(string $paymentId, array $data = []): array
    {
        try {
            // Find the payment by payment intent ID
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'Payment not found',
                ];
            }

            // Check if payment is already processed
            if ($payment->status === 'completed') {
                return [
                    'success' => true,
                    'message' => 'Payment already processed',
                    'payment' => $payment,
                ];
            }

            // Retrieve the payment intent
            $paymentIntent = PaymentIntent::retrieve($paymentId);

            // If the payment intent requires confirmation, confirm it
            if ($paymentIntent->status === 'requires_confirmation') {
                $paymentIntent = $this->stripeClient->paymentIntents->confirm($paymentId, [
                    'payment_method' => $data['payment_method_id'] ?? $paymentIntent->payment_method,
                ]);
            }

            // If the payment intent requires action, return the client secret
            if ($paymentIntent->status === 'requires_action') {
                return [
                    'success' => true,
                    'requires_action' => true,
                    'client_secret' => $paymentIntent->client_secret,
                    'payment_intent_id' => $paymentId,
                    'payment' => $payment,
                ];
            }

            // If the payment intent is succeeded, update the payment status
            if ($paymentIntent->status === 'succeeded') {
                // Get the charge ID
                $chargeId = $paymentIntent->charges->data[0]->id ?? null;

                // Update payment status
                $payment->update([
                    'status' => 'completed',
                    'gateway_transaction_id' => $chargeId,
                    'processed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'stripe_payment_details' => $paymentIntent->toArray(),
                    ]),
                ]);

                // Update order status if needed
                $order = $payment->order;
                if ($order && $order->status === 'pending') {
                    $order->update(['status' => 'processing']);
                }

                return [
                    'success' => true,
                    'message' => 'Payment executed successfully',
                    'payment' => $payment,
                    'payment_intent' => $paymentIntent->toArray(),
                ];
            }

            // For other statuses, return the payment intent status
            return [
                'success' => false,
                'message' => 'Payment not completed',
                'status' => $paymentIntent->status,
                'payment_intent' => $paymentIntent->toArray(),
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error executing Stripe payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_id' => $paymentId,
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while executing the payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment.
     *
     * This method retrieves the status of a Stripe Payment Intent.
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            // Find the payment by payment intent ID
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'Payment not found',
                ];
            }

            // Check if payment is already verified locally
            if ($payment->status === 'completed') {
                return [
                    'success' => true,
                    'message' => 'Payment already verified',
                    'payment' => $payment,
                    'status' => 'completed',
                ];
            }

            // Retrieve the Payment Intent
            $paymentIntent = $this->stripeClient->paymentIntents->retrieve($paymentId);

            // Map Stripe Payment Intent status to our status
            $status = match ($paymentIntent->status) {
                'succeeded' => 'completed',
                'processing' => 'pending',
                'requires_payment_method' => 'failed',
                'requires_confirmation' => 'pending',
                'requires_action' => 'pending',
                'canceled' => 'cancelled',
                default => 'failed',
            };

            // Update payment status if it has changed based on Stripe's status
            if ($payment->status !== $status) {
                 $payment->update([
                     'status' => $status,
                     'gateway_transaction_id' => $paymentIntent->latest_charge ?? null, // Store the latest charge ID
                     'processed_at' => $status === 'completed' ? now() : null,
                     'metadata' => array_merge($payment->metadata ?? [], [
                         'stripe_verification_result' => $paymentIntent->toArray(),
                     ]),
                 ]);

                 // Update order status if needed
                 $order = $payment->order;
                 if ($order && $status === 'completed' && $order->status === 'pending') {
                     $order->update(['status' => 'processing']);
                 }
            }

            return [
                'success' => true,
                'message' => 'Payment verified',
                'payment' => $payment,
                'status' => $status,
                'verification_result' => $paymentIntent->toArray(),
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error verifying Stripe payment intent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_id' => $paymentId,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while verifying the payment intent',
                'error' => $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::error('Something went wrong with Stripe payment verification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_id' => $paymentId,
            ]);
            return [
                'success' => false,
                'message' => 'Something went wrong with payment verification',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(Payment $payment, ?float $amount = null, ?string $reason = null): array
    {
        try {
            // Check if payment is completed
            if ($payment->status !== 'completed') {
                return [
                    'success' => false,
                    'message' => 'Payment is not completed and cannot be refunded',
                ];
            }

            // Check if we have a transaction ID
            if (!$payment->gateway_transaction_id) {
                return [
                    'success' => false,
                    'message' => 'No transaction ID found for this payment',
                ];
            }

            // Prepare refund data
            $refundData = [
                'charge' => $payment->gateway_transaction_id,
                'reason' => $reason ?? 'requested_by_customer',
            ];

            // Add amount if specified
            if ($amount !== null) {
                $refundData['amount'] = (int) ($amount * 100); // Stripe expects amount in cents
            }

            // Create the refund
            $refund = $this->stripeClient->refunds->create($refundData);

            // Update payment with refund details
            $payment->update([
                'status' => 'refunded',
                'metadata' => array_merge($payment->metadata ?? [], [
                    'refund_result' => $refund->toArray(),
                    'refund_amount' => $amount ?? $payment->amount,
                    'refund_reason' => $reason,
                ]),
            ]);

            // Update order status if needed
            $order = $payment->order;
            if ($order) {
                $order->update(['status' => 'refunded']);
            }

            return [
                'success' => true,
                'message' => 'Payment refunded successfully',
                'payment' => $payment,
                'refund_result' => $refund->toArray(),
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error refunding Stripe payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_id' => $payment->id,
                'transaction_id' => $payment->gateway_transaction_id,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while refunding the payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a setup intent for saving a payment method.
     */
    public function createSetupIntent(array $data = []): array
    {
        try {
            // Get the customer ID if available
            $customerId = $data['customer_id'] ?? null;

            if (!$customerId && isset($data['user_id'])) {
                // Try to get the customer ID from the payment gateway
                $paymentGateway = PaymentGatewayModel::where('user_id', $data['user_id'])
                    ->where('gateway', 'stripe')
                    ->first();

                if ($paymentGateway) {
                    $customerId = $paymentGateway->customer_id;
                }
            }

            // If no customer ID is found, create a new customer
            if (!$customerId && isset($data['user_id'])) {
                $customerResult = $this->createCustomer($data);
                if (!$customerResult['success']) {
                    return $customerResult;
                }
                $customerId = $customerResult['customer_id'];
            }

            // Prepare setup intent data
            $setupIntentData = [
                'payment_method_types' => ['card'],
                'usage' => 'off_session',
            ];

            // Add customer if available
            if ($customerId) {
                $setupIntentData['customer'] = $customerId;
            }

            // Create the setup intent
            $setupIntent = SetupIntent::create($setupIntentData);

            return [
                'success' => true,
                'intent_id' => $setupIntent->id,
                'client_secret' => $setupIntent->client_secret,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Error creating Stripe setup intent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the setup intent',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate a webhook payload.
     *
     * This method follows the reference implementation to validate a Stripe webhook.
     */
    public function validateWebhook(array $payload, string $signature, string $signatureHeader): bool
    {
        try {
            $webhookSecret = config('payment-gateways.stripe.webhook_secret');

            if (empty($webhookSecret)) {
                Log::warning('Stripe webhook secret is not configured');
                return false;
            }

            // Get the raw payload as in the reference implementation
            $rawPayload = @file_get_contents('php://input');
            if (!$rawPayload) {
                // If we can't get the raw payload, try to encode the array
                $rawPayload = json_encode($payload);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('Error encoding payload for Stripe webhook verification', [
                        'json_error' => json_last_error_msg(),
                    ]);
                    return false;
                }
            }

            // Verify the webhook signature using the Webhook::constructEvent method
            Webhook::constructEvent($rawPayload, $signature, $webhookSecret);
            return true;
        } catch (SignatureVerificationException $e) {
            Log::warning('Invalid Stripe webhook signature', [
                'error' => $e->getMessage(),
            ]);
            return false;
        } catch (\Exception $e) {
            Log::error('Error validating Stripe webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Process a webhook event.
     */
    public function processWebhookEvent(array $payload): array
    {
        try {
            $eventType = $payload['type'] ?? null;
            $eventData = $payload['data']['object'] ?? [];

            if (!$eventType) {
                return [
                    'success' => false,
                    'message' => 'Invalid webhook payload',
                ];
            }

            Log::info('Processing Stripe webhook event', [
                'event_type' => $eventType,
            ]);

            switch ($eventType) {
                case 'charge.succeeded':
                    return $this->handleChargeSucceeded($eventData);

                case 'charge.pending':
                    return $this->handleChargePending($eventData);

                case 'charge.failed':
                    return $this->handleChargeFailed($eventData);

                case 'charge.refunded':
                    return $this->handleChargeRefunded($eventData);

                default:
                    return [
                        'success' => true,
                        'message' => 'Event acknowledged but not processed',
                        'event_type' => $eventType,
                    ];
            }
        } catch (\Exception $e) {
            Log::error('Error processing Stripe webhook event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing the webhook event',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle charge succeeded event.
     */
    protected function handleChargeSucceeded(array $charge): array
    {
        $chargeId = $charge['id'] ?? null;
        $paymentIntentId = $charge['payment_intent'] ?? null;

        if (!$chargeId || !$paymentIntentId) {
            return [
                'success' => false,
                'message' => 'Invalid charge data',
            ];
        }

        // Find the payment by payment intent ID
        $payment = Payment::where('gateway_payment_id', $paymentIntentId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Check if payment is already processed
        if ($payment->status === 'completed') {
            return [
                'success' => true,
                'message' => 'Payment already processed',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'completed',
            'gateway_transaction_id' => $chargeId,
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'stripe_charge_details' => $charge,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order && $order->status === 'pending') {
            $order->update(['status' => 'processing']);
        }

        return [
            'success' => true,
            'message' => 'Payment processed successfully',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }

    /**
     * Handle charge pending event.
     */
    protected function handleChargePending(array $charge): array
    {
        $chargeId = $charge['id'] ?? null;
        $paymentIntentId = $charge['payment_intent'] ?? null;

        if (!$chargeId || !$paymentIntentId) {
            return [
                'success' => false,
                'message' => 'Invalid charge data',
            ];
        }

        // Find the payment by payment intent ID
        $payment = Payment::where('gateway_payment_id', $paymentIntentId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'pending',
            'gateway_transaction_id' => $chargeId,
            'metadata' => array_merge($payment->metadata ?? [], [
                'stripe_charge_details' => $charge,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'pending']);
        }

        return [
            'success' => true,
            'message' => 'Payment pending',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }

    /**
     * Handle charge failed event.
     */
    protected function handleChargeFailed(array $charge): array
    {
        $chargeId = $charge['id'] ?? null;
        $paymentIntentId = $charge['payment_intent'] ?? null;
        $failureMessage = $charge['failure_message'] ?? 'Unknown error';

        if (!$chargeId || !$paymentIntentId) {
            return [
                'success' => false,
                'message' => 'Invalid charge data',
            ];
        }

        // Find the payment by payment intent ID
        $payment = Payment::where('gateway_payment_id', $paymentIntentId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'failed',
            'gateway_transaction_id' => $chargeId,
            'metadata' => array_merge($payment->metadata ?? [], [
                'failure_reason' => $failureMessage,
                'stripe_charge_details' => $charge,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'pending']);
        }

        return [
            'success' => true,
            'message' => 'Payment failure processed',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }

    /**
     * Handle charge refunded event.
     */
    protected function handleChargeRefunded(array $charge): array
    {
        $chargeId = $charge['id'] ?? null;
        $paymentIntentId = $charge['payment_intent'] ?? null;

        if (!$chargeId) {
            return [
                'success' => false,
                'message' => 'Invalid charge data',
            ];
        }

        // Find the payment by charge ID or payment intent ID
        $payment = Payment::where('gateway_transaction_id', $chargeId)->first();

        if (!$payment && $paymentIntentId) {
            $payment = Payment::where('gateway_payment_id', $paymentIntentId)->first();
        }

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Check if refund is already processed
        if ($payment->status === 'refunded') {
            return [
                'success' => true,
                'message' => 'Refund already processed',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'refunded',
            'metadata' => array_merge($payment->metadata ?? [], [
                'refund_amount' => $charge['amount_refunded'] / 100,
                'refund_currency' => $charge['currency'],
                'stripe_refund_details' => $charge,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'refunded']);
        }

        return [
            'success' => true,
            'message' => 'Refund processed successfully',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }
}
