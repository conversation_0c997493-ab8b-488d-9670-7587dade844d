<?php

namespace App\Services\Gateways;

use App\Contracts\PaymentGateway;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class SrmklivePayPalGateway implements PaymentGateway
{
    /**
     * The PayPal client instance.
     */
    protected PayPalClient $paypalClient;

    /**
     * Create a new PayPal gateway instance.
     */
    public function __construct()
    {
        try {
            // Initialize PayPal client directly with config
            $this->paypalClient = new PayPalClient(config('paypal'));
            $token = $this->paypalClient->getAccessToken();
            $this->paypalClient->setAccessToken($token);
        } catch (\Exception $e) {
            Log::error('Failed to initialize PayPal client', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Create a payment intent.
     */
    public function createPaymentIntent(Order $order, array $data = []): array
    {
        try {
            // Define the return URL. This is where PayPal redirects the user after payment approval.
            // It's typically provided by the calling context (e.g., CheckoutController via PaymentService).
            // If not provided, a default route is used. PayPal will append 'token' (PayPal Order ID) and 'PayerID' to this URL.
            $returnUrl = $data['return_url'] ?? route('checkout.paypal.callback');

            // Define the cancel URL. This is where PayPal redirects the user if they cancel the payment.
            $cancelUrl = $data['cancel_url'] ?? route('checkout.paypal.cancel', ['order_id' => $order->id]);
            // Set a unique request ID to prevent duplicate orders
            $this->paypalClient->setRequestHeader("PayPal-Request-Id", \Illuminate\Support\Str::uuid());

            // Create order data for PayPal following the reference implementation
            $orderData = [
                "intent" => "CAPTURE",
                "purchase_units" => [
                    [
                        "invoice_id" => $order->tracking_number ?? $order->id,
                        "amount" => [
                            "currency_code" => strtoupper($order->currency),
                            "value" => round($order->total, 2),
                        ],
                        'description' => "Order From " . config('app.name'),
                    ]
                ],
                "payment_source" => [
                    "paypal" => [
                        "experience_context" => [
                            'user_action' => 'PAY_NOW',
                            'payment_method_preference' => 'IMMEDIATE_PAYMENT_REQUIRED',
                            'cancel_url' => $cancelUrl,
                            'return_url' => $returnUrl
                        ]
                    ]
                ],
            ];

            // Create the PayPal order
            $response = $this->paypalClient->createOrder($orderData);

            if (!isset($response['id'])) {
                Log::error('Failed to create PayPal order', [
                    'order_id' => $order->id,
                    'response' => $response,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create PayPal order',
                    'error' => $response['error'] ?? 'Unknown error',
                ];
            }

            // Find the appropriate redirect link ('approve' or 'payer-action')
            $redirectLink = null;
            if (isset($response['links']) && is_array($response['links'])) {
                foreach ($response['links'] as $link) {
                    if (isset($link['rel']) && ($link['rel'] === 'approve' || $link['rel'] === 'payer-action') && isset($link['href'])) {
                        $redirectLink = $link['href'];
                        break;
                    }
                }
            }

            if (!$redirectLink) {
                Log::error('No redirect link (approve or payer-action) found in PayPal response', [
                    'order_id' => $order->id,
                    'response' => $response,
                ]);

                return [
                    'success' => false,
                    'message' => 'No redirect link found in PayPal response',
                    'response' => $response, // Include response for debugging
                ];
            }

            // Create a payment record
            $payment = Payment::create([
                'order_id' => $order->id,
                'payment_method' => 'paypal',
                'amount' => $order->total,
                'currency' => $order->currency,
                'status' => 'pending',
                'gateway_payment_id' => $response['id'],
                'metadata' => [
                    'paypal_order' => $response,
                ],
            ]);

            return [
                'success' => true,
                'payment_id' => $response['id'],
                'redirect_url' => $redirectLink, // Use the found redirect link
                'payment' => $payment,
                'is_redirect' => true,
                'response' => $response, // Include response for debugging
            ];
        } catch (\Exception $e) {
            Log::error('Error creating PayPal payment intent', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the PayPal payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Execute a payment.
     *
     * This method captures a PayPal payment after approval.
     * It uses the srmklive/paypal package's capturePaymentOrder method.
     */
    public function executePayment(string $paymentId, array $data = []): array
    {
        try {
            // Find the payment by PayPal order ID
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'Payment not found',
                ];
            }

            // Check if payment is already processed
            if ($payment->status === 'completed') {
                return [
                    'success' => true,
                    'message' => 'Payment already processed',
                    'payment' => $payment,
                ];
            }

            // First, check the order status to ensure it's in a capturable state
            $orderDetails = $this->paypalClient->showOrderDetails($paymentId);

            if (!isset($orderDetails['status'])) {
                Log::error('Failed to retrieve PayPal order details', [
                    'payment_id' => $paymentId,
                    'response' => $orderDetails,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to retrieve PayPal order details',
                    'error' => $orderDetails['error'] ?? 'Unknown error',
                ];
            }

            // If the order is already completed, we need to get the capture ID
            if ($orderDetails['status'] === 'COMPLETED') {
                Log::info('PayPal order already captured', [
                    'payment_id' => $paymentId,
                    'order_status' => $orderDetails['status'],
                ]);

                // Extract capture ID from the completed order
                $captureId = $orderDetails['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;

                if ($captureId) {
                    // Update payment with capture details
                    $payment->update([
                        'status' => 'completed',
                        'gateway_transaction_id' => $captureId,
                        'processed_at' => now(),
                        'metadata' => array_merge($payment->metadata ?? [], [
                            'paypal_capture_result' => $orderDetails,
                            'recovery_method' => 'order_status_check',
                        ]),
                    ]);

                    // Update order status if needed
                    $order = $payment->order;
                    if ($order && $order->status === 'pending') {
                        $order->update(['status' => 'processing']);
                    }

                    return [
                        'success' => true,
                        'message' => 'Payment already captured',
                        'payment' => $payment,
                        'capture_result' => $orderDetails,
                    ];
                }
            }

            // If the order is not in an APPROVED state, it cannot be captured
            if ($orderDetails['status'] !== 'APPROVED') {
                Log::warning('PayPal order not in capturable state', [
                    'payment_id' => $paymentId,
                    'order_status' => $orderDetails['status'],
                ]);

                return [
                    'success' => false,
                    'message' => "PayPal order is in {$orderDetails['status']} state and cannot be captured",
                    'order_status' => $orderDetails['status'],
                ];
            }

            // Capture the PayPal order using the package's method
            $response = $this->paypalClient->capturePaymentOrder($paymentId);

            if (!isset($response['status']) || $response['status'] !== 'COMPLETED') {
                Log::error('Failed to capture PayPal payment', [
                    'payment_id' => $paymentId,
                    'response' => $response,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to capture PayPal payment',
                    'error' => $response['error'] ?? 'Unknown error',
                ];
            }

            // Get the capture ID
            $captureId = $response['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;

            // Update payment with capture details
            $payment->update([
                'status' => 'completed',
                'gateway_transaction_id' => $captureId,
                'processed_at' => now(),
                'metadata' => array_merge($payment->metadata ?? [], [
                    'paypal_capture_result' => $response,
                ]),
            ]);

            // Update order status if needed
            $order = $payment->order;
            if ($order && $order->status === 'pending') {
                $order->update(['status' => 'processing']);
            }

            return [
                'success' => true,
                'message' => 'Payment executed successfully',
                'payment' => $payment,
                'capture_result' => $response,
            ];
        } catch (\Exception $e) {
            Log::error('Error executing PayPal payment', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Check if this is a connection error
            $isConnectionError = (
                strpos($e->getMessage(), 'cURL error') !== false ||
                strpos($e->getMessage(), 'Connection') !== false ||
                strpos($e->getMessage(), 'timeout') !== false
            );

            // For connection errors, we should check if the payment was actually processed
            if ($isConnectionError && isset($payment)) {
                try {
                    // Check the order status using the package's method
                    $orderResponse = $this->paypalClient->showOrderDetails($paymentId);

                    if (isset($orderResponse['status']) && $orderResponse['status'] === 'COMPLETED') {
                        // The payment was actually completed
                        $captureId = $orderResponse['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;

                        // Update payment with capture details
                        $payment->update([
                            'status' => 'completed',
                            'gateway_transaction_id' => $captureId,
                            'processed_at' => now(),
                            'metadata' => array_merge($payment->metadata ?? [], [
                                'paypal_capture_result' => $orderResponse,
                                'recovery_method' => 'connection_error_verification',
                            ]),
                        ]);

                        // Update order status if needed
                        $order = $payment->order;
                        if ($order && $order->status === 'pending') {
                            $order->update(['status' => 'processing']);
                        }

                        return [
                            'success' => true,
                            'message' => 'Payment verified after connection error',
                            'payment' => $payment,
                            'capture_result' => $orderResponse,
                        ];
                    }
                } catch (\Exception $verifyException) {
                    Log::error('Error verifying PayPal payment after connection error', [
                        'payment_id' => $paymentId,
                        'error' => $verifyException->getMessage(),
                        'trace' => $verifyException->getTraceAsString(),
                    ]);
                }
            }

            return [
                'success' => false,
                'message' => 'An error occurred while executing the PayPal payment',
                'error' => $e->getMessage(),
                'is_connection_error' => $isConnectionError ?? false,
            ];
        }
    }

    /**
     * Verify a payment.
     *
     * This method retrieves the status of a PayPal order.
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            // Find the payment by PayPal order ID
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'Payment not found',
                ];
            }

            // Check if payment is already verified locally
            if ($payment->status === 'completed') {
                return [
                    'success' => true,
                    'message' => 'Payment already verified',
                    'payment' => $payment,
                    'status' => 'completed',
                ];
            }

            // Retrieve the PayPal order details
            $result = $this->paypalClient->showOrderDetails($paymentId);

            if (!isset($result['status'])) {
                Log::error('Failed to retrieve PayPal order details for verification', [
                    'payment_id' => $paymentId,
                    'response' => $result,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to retrieve PayPal order details',
                    'error' => $result['error'] ?? 'Unknown error',
                ];
            }

            // Map PayPal status to our status
            $status = match ($result['status']) {
                'COMPLETED' => 'completed',
                'APPROVED' => 'pending', // Approved but not captured
                'SAVED' => 'pending',
                'PAYER_ACTION_REQUIRED' => 'pending',
                'CREATED' => 'pending',
                'VOIDED' => 'cancelled',
                default => 'failed',
            };

            // Update payment status if it has changed based on PayPal's status
            if ($payment->status !== $status) {
                 $payment->update([
                     'status' => $status,
                     'metadata' => array_merge($payment->metadata ?? [], [
                         'paypal_verification_result' => $result,
                     ]),
                 ]);

                 // Update order status if needed
                 $order = $payment->order;
                 if ($order && $status === 'completed' && $order->status === 'pending') {
                     $order->update(['status' => 'processing']);
                 }
            }


            return [
                'success' => true,
                'message' => 'Payment verified',
                'payment' => $payment,
                'status' => $status,
                'verification_result' => $result,
            ];
        } catch (\Exception $e) {
            Log::error('Error verifying PayPal payment', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while verifying the PayPal payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund a payment.
     *
     * This method refunds a PayPal payment using the srmklive/paypal package's
     * refundCapturedPayment method.
     */
    public function refundPayment(Payment $payment, ?float $amount = null, ?string $reason = null): array
    {
        try {
            // Check if payment is completed
            if ($payment->status !== 'completed') {
                return [
                    'success' => false,
                    'message' => 'Payment is not completed and cannot be refunded',
                ];
            }

            // Check if we have a transaction ID
            if (!$payment->gateway_transaction_id) {
                return [
                    'success' => false,
                    'message' => 'No transaction ID found for this payment',
                ];
            }

            // Extract the required parameters for the refundCapturedPayment method
            $captureId = $payment->gateway_transaction_id;
            $invoiceId = $payment->order->tracking_number ?? 'refund-' . $payment->id;
            $refundAmount = (float) number_format($amount ?? $payment->amount, 2, '.', '');
            $noteToPayer = $reason ?? 'Refund for order #' . $payment->order_id;

            // Process the refund using the package's method with all required parameters
            $response = $this->paypalClient->refundCapturedPayment(
                $captureId,
                $invoiceId,
                $refundAmount,
                $noteToPayer
            );

            if (!isset($response['status']) || $response['status'] !== 'COMPLETED') {
                Log::error('Failed to process PayPal refund', [
                    'payment_id' => $payment->id,
                    'transaction_id' => $payment->gateway_transaction_id,
                    'response' => $response,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to process PayPal refund',
                    'error' => $response['error'] ?? 'Unknown error',
                ];
            }

            // Update payment with refund details
            $payment->update([
                'status' => 'refunded',
                'metadata' => array_merge($payment->metadata ?? [], [
                    'refund_result' => $response,
                    'refund_amount' => $amount ?? $payment->amount,
                    'refund_reason' => $reason,
                ]),
            ]);

            // Update order status if needed
            $order = $payment->order;
            if ($order) {
                $order->update(['status' => 'refunded']);
            }

            return [
                'success' => true,
                'message' => 'Payment refunded successfully',
                'payment' => $payment,
                'refund_result' => $response,
            ];
        } catch (\Exception $e) {
            Log::error('Error refunding PayPal payment', [
                'payment_id' => $payment->id,
                'transaction_id' => $payment->gateway_transaction_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while refunding the PayPal payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate a webhook payload.
     */
    public function validateWebhook(array $payload, string $signature, string $signatureHeader): bool
    {
        try {
            $webhookId = config('paypal.' . config('paypal.mode') . '.webhook_id');

            if (empty($webhookId)) {
                Log::warning('PayPal webhook ID is not configured');
                return false;
            }

            // Parse the signature header to get the required values
            // The signatureHeader is expected to be in the format:
            // PAYPAL-AUTH-ALGO,PAYPAL-CERT-URL,PAYPAL-TRANSMISSION-ID,PAYPAL-TRANSMISSION-TIME
            $headerParts = explode(',', $signatureHeader);
            if (count($headerParts) < 4) {
                Log::warning('Invalid PayPal webhook signature header format', [
                    'signature_header' => $signatureHeader,
                ]);
                return false;
            }

            // Prepare the webhook verification data exactly as in the reference implementation
            $verifyData = [
                'auth_algo' => $headerParts[0],
                'cert_url' => $headerParts[1],
                'transmission_id' => $headerParts[2],
                'transmission_sig' => $signature,
                'transmission_time' => $headerParts[3],
                'webhook_id' => $webhookId,
                'webhook_event' => $payload
            ];

            // Use the package's method to verify the webhook signature
            // The correct method name is verifyWebHook (with capital H)
            $event = $this->paypalClient->verifyWebHook($verifyData);

            $isValid = isset($event['verification_status']) && $event['verification_status'] === 'SUCCESS';

            if (!$isValid) {
                Log::warning('Invalid PayPal webhook signature', [
                    'event_type' => $payload['event_type'] ?? 'unknown',
                    'event_id' => $payload['id'] ?? null,
                    'verification_status' => $event['verification_status'] ?? 'unknown',
                ]);
            }

            return $isValid;
        } catch (\Exception $e) {
            Log::error('Error validating PayPal webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event_type' => $payload['event_type'] ?? 'unknown',
                'event_id' => $payload['id'] ?? null,
            ]);

            return false;
        }
    }

    /**
     * Process a webhook event.
     */
    public function processWebhookEvent(array $payload): array
    {
        try {
            $eventType = $payload['event_type'] ?? null;
            $resource = $payload['resource'] ?? [];
            $eventId = $payload['id'] ?? null;

            if (!$eventType) {
                return [
                    'success' => false,
                    'message' => 'Invalid webhook payload',
                ];
            }

            Log::info('Processing PayPal webhook event', [
                'event_type' => $eventType,
                'event_id' => $eventId,
            ]);

            // Check if this event has already been processed (idempotency check)
            $cacheKey = "paypal_webhook_{$eventId}";
            if ($eventId && cache()->has($cacheKey)) {
                Log::info('PayPal webhook event already processed', [
                    'event_id' => $eventId,
                    'event_type' => $eventType,
                ]);

                return [
                    'success' => true,
                    'message' => 'Event already processed',
                    'event_type' => $eventType,
                ];
            }

            // Process the event based on its type
            $result = match ($eventType) {
                'PAYMENT.CAPTURE.COMPLETED' => $this->handlePaymentCaptureCompleted($resource),
                'PAYMENT.CAPTURE.DENIED' => $this->handlePaymentCaptureDenied($resource),
                'PAYMENT.CAPTURE.REFUNDED' => $this->handlePaymentCaptureRefunded($resource),
                'PAYMENT.CAPTURE.PENDING' => $this->handlePaymentCapturePending($resource),
                'PAYMENT.CAPTURE.REVERSED' => $this->handlePaymentCaptureReversed($resource),
                'CHECKOUT.ORDER.APPROVED' => $this->handleCheckoutOrderApproved($resource),
                default => [
                    'success' => true,
                    'message' => 'Event acknowledged but not processed',
                    'event_type' => $eventType,
                ],
            };

            // Cache the event ID to prevent duplicate processing
            if ($eventId && $result['success']) {
                cache()->put($cacheKey, true, now()->addDays(7));
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error processing PayPal webhook event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event_type' => $payload['event_type'] ?? 'unknown',
                'event_id' => $payload['id'] ?? null,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing the webhook event',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle payment capture completed event.
     */
    protected function handlePaymentCaptureCompleted(array $resource): array
    {
        $captureId = $resource['id'] ?? null;
        $orderId = $resource['supplementary_data']['related_ids']['order_id'] ?? null;

        if (!$captureId || !$orderId) {
            return [
                'success' => false,
                'message' => 'Invalid capture data',
            ];
        }

        // Find the payment by PayPal order ID
        $payment = Payment::where('gateway_payment_id', $orderId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'completed',
            'gateway_transaction_id' => $captureId,
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_capture_details' => $resource,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order && $order->status === 'pending') {
            $order->update(['status' => 'processing']);
        }

        return [
            'success' => true,
            'message' => 'Payment completed',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }

    /**
     * Handle payment capture denied event.
     */
    protected function handlePaymentCaptureDenied(array $resource): array
    {
        $captureId = $resource['id'] ?? null;
        $orderId = $resource['supplementary_data']['related_ids']['order_id'] ?? null;

        if (!$captureId || !$orderId) {
            return [
                'success' => false,
                'message' => 'Invalid capture data',
            ];
        }

        // Find the payment by PayPal order ID
        $payment = Payment::where('gateway_payment_id', $orderId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'failed',
            'gateway_transaction_id' => $captureId,
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_capture_details' => $resource,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'failed']);
        }

        return [
            'success' => true,
            'message' => 'Payment denied',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }

    /**
     * Handle payment capture refunded event.
     */
    protected function handlePaymentCaptureRefunded(array $resource): array
    {
        $captureId = $resource['id'] ?? null;
        $orderId = $resource['supplementary_data']['related_ids']['order_id'] ?? null;

        if (!$captureId || !$orderId) {
            return [
                'success' => false,
                'message' => 'Invalid capture data',
            ];
        }

        // Find the payment by PayPal order ID
        $payment = Payment::where('gateway_payment_id', $orderId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'refunded',
            'gateway_transaction_id' => $captureId,
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_capture_details' => $resource,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'refunded']);
        }

        return [
            'success' => true,
            'message' => 'Payment refunded',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
        ];
    }

    /**
     * Handle payment capture pending event.
     */
    protected function handlePaymentCapturePending(array $resource): array
    {
        $captureId = $resource['id'] ?? null;
        $orderId = $resource['supplementary_data']['related_ids']['order_id'] ?? null;
        $reason = $resource['status_details']['reason'] ?? 'Unknown reason';

        if (!$captureId || !$orderId) {
            return [
                'success' => false,
                'message' => 'Invalid capture data',
            ];
        }

        // Find the payment by PayPal order ID
        $payment = Payment::where('gateway_payment_id', $orderId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'pending',
            'gateway_transaction_id' => $captureId,
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'pending_reason' => $reason,
                'paypal_capture_details' => $resource,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'pending']);
        }

        return [
            'success' => true,
            'message' => 'Payment pending',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
            'pending_reason' => $reason,
        ];
    }

    /**
     * Handle payment capture reversed event.
     */
    protected function handlePaymentCaptureReversed(array $resource): array
    {
        $captureId = $resource['id'] ?? null;
        $orderId = $resource['supplementary_data']['related_ids']['order_id'] ?? null;
        $reason = $resource['status_details']['reason'] ?? 'Unknown reason';

        if (!$captureId || !$orderId) {
            return [
                'success' => false,
                'message' => 'Invalid capture data',
            ];
        }

        // Find the payment by PayPal order ID
        $payment = Payment::where('gateway_payment_id', $orderId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment status
        $payment->update([
            'status' => 'reversed',
            'gateway_transaction_id' => $captureId,
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'reversal_reason' => $reason,
                'paypal_capture_details' => $resource,
            ]),
        ]);

        // Update order status if needed
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'cancelled']);
        }

        return [
            'success' => true,
            'message' => 'Payment reversed',
            'payment_id' => $payment->id,
            'order_id' => $order->id ?? null,
            'reversal_reason' => $reason,
        ];
    }

    /**
     * Handle checkout order approved event.
     */
    protected function handleCheckoutOrderApproved(array $resource): array
    {
        $orderId = $resource['id'] ?? null;

        if (!$orderId) {
            return [
                'success' => false,
                'message' => 'Invalid order data',
            ];
        }

        // Find the payment by PayPal order ID
        $payment = Payment::where('gateway_payment_id', $orderId)->first();

        if (!$payment) {
            return [
                'success' => false,
                'message' => 'Payment not found',
            ];
        }

        // Update payment metadata
        $payment->update([
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_order_approved' => $resource,
            ]),
        ]);

        return [
            'success' => true,
            'message' => 'Order approval acknowledged',
            'payment_id' => $payment->id,
            'order_id' => $payment->order->id ?? null,
        ];
    }
}
